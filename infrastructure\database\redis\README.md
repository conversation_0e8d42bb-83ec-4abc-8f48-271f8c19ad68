# Redis 部署说明

## 目录结构

Redis 部署目录包含以下结构：

```
infrastructure/
└── database/
    └── redis/
        ├── config/              # 配置文件目录
        │   └── redis.conf       # 统一配置文件（适用于主从节点）
        ├── data/                # 数据存储目录
        │   └── <产品名>/        # 按产品隔离
        │       ├── master/      # 主节点数据
        │       └── slave/       # 从节点数据
        ├── scripts/             # 操作脚本
        │   ├── deploy-redis.sh          # 部署脚本（支持单机和分布式）
        │   ├── service-distributed.sh   # 服务管理脚本
        │   ├── switch-master.sh         # 主从切换脚本
        │   ├── cleanup-configs.sh       # 配置清理脚本
        │   ├── uninstall-redis.sh       # 卸载脚本
        │   ├── monitor.sh               # 监控脚本
        │   ├── backup.sh                # 备份脚本
        │   └── restore.sh               # 恢复脚本
        ├── backups/             # 备份存储
        │   └── <产品名>/        # 按产品隔离
        ├── logs/                # 日志目录
        │   └── <产品名>/        # 按产品隔离
        │       ├── master/      # 主节点日志
        │       └── slave/       # 从节点日志
        ├── secrets/             # 敏感信息
        │   ├── cloudkey_redis_password.txt  # 云密钥系统Redis密码
        │   └── quicksign_redis_password.txt # 速签通系统Redis密码
        └── compose/             # Docker Compose配置
            ├── cloudkey-redis-master.yml  # 云密钥主节点编排文件
            ├── cloudkey-redis-slave.yml   # 云密钥从节点编排文件
            ├── quicksign-redis-master.yml # 速签通主节点编排文件
            └── quicksign-redis-slave.yml  # 速签通从节点编排文件
```

## 部署架构

本Redis部署采用主从复制（Master-Slave Replication）架构，具有以下特点：

1. **主从复制结构**：一个主节点和一个从节点，主节点负责读写操作，从节点主要用于读操作和数据备份。
2. **容器化部署**：使用Docker容器化部署，简化安装和维护过程。
3. **产品隔离**：不同产品使用独立的数据库实例，确保安全隔离。
4. **统一配置管理**：使用单一配置文件管理各节点，通过启动参数设置主从关系。
5. **动态主从切换**：支持在运行期间动态切换主从关系，无需重启服务。
6. **支持分布式部署**：支持将主从节点部署在不同宿主机上，提高可用性。
7. **安全密码管理**：使用Docker Secrets管理密码，避免密码泄露。

### 端口分配方案

每个产品使用预定义的端口号：

| 产品名称 | 主节点端口 | 从节点端口 | 说明 |
|---------|----------|----------|------|
| cloudkey | 16379 | 16379 | 云密钥系统 |
| eseal | 16389 | 16389 | 电子签章系统 |
| quicksign | 16399 | 16399 | 速签通系统 |

### 密码管理方案

每个产品使用独立的密码文件，通过Docker Secrets进行安全管理：

| 产品名称 | 密码文件 | 默认密码 |
|---------|---------|---------|
| cloudkey | cloudkey_redis_password.txt | CK_Redis@2006 |
| quicksign | quicksign_redis_password.txt | QS_Redis@2024 |

## 部署步骤

### 1. 环境准备

1. 安装Docker和Docker Compose
2. 确保宿主机具有足够的存储空间
3. 确保宿主机间网络互通，并开放Redis端口

### 2. 密码配置

1. 检查secrets目录下是否存在对应产品的密码文件
2. 如需修改密码，直接编辑对应的密码文件
3. 在分布式部署时，确保主从节点使用相同的密码文件

### 3. 执行部署脚本

使用统一部署脚本根据需要部署Redis服务：

```bash
# 设置脚本执行权限
cd infrastructure/database/redis/scripts
chmod +x *.sh

# 在同一台机器上同时部署主从节点（默认模式）
./deploy-redis.sh cloudkey

# 或明确指定同时部署主从节点
./deploy-redis.sh cloudkey both

# 仅部署主节点
./deploy-redis.sh cloudkey master

# 仅部署从节点（需指定主节点IP）
./deploy-redis.sh cloudkey slave *************
```

部署脚本将自动：
- 创建必要的目录结构
- 检查密码文件是否存在
- 检查并开放防火墙端口(如有需要)
- 启动Redis服务

### 4. 分布式部署说明

对于需要在不同服务器上部署主从节点的场景：

1. **在主节点服务器上部署**：
   ```bash
   ./deploy-redis.sh cloudkey master
   ```

2. **复制密码文件到从节点服务器**：
   ```bash
   # 在主节点服务器上执行
   scp infrastructure/database/redis/secrets/cloudkey_redis_password.txt user@slave-host:/path/to/infrastructure/database/redis/secrets/
   ```

3. **在从节点服务器上部署，连接到主节点**：
   ```bash
   # 使用主节点的实际IP地址
   ./deploy-redis.sh cloudkey slave *************
   ```

4. **验证部署**：
   ```bash
   # 查看服务状态
   ./service-distributed.sh cloudkey slave status
   ```

## 安全配置说明

### 密码管理

1. 使用Docker Secrets管理密码：
   - 每个产品使用独立的密码文件
   - 密码文件存放在secrets目录下
   - 文件命名格式：`<产品名>_redis_password.txt`

2. Redis密码配置：
   - 通过启动命令的`--requirepass`参数设置密码
   - 从节点通过`--masterauth`参数设置主节点认证
   - 不在配置文件或环境变量中存储密码

3. 密码文件权限：
   ```bash
   # 设置适当的文件权限
   chmod 600 infrastructure/database/redis/secrets/*_redis_password.txt
   ```

### 网络安全

1. 端口隔离：
   - 每个产品使用独立的端口
   - 避免端口冲突和混用

2. 容器网络：
   - 使用独立的bridge网络
   - 可配置自定义网络以增强隔离性

## 日常运维操作

### 动态主从切换

系统支持在运行期间不停机切换主从关系，适用于维护或故障转移场景：

```bash
# 将当前从节点提升为主节点（原主节点降级为从节点）
./switch-master.sh cloudkey slave

# 确认当前主节点为主节点，确保从节点正确复制
./switch-master.sh cloudkey master
```

这种动态切换不需要重启服务，确保业务连续性。

### 服务管理

使用service-distributed.sh脚本管理服务：

```bash
# 启动主节点服务
./service-distributed.sh cloudkey master start

# 启动从节点服务
./service-distributed.sh cloudkey slave start

# 停止主节点服务
./service-distributed.sh cloudkey master stop

# 查看从节点状态
./service-distributed.sh cloudkey slave status

# 查看主节点日志
./service-distributed.sh cloudkey master logs
```

### 监控Redis服务

监控脚本可以生成Redis服务的健康状态报告：

```bash
# 生成简单状态报告
./monitor.sh cloudkey simple

# 生成详细状态报告
./monitor.sh cloudkey detailed

# 生成完整状态报告
./monitor.sh cloudkey full
```

### 数据备份和恢复

#### 备份操作

```bash
# 执行全量备份（推荐在主节点上执行）
./backup.sh cloudkey full

# 执行快照备份(仅备份RDB文件)
./backup.sh cloudkey snapshot
```

备份文件将保存在 `backups/<产品名>/` 目录下，格式为：`redis_<产品名>_<备份类型>_<时间戳>.tar.gz`

#### 恢复操作

```bash
# 从备份文件恢复（推荐在主节点上执行）
./restore.sh cloudkey <备份文件路径>
```

执行恢复操作时会提示确认，以防止误操作导致数据丢失。

## 多产品支持

本部署方案支持多个产品同时使用，各产品之间完全隔离：

1. 不同产品使用不同的Docker Compose配置文件
2. 不同产品使用不同的数据目录和备份目录
3. 不同产品使用不同的端口避免冲突

部署新产品：

```bash
# 在同一台机器上部署新产品
./deploy-redis.sh <新产品名称>

# 分布式部署新产品的主节点
./deploy-redis.sh <新产品名称> master

# 分布式部署新产品的从节点
./deploy-redis.sh <新产品名称> slave <主节点IP>
```

## 技术支持

如遇到无法解决的问题，请联系系统管理员。

## 脚本使用指南

### deploy-redis.sh - 部署脚本

**用途**: 部署和初始化Redis服务，支持单机部署和分布式部署。

**用法**:
```bash
./deploy-redis.sh [产品名] [部署类型] [主节点IP]
```

**参数**:
- `产品名`: 要部署的产品名称，可选值：cloudkey, eseal, quicksign（默认：cloudkey）
- `部署类型`: both(同时部署主从节点)、master(仅主节点)或slave(仅从节点)（默认：both）
- `主节点IP`: 部署从节点时需指定主节点的IP地址

### uninstall-redis.sh - 卸载脚本

**用途**: 卸载Redis服务和相关数据。

**用法**:
```bash
./uninstall-redis.sh [产品名] [节点类型] [--remove-data|--keep-data]
```

**参数**:
- `产品名`: 要卸载的产品名称（默认：cloudkey）
- `节点类型`: both(主从节点)、master(仅主节点)或slave(仅从节点)（默认：both）
- `数据选项`: 
  - `--remove-data`: 删除所有数据目录和配置
  - `--keep-data`: 保留数据目录（默认）

### switch-master.sh - 主从切换脚本

**用途**: 在运行时动态切换Redis主从关系。

**用法**:
```bash
./switch-master.sh <产品名> <新主节点类型>
```

**参数**:
- `产品名`: 要操作的产品名称
- `新主节点类型`: 指定要提升为主节点的节点(master/slave)

### cleanup-configs.sh - 配置清理脚本

**用途**: 清理旧的独立主从配置文件，迁移到统一配置文件。

**用法**:
```bash
./cleanup-configs.sh
```

### service-distributed.sh - 服务管理脚本

**用途**: 提供对各节点的服务管理操作。

**用法**:
```bash
./service-distributed.sh [产品名] [节点类型] [操作]
```

**参数**:
- `产品名`: 要管理的产品名称（默认：cloudkey）
- `节点类型`: master(主节点)或slave(从节点)（默认：master）
- `操作`: 可选值：start, stop, restart, status, logs（默认：status）

### monitor.sh - 监控脚本

**用途**: 监控Redis服务的状态、性能和资源使用情况。

**用法**:
```bash
./monitor.sh [产品名] [报告类型]
```

**参数**:
- `产品名`: 要监控的产品名称（默认：cloudkey）
- `报告类型`: 可选值：simple, detailed, full（默认：simple）

### backup.sh - 备份脚本

**用途**: 执行数据库备份操作，支持全量备份和快照备份。

**用法**:
```bash
./backup.sh [产品名] [备份类型]
```

**参数**:
- `产品名`: 要备份的产品名称（默认：cloudkey）
- `备份类型`: 可选值：full, snapshot（默认：full）

### restore.sh - 恢复脚本

**用途**: 从现有备份中恢复数据库。

**用法**:
```bash
./restore.sh [产品名] <备份文件路径>
```

**参数**:
- `产品名`: 要恢复的产品名称（默认：cloudkey）
- `备份文件路径`: 备份文件的完整路径 