# Redis 统一配置文件
# 适用于主节点和从节点

# 基本配置
port 6379
daemonize no
pidfile /var/run/redis.pid
loglevel notice
logfile "/var/log/redis/redis.log"
databases 16

# 安全配置
# requirepass 由启动命令通过 --requirepass 参数设置
protected-mode yes
maxclients 10000

# 内存管理
maxmemory 1gb
maxmemory-policy volatile-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# 连接配置
tcp-backlog 511
timeout 0
tcp-keepalive 300

# 复制相关配置
# 注意：不要在这里设置replicaof，它将通过命令行参数动态设置
# 注意：不要在这里设置masterauth，它将在从节点连接主节点时动态设置
replica-serve-stale-data yes
replica-read-only yes
replica-priority 100
repl-diskless-sync yes
repl-diskless-sync-delay 5

# 性能优化
# 根据实际硬件配置调整以下参数
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
list-compress-depth 0
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64
hll-sparse-max-bytes 3000
activerehashing yes
hz 10
dynamic-hz yes
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
