---
description: 
globs: 
alwaysApply: false
---
---
description: 此规则用于管理项目的目录结构。   当涉及目录结构变更时，必须遵循这些规则。
globs: dir-structure, **/README.md
---
# 目录结构规则
description: |
  此规则用于管理项目的目录结构。
  当涉及目录结构变更时，必须遵循这些规则。

references:
  - "@dir-structure"
  - "@docs/PROJECT_RULES.md"

rules:
  - 目录结构变更必须同步更新 dir-structure 文件
  - 新增目录必须添加 README.md 说明文件
  - 目录命名必须全小写，多词使用连字符(-)分隔
  - 遵循标准的目录命名约定：
    - setup: 基础安装包
    - registry: 镜像仓库
    - infrastructure: 基础设施
    - products: 业务应用
    - operations: 运维管理 