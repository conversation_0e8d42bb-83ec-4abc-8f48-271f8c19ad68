# 操作系统基础镜像

本目录包含各种操作系统的基础镜像构建配置。

## 目录结构

- `alpine/`: Alpine Linux基础镜像
- `ubuntu/`: Ubuntu基础镜像
- `centos/`: CentOS基础镜像
- `rhel/`: Red Hat Enterprise Linux基础镜像
- `openeuler/`: openEuler基础镜像
- `shared/`: 所有操作系统共享的资源
  - `scripts/`: 共享脚本
  - `configs/`: 共享配置
- `scripts/`: 操作系统镜像专用脚本
  - `build-os.sh`: 通用镜像构建脚本
  - `customize.sh`: 镜像定制脚本

## 使用说明

各操作系统目录下包含：
1. `Dockerfile.template`: 镜像构建模板
2. `versions.json`: 支持的版本配置

### 构建示例

```bash
# 构建Ubuntu 22.04镜像
./scripts/build-os.sh ubuntu 22.04

# 构建Alpine 3.17镜像
./scripts/build-os.sh alpine 3.17

# 构建CentOS 7镜像
./scripts/build-os.sh centos 7
```

## 版本管理

各操作系统的支持版本在各自目录的`versions.json`文件中定义，包括：
- 版本号
- 系统代号
- 支持架构
- 默认安装的软件包 