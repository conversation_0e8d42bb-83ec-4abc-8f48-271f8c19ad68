#!/bin/bash
#
# Nginx网关组件公共库函数
# 提供Docker Compose检测和其他公共函数
#

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # 无颜色

# 函数: 获取Docker Compose命令
# 功能: 检测系统中可用的Docker Compose命令
# 返回: 输出可用的Docker Compose命令字符串
# 使用: DOCKER_COMPOSE_CMD=$(get_docker_compose_cmd)
get_docker_compose_cmd() {
  # 首先检查docker命令是否存在
  if ! command -v docker &> /dev/null; then
    echo -e "${RED}错误: Docker未安装或不在PATH中${NC}" >&2
    echo -e "${YELLOW}请先安装Docker并确保其在PATH中${NC}" >&2
    return 1
  fi
  
  # 然后尝试docker compose (V2版本)
  if docker compose version &> /dev/null; then
    echo "docker compose"
    return 0
  fi
  
  # 然后尝试独立的docker-compose命令
  if command -v docker-compose &> /dev/null; then
    echo "docker-compose"
    return 0
  fi
  
  # 如果都不存在，报错并返回失败
  echo -e "${RED}错误: 未找到docker compose或docker-compose命令${NC}" >&2
  echo -e "${YELLOW}请安装Docker Compose后重试${NC}" >&2
  return 1
}

# 函数: 检查Docker和Docker Compose是否已安装
# 功能: 检查系统中是否已安装Docker和Docker Compose
# 返回: 成功返回0，失败返回1
# 使用: check_docker_environment || exit 1
check_docker_environment() {
  # 检查Docker是否已安装
  if ! command -v docker &> /dev/null; then
    echo -e "${RED}错误: Docker未安装${NC}"
    echo -e "${YELLOW}请先安装Docker: https://docs.docker.com/engine/install/${NC}"
    return 1
  fi
  
  # 检查Docker是否在运行
  if ! docker info &> /dev/null; then
    echo -e "${RED}错误: Docker未在运行或当前用户无权限${NC}"
    echo -e "${YELLOW}请确保Docker服务已启动且当前用户在docker组中${NC}"
    echo -e "${YELLOW}可以尝试: sudo systemctl start docker${NC}"
    return 1
  fi
  
  # 检查Docker Compose是否可用
  if ! get_docker_compose_cmd &> /dev/null; then
    echo -e "${RED}错误: Docker Compose未安装或不可用${NC}"
    echo -e "${YELLOW}请安装Docker Compose: https://docs.docker.com/compose/install/${NC}"
    return 1
  fi
  
  # 显示版本信息
  local docker_version=$(docker --version | cut -d' ' -f3 | tr -d ',')
  local compose_cmd=$(get_docker_compose_cmd)
  local compose_version
  
  if [[ "$compose_cmd" == "docker compose" ]]; then
    compose_version=$(docker compose version --short 2>/dev/null || echo "未知")
    echo -e "${GREEN}使用Docker版本: ${docker_version}, Docker Compose V2版本: ${compose_version}${NC}"
  else
    compose_version=$(docker-compose --version | cut -d' ' -f3 | tr -d ',')
    echo -e "${GREEN}使用Docker版本: ${docker_version}, Docker Compose独立版本: ${compose_version}${NC}"
  fi
  
  return 0
}

# 函数: 检测操作系统类型
# 功能: 检测当前系统的类型和版本
# 返回: 在全局变量OS_TYPE中设置系统类型
# 使用: detect_os_type
detect_os_type() {
  # 初始化变量
  OS_TYPE="unknown"
  OS_NAME=""
  OS_VERSION=""
  
  # 检查os-release文件
  if [ -f /etc/os-release ]; then
    # 加载系统信息
    . /etc/os-release
    OS_NAME=$NAME
    OS_VERSION=$VERSION_ID
    
    # 判断系统类型
    if echo "$OS_NAME" | grep -i "openeuler" > /dev/null; then
      OS_TYPE="openeuler"
    elif echo "$OS_NAME" | grep -i "ubuntu" > /dev/null; then
      OS_TYPE="ubuntu"
    elif echo "$OS_NAME" | grep -i "centos" > /dev/null || echo "$OS_NAME" | grep -i "red hat" > /dev/null; then
      OS_TYPE="rhel"
    elif echo "$OS_NAME" | grep -i "debian" > /dev/null; then
      OS_TYPE="debian"
    fi
    
    echo -e "${BLUE}检测到操作系统: ${OS_NAME} ${OS_VERSION} (类型: ${OS_TYPE})${NC}"
  else
    echo -e "${YELLOW}警告: 无法检测操作系统类型${NC}"
  fi
}

# 函数: 在防火墙中开放指定端口
# 功能: 根据系统类型自动在防火墙中开放指定端口
# 参数: $1 - 要开放的端口号
# 使用: open_firewall_port 80
open_firewall_port() {
  local port=$1
  
  if [ -z "$port" ]; then
    echo -e "${RED}错误: 未指定端口号${NC}"
    return 1
  fi
  
  # 如果未检测系统类型，则先检测
  if [ -z "$OS_TYPE" ]; then
    detect_os_type
  fi
  
  echo -e "${YELLOW}尝试在防火墙中开放端口 ${port}...${NC}"
  
  case "$OS_TYPE" in
    "openeuler"|"rhel")
      # 使用firewalld
      if command -v firewall-cmd &> /dev/null && systemctl is-active --quiet firewalld; then
        if firewall-cmd --list-ports | grep -q "${port}/tcp"; then
          echo -e "${GREEN}端口 ${port} 已在防火墙中开放${NC}"
        else
          echo -e "${YELLOW}是否开放端口 ${port}? [y/N]${NC}"
          read -r response
          if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
            if ! sudo firewall-cmd --zone=public --add-port=${port}/tcp --permanent; then
              echo -e "${RED}开放端口失败，请检查权限${NC}"
              return 1
            fi
            if ! sudo firewall-cmd --reload; then
              echo -e "${RED}重载防火墙配置失败${NC}"
              return 1
            fi
            echo -e "${GREEN}端口已开放${NC}"
          else
            echo -e "${YELLOW}跳过端口开放，请手动配置防火墙${NC}"
          fi
        fi
      else
        echo -e "${YELLOW}firewalld未运行，跳过防火墙配置${NC}"
      fi
      ;;
      
    "ubuntu"|"debian")
      # 使用ufw
      if command -v ufw &> /dev/null && ufw status | grep -q "active"; then
        if ufw status | grep -q "${port}/tcp"; then
          echo -e "${GREEN}端口 ${port} 已在防火墙中开放${NC}"
        else
          echo -e "${YELLOW}是否开放端口 ${port}? [y/N]${NC}"
          read -r response
          if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
            if ! sudo ufw allow ${port}/tcp; then
              echo -e "${RED}开放端口失败，请检查权限${NC}"
              return 1
            fi
            echo -e "${GREEN}端口已开放${NC}"
          else
            echo -e "${YELLOW}跳过端口开放，请手动配置防火墙${NC}"
          fi
        fi
      else
        echo -e "${YELLOW}ufw未启用，跳过防火墙配置${NC}"
      fi
      ;;
      
    *)
      echo -e "${YELLOW}未能识别操作系统类型，请手动配置防火墙开放所需端口${NC}"
      ;;
  esac
  
  return 0
}

# 函数: 检查Nginx配置是否有效
# 功能: 使用docker内部nginx -t检查配置文件是否有效
# 参数: $1 - 容器名称, $2 - 配置文件路径
# 使用: check_nginx_config "nginx-container" "/etc/nginx/nginx.conf"
check_nginx_config() {
  local container_name=$1
  local config_path=${2:-"/etc/nginx/nginx.conf"}
  
  echo -e "${YELLOW}检查Nginx配置有效性...${NC}"
  
  # 检查容器是否存在
  if ! docker ps -a | grep -q "$container_name"; then
    echo -e "${RED}错误: 容器 ${container_name} 不存在${NC}"
    return 1
  fi
  
  # 检查容器是否在运行
  if ! docker ps | grep -q "$container_name"; then
    echo -e "${YELLOW}注意: 容器 ${container_name} 未运行，将启动临时容器检查配置${NC}"
    # 这里可以增加临时容器检查配置的逻辑
    return 0
  fi
  
  # 使用容器内nginx检查配置
  if docker exec "$container_name" nginx -t -c "$config_path" 2>&1 | grep -q "syntax is ok"; then
    echo -e "${GREEN}Nginx配置检查通过${NC}"
    return 0
  else
    echo -e "${RED}Nginx配置检查失败:${NC}"
    docker exec "$container_name" nginx -t -c "$config_path"
    return 1
  fi
} 