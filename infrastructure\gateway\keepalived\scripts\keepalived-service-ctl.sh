#!/bin/bash
#
# Keepalived 服务控制脚本
# 用于管理 Keepalived 服务的启动、停止、重启和状态查询
# 
# 注意：请先使用 keepalived-install.sh 脚本安装 Keepalived 服务
# 

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# 帮助信息
show_usage() {
    echo -e "${GREEN}Keepalived 服务控制脚本${NC}"
    echo "使用方法: $0 [start|stop|restart|reload|status]"
    echo "  start   - 启动 Keepalived 服务"
    echo "  stop    - 停止 Keepalived 服务"
    echo "  restart - 重启 Keepalived 服务"
    echo "  reload  - 重新加载 Keepalived 配置"
    echo "  status  - 查看 Keepalived 服务状态"
}

# 检查是否为root用户
check_user() {
    if [ "$(id -u)" -ne 0 ]; then
        echo -e "${RED}错误: 必须以root用户运行此脚本${NC}" >&2
        exit 1
    fi
}

# 服务启动
start_service() {
    echo -e "${YELLOW}启动 Keepalived 服务...${NC}"
    
    # 使用systemctl启动服务
    systemctl start keepalived
    
    # 检查启动结果
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}Keepalived 服务已启动${NC}"
    else
        echo -e "${RED}Keepalived 服务启动失败${NC}"
        exit 1
    fi
}

# 服务停止
stop_service() {
    echo -e "${YELLOW}停止 Keepalived 服务...${NC}"
    
    # 使用systemctl停止服务
    systemctl stop keepalived
    
    # 检查停止结果
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}Keepalived 服务已停止${NC}"
    else
        echo -e "${RED}Keepalived 服务停止失败${NC}"
        exit 1
    fi
}

# 服务重启
restart_service() {
    echo -e "${YELLOW}重启 Keepalived 服务...${NC}"
    
    # 使用systemctl重启服务
    systemctl restart keepalived
    
    # 检查重启结果
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}Keepalived 服务已重启${NC}"
    else
        echo -e "${RED}Keepalived 服务重启失败${NC}"
        exit 1
    fi
}

# 重新加载配置
reload_service() {
    echo -e "${YELLOW}重新加载 Keepalived 配置...${NC}"
    
    # 使用systemctl重载配置
    systemctl reload keepalived
    
    # 检查重载结果
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}Keepalived 配置已重新加载${NC}"
    else
        echo -e "${RED}Keepalived 配置重新加载失败${NC}"
        exit 1
    fi
}

# 查看服务状态
check_status() {
    echo -e "${YELLOW}Keepalived 服务状态:${NC}"
    
    # 使用systemctl查看服务状态
    systemctl status keepalived
}

# 主函数
main() {
    # 检查参数
    if [ $# -ne 1 ]; then
        show_usage
        exit 1
    fi
    
    # 检查用户权限
    check_user
    
    # 根据参数执行不同操作
    case "$1" in
        start)
            start_service
            ;;
        stop)
            stop_service
            ;;
        restart)
            restart_service
            ;;
        reload)
            reload_service
            ;;
        status)
            check_status
            ;;
        *)
            echo -e "${RED}错误: 无效的参数${NC}" >&2
            show_usage
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@" 