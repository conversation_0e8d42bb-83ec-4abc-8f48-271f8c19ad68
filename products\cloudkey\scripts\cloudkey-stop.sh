#!/bin/bash
#
# CloudKey 停止脚本
# 用于停止 CloudKey 服务
#

# 引入公共库函数
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${SCRIPT_DIR}/common_lib.sh"

# 显示帮助信息
show_help() {
  cat << EOF
用法: $(basename "$0") [选项]

CloudKey 停止脚本，用于停止 CloudKey 服务容器。

选项:
  -h, --help    显示此帮助信息并退出

功能:
  - 停止 Docker 容器服务
  - 验证服务是否成功停止
  
示例:
  $(basename "$0")             停止服务
  $(basename "$0") --help      显示帮助信息

相关脚本:
  cloudkey-start.sh     启动服务
  cloudkey-restart.sh   重启服务
  cloudkey-status.sh    查看服务状态
EOF
}

# 处理命令行参数
for arg in "$@"; do
  case $arg in
    -h|--help)
      show_help
      exit 0
      ;;
    *)
      echo -e "${RED}未知参数: $arg${NC}"
      echo -e "${YELLOW}使用 -h 或 --help 查看帮助信息${NC}"
      exit 1
      ;;
  esac
done

set -e

# 检查Docker环境
check_docker_environment || exit 1

# 获取Docker Compose命令
DOCKER_COMPOSE_CMD=$(get_docker_compose_cmd)

# 项目根目录（上级目录）
PROJECT_DIR="$(cd "${SCRIPT_DIR}/.." && pwd)"

# 停止服务
echo -e "${BLUE}正在停止 CloudKey 服务...${NC}"
cd "${PROJECT_DIR}"

# 检查服务是否存在且在运行
CONTAINER_ID=$($DOCKER_COMPOSE_CMD ps -q cloudkey 2>/dev/null)
if [ -z "${CONTAINER_ID}" ] || ! docker ps -q --no-trunc | grep -q "${CONTAINER_ID}"; then
  echo -e "${YELLOW}CloudKey 服务当前未运行。${NC}"
  exit 0
fi

# 停止服务
$DOCKER_COMPOSE_CMD stop cloudkey

# 验证服务已停止
if ! $DOCKER_COMPOSE_CMD ps | grep -q "cloudkey.*Up"; then
  echo -e "${GREEN}CloudKey 服务已成功停止。${NC}"
else
  echo -e "${RED}错误: CloudKey 服务未能成功停止，请检查日志:${NC}"
  echo -e "${YELLOW}$DOCKER_COMPOSE_CMD logs cloudkey${NC}"
  exit 1
fi 