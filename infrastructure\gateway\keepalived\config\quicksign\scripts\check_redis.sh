#!/bin/bash
# Redis健康检查脚本模板
# 由generate_config.sh自动替换变量

REDIS_HOST="127.0.0.1"
REDIS_PORT="16399"
REDIS_PASSWORD="NetcaQuickSignPwd"
REDIS_DB="0"
PRODUCT_NAME="quicksign"
PRODUCT_DISPLAY_NAME="速签通系统"

LOG_FILE="/var/log/keepalived-redis-${PRODUCT_NAME}-check.log"

# 记录日志
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> ${LOG_FILE}
}

# 检查Redis服务是否正常
check_redis() {
    local result
    
    if [ -n "${REDIS_PASSWORD}" ]; then
        result=$(redis-cli -h ${REDIS_HOST} -p ${REDIS_PORT} -a ${REDIS_PASSWORD} ping 2>&1)
    else
        result=$(redis-cli -h ${REDIS_HOST} -p ${REDIS_PORT} ping 2>&1)
    fi
    
    if [[ "$result" == "PONG" ]]; then
        log "${PRODUCT_DISPLAY_NAME} Redis服务状态正常"
        return 0
    else
        log "${PRODUCT_DISPLAY_NAME} Redis服务异常: $result"
        return 1
    fi
}

# 执行检查
check_redis
exit $?