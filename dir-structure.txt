.
├── setup/                    # 基础安装包目录（所有节点都需要）
│   ├── README.md            # 目录说明文件
│   ├── images/              # 预打包镜像（离线安装使用）
│   │   ├── README.md       # 目录说明文件
│   │   ├── infrastructure/ # 中间件镜像
│   │   │   ├── os/         # 操作系统预打包镜像
│   │   │   │   ├── README.md   # 说明文件
│   │   │   │   ├── alpine/     # Alpine预构建镜像
│   │   │   │   ├── ubuntu/     # Ubuntu预构建镜像
│   │   │   │   ├── centos/     # CentOS预构建镜像
│   │   │   │   ├── rhel/       # RHEL预构建镜像
│   │   │   │   └── openeuler/  # openEuler预构建镜像
│   │   │   └── products/       # 业务应用镜像
│   └── docker/              # Docker运行时和Compose
│       ├── README.md       # 目录说明文件
│       ├── bin/             # 静态二进制文件
│       │   ├── x86_64/      # Intel架构二进制
│       │   │   ├── docker-20.10.9.tgz                    # Docker主程序包
│       │   │   ├── containerd-1.4.12-linux-amd64.tar.gz  # Containerd运行时
│       │   │   ├── runc.amd64                            # 容器运行时
│       │   │   └── docker-compose-Linux-x86_64           # Docker Compose工具
│       │   └── aarch64/     # ARM架构二进制
│       │       ├── docker-20.10.9.tgz                    # Docker主程序包
│       │       ├── containerd-1.4.12-linux-arm64.tar.gz  # Containerd运行时
│       │       ├── runc.arm64                            # 容器运行时
│       │       └── docker-compose-Linux-aarch64          # Docker Compose工具
│       ├── systemd/         # 服务管理文件
│       │   ├── docker.service                            # Docker服务配置
│       │   └── docker.socket                             # Docker Socket配置
│       └── config/          # Docker配置目录
│           └── daemon.json                               # 守护进程配置
├── registry/                # 仓库服务器专用目录
│   ├── README.md           # 目录说明文件
│   ├── images/             # 预打包镜像仓库
│   │   ├── README.md      # 目录说明文件
│   │   ├── infrastructure/ # 中间件镜像
│   │   │   ├── registry/   # 仓库服务镜像
│   │   │   ├── mysql/      # MySQL镜像
│   │   │   ├── redis/      # Redis镜像
│   │   │   ├── jdk/        # JDK基础镜像
│   │   │   │   └── 8/      # JDK 8版本镜像 [动态生成]
│   │   │   │       └── jammy/  # 基于Ubuntu Jammy的JDK镜像 [动态生成]
│   │   │   └── tomcat/     # Tomcat镜像
│   │   │       └── 9.0/    # Tomcat 9.0版本镜像 [动态生成]
│   │   │           └── jdk8/    # 基于JDK 8环境 [动态生成]
│   │   └── products/       # 业务应用镜像
│   │       ├── cloudkey/   # 云密钥系统镜像
│   │       ├── eseal/      # 电子签章系统镜像
│   │       └── quicksign/  # 快捷签系统镜像
│   └── config/             # 仓库服务配置
│       ├── README.md      # 目录说明文件
│       ├── config.yml      # Registry配置文件
│       └── compose.yml     # Registry启动编排
├── infrastructure/         # 核心中间件层配置
│   ├── README.md          # 目录说明文件
│   ├── database/          # 数据库服务
│   │   ├── README.md     # 目录说明文件
│   │   ├── mysql/        # MySQL服务
│   │   │   ├── README.md # 目录说明文件
│   │   │   ├── config/   # MySQL配置
│   │   │   ├── scripts/  # 运维脚本
│   │   │   └── backups/  # 备份目录
│   │   └── redis/        # Redis服务
│   │       ├── README.md # 目录说明文件
│   │       ├── config/   # Redis配置
│   │       ├── data/     # 数据目录
│   │       │   ├── cloudkey/  # 云密钥Redis数据
│   │       │   │   ├── master/  # 主节点数据
│   │       │   │   └── slave/   # 从节点数据
│   │       │   ├── eseal/     # 电子签章Redis数据
│   │       │   │   ├── master/  # 主节点数据
│   │       │   │   └── slave/   # 从节点数据
│   │       │   └── quicksign/  # 快捷签Redis数据
│   │       │       ├── master/  # 主节点数据
│   │       │       └── slave/   # 从节点数据
│   │       ├── logs/     # 日志目录
│   │       │   ├── cloudkey/  # 云密钥Redis日志
│   │       │   │   ├── master/  # 主节点日志
│   │       │   │   └── slave/   # 从节点日志
│   │       │   ├── eseal/     # 电子签章Redis日志
│   │       │   │   ├── master/  # 主节点日志
│   │       │   │   └── slave/   # 从节点日志
│   │       │   └── quicksign/  # 快捷签Redis日志
│   │       │       ├── master/  # 主节点日志
│   │       │       └── slave/   # 从节点日志
│   │       ├── env/      # 环境变量配置
│   │       │   ├── cloudkey.env  # 云密钥Redis环境变量
│   │       │   ├── eseal.env     # 电子签章Redis环境变量
│   │       │   └── quicksign.env # 快捷签Redis环境变量
│   │       ├── scripts/  # 脚本目录
│   │       └── compose/  # Docker Compose配置
│   ├── gateway/          # 接入层
│   │   ├── README.md     # 目录说明文件
│   │   ├── nginx/        # Nginx服务
│   │   │   ├── README.md # 目录说明文件
│   │   │   ├── config/   # Nginx配置目录
│   │   │   │   ├── nginx.conf   # Nginx主配置文件
│   │   │   │   ├── conf.d/      # 网站配置目录
│   │   │   │   │   └── cloudkey.conf  # 云密钥系统配置
│   │   │   │   └── ssl/         # SSL证书目录
│   │   │   │       ├── nginx.cer       # SSL证书
│   │   │   │       └── nginx_cert.key  # SSL私钥
│   │   │   ├── logs/     # 日志目录（挂载点）
│   │   │   ├── scripts/  # 运维脚本
│   │   │   └── docker-compose.yml # Docker Compose编排文件
│   │   ├── openresty/    # OpenResty服务
│   │   │   ├── README.md # 目录说明文件
│   │   │   ├── config/   # OpenResty配置目录
│   │   │   ├── logs/     # 日志目录（挂载点）
│   │   │   ├── scripts/  # 运维脚本
│   │   │   └── docker-compose.yml # Docker Compose编排文件
│   │   └── keepalived/   # 高可用组件
│   │       ├── README.md # 目录说明文件
│   │       ├── README_HA.md # 高可用架构说明
│   │       ├── src/      # 源码安装目录
│   │       │   └── README.md                  # 源码获取说明
│   │       ├── rpm/      # RPM包安装目录
│   │       │   ├── README.md                  # RPM安装说明
│   │       │   ├── packages/                  # RPM包存放目录
│   │       │   │   ├── rhel/                  # RHEL/CentOS系列RPM包
│   │       │   │   │   ├── keepalived-2.2.7-1.el8.x86_64.rpm  # RHEL 8 RPM包
│   │       │   │   │   └── keepalived-2.2.7-1.el7.x86_64.rpm  # RHEL 7 RPM包
│   │       │   │   ├── openeuler/             # openEuler系列RPM包
│   │       │   │   │   └── keepalived-2.2.7-1.oe2203.x86_64.rpm # openEuler 22.03 RPM包
│   │       │   │   └── suse/                  # SUSE系列RPM包
│   │       │   │       └── keepalived-2.2.7-1.suse15.x86_64.rpm # SUSE 15 RPM包
│   │       │   └── scripts/                   # RPM安装相关脚本
│   │       │       ├── install-rpm.sh         # RPM安装脚本
│   │       │       └── setup-ha.sh            # HA环境配置脚本
│   │       ├── config/   # Keepalived配置目录
│   │       │   ├── common/                     # 共享配置
│   │       │   │   ├── env/                    # 环境变量目录
│   │       │   │   │   ├── cloudkey.env        # 云密钥环境变量
│   │       │   │   │   ├── eseal.env           # 电子签章环境变量
│   │       │   │   │   └── quicksign.env       # 快捷签环境变量
│   │       │   │   └── scripts/                # 通用脚本模板
│   │       │   │       ├── check_redis.sh.tpl  # Redis检查脚本模板
│   │       │   │       ├── check_mysql.sh.tpl  # MySQL检查脚本模板
│   │       │   │       ├── check_nginx.sh.tpl  # Nginx检查脚本模板
│   │       │   │       └── notify.sh.tpl       # 通知脚本模板
│   │       │   ├── cloudkey/                   # 云密钥配置
│   │       │   │   ├── keepalived_master.conf  # 主节点配置
│   │       │   │   ├── keepalived_backup.conf  # 备节点配置
│   │       │   │   ├── notify.sh               # 状态变更通知脚本
│   │       │   │   └── scripts/                # 健康检查脚本目录
│   │       │   │       ├── check_redis.sh      # Redis检查脚本
│   │       │   │       ├── check_mysql.sh      # MySQL检查脚本
│   │       │   │       └── check_nginx.sh      # Nginx检查脚本
│   │       │   ├── eseal/                      # 电子签章配置
│   │       │   │   ├── keepalived_master.conf  # 主节点配置
│   │       │   │   ├── keepalived_backup.conf  # 备节点配置
│   │       │   │   ├── notify.sh               # 状态变更通知脚本
│   │       │   │   └── scripts/                # 健康检查脚本目录
│   │       │   │       ├── check_redis.sh      # Redis检查脚本
│   │       │   │       ├── check_mysql.sh      # MySQL检查脚本
│   │       │   │       └── check_nginx.sh      # Nginx检查脚本
│   │       │   └── quicksign/                  # 快捷签配置
│   │       │       ├── keepalived_master.conf  # 主节点配置
│   │       │       ├── keepalived_backup.conf  # 备节点配置
│   │       │       ├── notify.sh               # 状态变更通知脚本
│   │       │       └── scripts/                # 健康检查脚本目录
│   │       │           ├── check_redis.sh      # Redis检查脚本
│   │       │           ├── check_mysql.sh      # MySQL检查脚本
│   │       │           └── check_nginx.sh      # Nginx检查脚本
│   │       └── scripts/  # 运维脚本目录
│   │           ├── install.sh                  # 源码安装脚本
│   │           ├── control.sh                  # 服务控制脚本
│   │           ├── backup.sh                   # 配置备份脚本
│   │           ├── status.sh                   # 状态检查脚本
│   │           ├── deploy.sh                   # 多产品部署脚本
│   │           └── generate_config.sh          # 配置生成脚本
│   └── registry/         # 仓库服务
│       └── README.md     # 目录说明文件
├── products/             # 业务应用层
│   ├── README.md        # 目录说明文件
│   ├── cloudkey/        # 云密钥系统
│   │   ├── README.md   # 目录说明文件
│   │   ├── config/      # 应用配置
│   │   ├── webapps/     # 应用部署目录
│   │   ├── resources/   # 资源目录
│   │   ├── tomcat/      # Tomcat挂载目录
│   │   ├── logs/        # 日志目录
│   │   └── docker-compose.yml  # CloudKey服务编排文件
│   ├── eseal/           # 电子签章系统
│   │   ├── README.md   # 目录说明文件
│   │   ├── config/      # 应用配置
│   │   └── logs/        # 日志目录
│   └── quicksign/       # 快捷签系统
│       ├── README.md   # 目录说明文件
│       ├── config/      # 应用配置
│       └── logs/        # 日志目录
├── operations/          # 运维管控层
│   ├── README.md       # 目录说明文件
│   ├── log-management/  # 日志管理
│   │   ├── README.md   # 目录说明文件
│   │   ├── rotate/     # 日志轮转
│   │   └── elk/        # 日志分析
│   ├── ha/             # 高可用配置
│   │   └── README.md   # 目录说明文件
│   └── backup/         # 备份管理
│       └── README.md   # 目录说明文件
├── docker/             # Docker镜像构建
│   ├── README.md      # 目录说明文件
│   ├── infrastructure/ # 中间件镜像
│   │   ├── README.md  # 目录说明文件
│   │   ├── os/                   # 操作系统基础镜像目录
│   │   │   ├── README.md         # 操作系统镜像说明文档
│   │   │   ├── alpine/           # Alpine Linux镜像
│   │   │   │   └── Dockerfile.template  # Alpine镜像模板
│   │   │   ├── ubuntu/           # Ubuntu镜像
│   │   │   │   └── Dockerfile.template  # Ubuntu镜像模板
│   │   │   ├── centos/           # CentOS镜像
│   │   │   │   └── Dockerfile.template  # CentOS镜像模板
│   │   │   ├── rhel/             # Red Hat Enterprise Linux镜像
│   │   │   │   └── Dockerfile.template  # RHEL镜像模板
│   │   │   ├── openeuler/        # openEuler镜像
│   │   │   │   └── Dockerfile.template  # openEuler镜像模板
│   │   │   ├── shared/           # 所有操作系统共享的资源
│   │   │   │   ├── scripts/      # 共享脚本
│   │   │   │   └── configs/      # 共享配置
│   │   │   └── scripts/          # 操作系统镜像专用脚本
│   │   │       ├── build-os.sh   # 通用镜像构建脚本
│   │   │       └── customize.sh  # 镜像定制脚本
│   │   ├── mysql/     # MySQL镜像
│   │   ├── redis/     # Redis镜像
│   │   ├── nginx/     # Nginx镜像
│   │   ├── jdk/       # JDK基础镜像
│   │   │   ├── README.md          # 目录说明文件
│   │   │   ├── Dockerfile.template  # JDK镜像模板
│   │   │   ├── shared/            # 共享资源目录
│   │   │   └── resources/         # 本地资源目录
│   │   └── tomcat/    # Tomcat镜像构建
│   │       ├── README.md        # 目录说明文件
│   │       ├── Dockerfile.template # Tomcat镜像模板
│   │       ├── shared/           # 共享资源目录
│   │       └── resources/       # 本地资源目录
│   ├── products/      # 应用镜像
│   │   ├── README.md # 目录说明文件
│   │   ├── cloudkey/  # 云密钥系统镜像
│   │   │   └── Dockerfile
│   │   ├── eseal/     # 电子签章系统镜像
│   │   │   └── Dockerfile
│   │   └── quicksign/ # 快捷签系统镜像
│   │       └── Dockerfile
│   └── scripts/       # 脚本目录
│       ├── README.md  # 脚本总体说明
│       ├── lib/       # 公共函数库
│       ├── config/     # 配置文件目录
│       │   ├── harbor.conf  # Harbor仓库配置
│       │   └── README.md    # 配置说明文件
│       └── build/      # 构建脚本
│           ├── README.md    # 构建脚本说明
│           ├── versions/    # 版本配置目录
│           │   ├── README.md          # 版本配置说明
│           │   ├── os_versions.json   # 操作系统版本配置
│           │   ├── jdk_versions.json  # JDK版本配置
│           │   └── tomcat_versions.json # Tomcat版本配置
│           ├── build-jdk.sh    # JDK镜像构建脚本
│           ├── build-os.sh     # 操作系统镜像构建脚本
│           ├── build-all-os.sh # 批量构建操作系统镜像
│           └── build-tomcat.sh # Tomcat镜像构建脚本
├── docs/               # 项目文档
│   ├── README.md           # 项目文档说明
│   ├── PROJECT_RULES.md    # 项目规则文档
│   ├── INSTALL.md          # 安装指南
│   ├── OFFLINE_SETUP.md    # 离线部署指南
│   ├── offline.md          # 离线模式说明
│   └── troubleshooting.md  # 故障排查手册

# 动态生成目录说明
# -----------------
# 标记为 [动态生成] 的目录是通过构建脚本动态创建的，不在代码仓库中直接存储。
# 这些目录主要包括：
# 
# 1. registry/images/ 下的镜像文件 - 由构建脚本生成并存储
#    - JDK镜像目录: 由build-jdk.sh生成，基于Ubuntu Jammy和本地JDK安装包
#    - Tomcat镜像目录: 由build-tomcat.sh生成
# 
# 2. 特定版本的Dockerfile目录 - 如果需要，可以通过脚本生成
#    - 设置OUTPUT_DIR环境变量执行构建脚本可保存生成的Dockerfile
#    - 例如: OUTPUT_DIR=docker/infrastructure ./docker/scripts/build/build-jdk.sh
# 
# 3. JDK安装包目录 - 存放预下载的JDK安装包，用于构建时离线安装
#    - docker/infrastructure/jdk/shared/jdk/8/jdk1.8.0_172.tar.gz
#
# 4. Keepalived源码安装包 - 存放预下载的Keepalived源码包，用于离线安装
#    - infrastructure/gateway/keepalived/src/keepalived-2.2.7.tar.gz
#
# 5. Keepalived RPM包 - 存放预下载的Keepalived RPM包，用于离线安装
#    - infrastructure/gateway/keepalived/rpm/packages/各系统目录下的RPM包
#
# 6. 操作系统镜像 - 存放预打包的各种操作系统基础镜像，用于离线部署
#    - setup/images/infrastructure/os/各系统目录下的镜像文件
