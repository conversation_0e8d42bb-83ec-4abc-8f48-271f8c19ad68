#!/bin/bash
# MySQL 日常维护脚本 - 简化维护流程
# 2025-03-02
# 版本: 1.0.0

# 脚本路径
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LIB_DIR="$SCRIPT_DIR/lib"

# 引入依赖
source "$LIB_DIR/config.sh"
source "$LIB_DIR/utils.sh"
source "$LIB_DIR/status.sh"
source "$LIB_DIR/backup_restore.sh"
source "$LIB_DIR/service.sh"
source "$LIB_DIR/performance.sh"
source "$LIB_DIR/logs.sh"
source "$LIB_DIR/menu.sh"

# 在文件开头添加 Docker Compose 命令变量
DOCKER_COMPOSE_CMD=""

# 在颜色定义后添加函数
# 确定使用哪个版本的 Docker Compose 命令
determine_docker_compose_cmd() {
    # 首先尝试 Docker Compose V2
    if docker compose version &> /dev/null; then
        DOCKER_COMPOSE_CMD="docker compose"
        return 0
    fi
    
    # 如果 V2 不可用，尝试传统版本
    if command -v docker-compose &> /dev/null; then
        DOCKER_COMPOSE_CMD="docker-compose"
        return 0
    fi
    
    # 如果都不可用
    echo -e "${RED}错误: 未找到可用的 Docker Compose 命令${NC}"
    return 1
}

# 在主要逻辑开始前调用检测函数
if ! determine_docker_compose_cmd; then
    exit 1
fi

# 检查是否请求帮助
check_help() {
  for arg in "$@"; do
    case "$arg" in
      -h|--help)
        show_help "$0"
        # 帮助函数内部已有exit，不会继续执行
        ;;
    esac
  done
}

# 主函数
main() {
  local choice
  
  # 首先检查是否有帮助参数
  check_help "$@"
  
  # 解析命令行参数
  local product=$(parse_args "$0" "$@")
  
  # 检查产品名称
  validate_product_name "$product" || exit 1
  
  # 检查Compose文件
  check_compose_files "$product" || exit 1
  
  local port=$(get_mysql_port "$product")
  log_info "MySQL维护工具 - 产品: $product (端口: $port)"
  
  while true; do
    show_menu "$product"
    read choice
    
    case $choice in
      1) check_status "$product" ;;
      2) check_replication "$product" ;;
      3) do_backup "$product" ;;
      4) do_restore "$product" ;;
      5) start_services "$product" ;;
      6) stop_services "$product" ;;
      7) restart_services "$product" ;;
      8) fix_replication "$product" ;;
      9) check_performance "$product" ;;
      10) show_logs "$product" ;;
      11) show_connection_info "$product" ;;
      0) echo "退出维护工具"; exit 0 ;;
      *) log_error "无效选择" ;;
    esac
    
    wait_for_enter
  done
}

# 检查compose文件是否存在
check_compose_files() {
  local product="$1"
  local COMPOSE_DIR="$BASE_DIR/compose"
  
  # 检查主节点1配置文件
  local master1_compose="$COMPOSE_DIR/$product-mysql-master1.yml"
  if [ ! -f "$master1_compose" ]; then
    log_error "找不到主节点1的配置文件: $master1_compose"
    log_info "请确认产品名称是否正确"
    exit 1
  fi
  
  # 检查主节点2配置文件
  local master2_compose="$COMPOSE_DIR/$product-mysql-master2.yml"
  if [ ! -f "$master2_compose" ]; then
    log_error "找不到主节点2的配置文件: $master2_compose"
    log_info "请确认产品名称是否正确"
    exit 1
  fi
  
  return 0
}

# 启动MySQL服务
start_mysql() {
  local product="$1"
  local node="${2:-all}"
  
  log_info "启动MySQL服务..."
  
  local COMPOSE_DIR="$BASE_DIR/compose"
  local master1_compose="$COMPOSE_DIR/$product-mysql-master1.yml"
  local master2_compose="$COMPOSE_DIR/$product-mysql-master2.yml"
  
  if [ "$node" == "master1" ] || [ "$node" == "all" ]; then
    log_info "启动主节点1服务..."
    (cd "$COMPOSE_DIR" && $DOCKER_COMPOSE_CMD -f "$master1_compose" up -d --remove-orphans)
    wait_for_mysql_to_start "$product-mysql-master1"
  fi
  
  if [ "$node" == "master2" ] || [ "$node" == "all" ]; then
    log_info "启动主节点2服务..."
    (cd "$COMPOSE_DIR" && $DOCKER_COMPOSE_CMD -f "$master2_compose" up -d --remove-orphans)
    wait_for_mysql_to_start "$product-mysql-master2"
  fi
  
  log_success "MySQL服务已启动"
}

# 停止MySQL服务
stop_mysql() {
  local product="$1"
  local node="${2:-all}"
  
  log_info "停止MySQL服务..."
  
  local COMPOSE_DIR="$BASE_DIR/compose"
  local master1_compose="$COMPOSE_DIR/$product-mysql-master1.yml"
  local master2_compose="$COMPOSE_DIR/$product-mysql-master2.yml"
  
  if [ "$node" == "master1" ] || [ "$node" == "all" ]; then
    log_info "停止主节点1服务..."
    (cd "$COMPOSE_DIR" && $DOCKER_COMPOSE_CMD -f "$master1_compose" down)
  fi
  
  if [ "$node" == "master2" ] || [ "$node" == "all" ]; then
    log_info "停止主节点2服务..."
    (cd "$COMPOSE_DIR" && $DOCKER_COMPOSE_CMD -f "$master2_compose" down)
  fi
  
  log_success "MySQL服务已停止"
}

# 重启MySQL服务
restart_mysql() {
  local product="$1"
  local node="${2:-all}"
  
  log_info "重启MySQL服务..."
  
  stop_mysql "$product" "$node"
  sleep 3
  start_mysql "$product" "$node"
  
  log_success "MySQL服务已重启"
}

# 检查MySQL服务状态
check_mysql_status() {
  local product="$1"
  local node="${2:-all}"
  
  log_info "检查MySQL服务状态..."
  
  local COMPOSE_DIR="$BASE_DIR/compose"
  local master1_compose="$COMPOSE_DIR/$product-mysql-master1.yml"
  local master2_compose="$COMPOSE_DIR/$product-mysql-master2.yml"
  
  if [ "$node" == "master1" ] || [ "$node" == "all" ]; then
    log_info "主节点1状态:"
    (cd "$COMPOSE_DIR" && $DOCKER_COMPOSE_CMD -f "$master1_compose" ps)
  fi
  
  if [ "$node" == "master2" ] || [ "$node" == "all" ]; then
    log_info "主节点2状态:"
    (cd "$COMPOSE_DIR" && $DOCKER_COMPOSE_CMD -f "$master2_compose" ps)
  fi
}

# 执行数据库备份
backup_mysql() {
  local product="$1"
  local node="${2:-master1}"
  
  log_info "执行MySQL数据库备份..."
  
  local COMPOSE_DIR="$BASE_DIR/compose"
  local master1_compose="$COMPOSE_DIR/$product-mysql-master1.yml"
  local master2_compose="$COMPOSE_DIR/$product-mysql-master2.yml"
  
  # 根据节点选择备份
  if [ "$node" == "master1" ]; then
    log_info "执行主节点1备份..."
    (cd "$COMPOSE_DIR" && $DOCKER_COMPOSE_CMD -f "$master1_compose" exec $product-mysql-master1 /scripts/backup.sh $product)
  elif [ "$node" == "master2" ]; then
    log_info "执行主节点2备份..."
    (cd "$COMPOSE_DIR" && $DOCKER_COMPOSE_CMD -f "$master2_compose" exec $product-mysql-master2 /scripts/backup.sh $product)
  else
    log_info "执行主节点1备份..."
    (cd "$COMPOSE_DIR" && $DOCKER_COMPOSE_CMD -f "$master1_compose" exec $product-mysql-master1 /scripts/backup.sh $product)
  fi
  
  log_success "备份已完成"
}

# 修复复制问题
fix_replication() {
  local product="$1"
  
  log_info "修复MySQL主主复制..."
  
  local COMPOSE_DIR="$BASE_DIR/compose"
  local master1_compose="$COMPOSE_DIR/$product-mysql-master1.yml"
  local master2_compose="$COMPOSE_DIR/$product-mysql-master2.yml"
  local setup_compose="$COMPOSE_DIR/$product-setup-replication.yml"
  
  # 检查复制配置文件是否存在
  if [ ! -f "$setup_compose" ]; then
    log_warning "找不到产品特定的复制配置文件: $setup_compose"
    log_info "尝试使用默认配置文件..."
    setup_compose="$COMPOSE_DIR/setup-replication.yml"
    
    if [ ! -f "$setup_compose" ]; then
      log_error "找不到复制配置文件: $setup_compose"
      return 1
    fi
  fi
  
  log_info "开始修复主主复制配置..."
  (cd "$COMPOSE_DIR" && $DOCKER_COMPOSE_CMD -f "$setup_compose" up --abort-on-container-exit)
  
  if [ $? -eq 0 ]; then
    log_success "MySQL主主复制修复成功"
  else
    log_error "MySQL主主复制修复失败，请检查日志"
    return 1
  fi
  
  return 0
}

# 执行主函数
main "$@" 