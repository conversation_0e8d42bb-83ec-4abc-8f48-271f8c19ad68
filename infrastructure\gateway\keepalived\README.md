# Keepalived 高可用组件

Keepalived 是一个用于实现高可用架构的关键组件，支持VRRP协议实现IP地址漂移，确保服务在节点失效时能够自动切换。本目录包含了Keepalived的安装配置及相关脚本。

## 目录结构

```
keepalived/
├── README.md               # 主说明文档
├── README_HA.md            # 高可用架构说明
├── src/                    # 源码安装目录
│   └── README.md           # 源码获取说明
├── rpm/                    # RPM包安装目录
│   ├── README.md           # RPM安装说明
│   └── packages/           # RPM包存放目录
├── config/                 # Keepalived配置目录
│   ├── common/             # 通用配置目录
│   │   ├── env/            # 环境变量配置
│   │   │   ├── cloudkey.env  # 云密钥系统环境变量
│   │   │   ├── eseal.env     # 电子签章系统环境变量
│   │   │   └── quicksign.env # 快捷签系统环境变量
│   │   └── scripts/        # 通用脚本模板
│   │       ├── check_redis.sh.tpl  # Redis检查脚本模板
│   │       ├── check_mysql.sh.tpl  # MySQL检查脚本模板
│   │       ├── check_nginx.sh.tpl  # Nginx检查脚本模板
│   │       └── notify.sh.tpl       # 通知脚本模板
│   ├── templates/          # 系统配置模板目录
│   │   ├── rsyslog.conf.template   # rsyslog配置模板
│   │   └── logrotate.conf.template # logrotate配置模板
│   ├── keepalived.service  # systemd服务配置文件
│   ├── cloudkey/           # 云密钥系统配置
│   │   ├── keepalived_master.conf  # 主节点配置
│   │   ├── keepalived_backup.conf  # 备节点配置
│   │   ├── notify.sh               # 状态变更通知脚本
│   │   └── scripts/                # 健康检查脚本目录
│   │       └── switch_redis_master.sh  # Redis主从切换脚本
│   ├── eseal/              # 电子签章系统配置
│   │   ├── keepalived_master.conf  # 主节点配置
│   │   ├── keepalived_backup.conf  # 备节点配置
│   │   ├── notify.sh               # 状态变更通知脚本
│   │   └── scripts/                # 健康检查脚本目录
│   │       └── switch_redis_master.sh  # Redis主从切换脚本
│   └── quicksign/          # 快捷签系统配置
│       ├── keepalived_master.conf  # 主节点配置
│       ├── keepalived_backup.conf  # 备节点配置
│       ├── notify.sh               # 状态变更通知脚本
│       └── scripts/                # 健康检查脚本目录
│           └── switch_redis_master.sh  # Redis主从切换脚本
└── scripts/                # 运维脚本目录
    ├── keepalived-install.sh         # 安装脚本
    ├── keepalived-uninstall.sh       # 卸载脚本
    ├── keepalived-service-ctl.sh     # 服务控制脚本
    ├── keepalived-config-backup.sh   # 配置备份脚本
    ├── keepalived-health-check.sh    # 状态检查脚本
    ├── keepalived-config-generator.sh # 配置生成脚本
    ├── keepalived-deploy.sh          # 多产品部署脚本
    └── keepalived-logging-setup.sh   # 日志配置脚本
```

## 安装方式

本项目支持两种安装方式，均通过统一安装脚本进行管理：

### 统一安装脚本（推荐）

统一安装脚本支持源码和RPM两种安装方式，能够自动检测环境并选择最合适的安装方式。

```bash
# 进入 keepalived 目录
cd /path/to/infrastructure/gateway/keepalived

# 设置脚本执行权限
chmod +x scripts/keepalived-install.sh

# 基本安装命令
scripts/keepalived-install.sh --auto  # 自动选择最适合的安装方式

# 指定安装方式
scripts/keepalived-install.sh --rpm   # 使用RPM包安装
scripts/keepalived-install.sh --src   # 使用源码安装

# 查看帮助信息
scripts/keepalived-install.sh --help
```

#### 安装方式

1. **源码安装** (`--src`)
   - 适用于任何Linux发行版
   - 支持定制化编译选项
   - 需要完整的编译环境
   
2. **RPM安装** (`--rpm`)
   - 适用于RHEL/CentOS、openEuler、SUSE等RPM体系发行版
   - 安装速度更快，依赖处理更简单
   - 对系统环境要求更低

#### RPM包获取

如需手动获取 openEuler 22.03 的 Keepalived RPM 包，可以通过以下链接下载：

```
# openEuler 22.03 官方源 (主程序包)
https://repo.openeuler.org/openEuler-22.03-LTS/everything/x86_64/Packages/keepalived-2.2.4-1.oe2203.x86_64.rpm

# openEuler 22.03 帮助包
https://repo.openeuler.org/openEuler-22.03-LTS/everything/noarch/Packages/keepalived-help-2.2.4-1.oe2203.noarch.rpm

# 也可以使用以下命令从官方源下载
curl -O https://repo.openeuler.org/openEuler-22.03-LTS/everything/x86_64/Packages/keepalived-2.2.4-1.oe2203.x86_64.rpm
curl -O https://repo.openeuler.org/openEuler-22.03-LTS/everything/noarch/Packages/keepalived-help-2.2.4-1.oe2203.noarch.rpm
```

下载后请放置在 `rpm/packages/openeuler/` 目录中。

## 配置与部署

### 产品配置管理

Keepalived支持多产品部署，包括以下几个产品：
- 云密钥系统（cloudkey）
- 电子签章系统（eseal）
- 快捷签系统（quicksign）

每个产品在 `config` 目录下有独立的配置，包括主备节点配置和健康检查脚本。

### 环境变量配置

在 `config/common/env` 目录下为每个产品配置环境变量：

```bash
# 示例: config/common/env/cloudkey.env
PRODUCT_NAME="cloudkey"
PRODUCT_DISPLAY_NAME="云密钥系统"
VIRTUAL_IP="*************"
VIRTUAL_INTERFACE="eth0"
VIRTUAL_ROUTER_ID="51"
REDIS_HOST="127.0.0.1"
REDIS_PORT="16379"
REDIS_PASSWORD="CK_Redis@2006"
...
```

### 配置生成

使用配置生成脚本自动生成特定产品的配置：

```bash
# 为单个产品生成配置
scripts/keepalived-config-generator.sh -p cloudkey

# 为所有产品生成配置
scripts/keepalived-config-generator.sh -a

# 查看帮助
scripts/keepalived-config-generator.sh -h
```

配置生成器会根据环境变量文件和模板生成产品特定的健康检查脚本和通知脚本。

### 部署

使用部署脚本进行安装和配置：

```bash
# 部署云密钥系统的主节点
scripts/keepalived-deploy.sh -p cloudkey -r master

# 部署所有产品的备节点
scripts/keepalived-deploy.sh -a -r backup

# 查看帮助
scripts/keepalived-deploy.sh -h
```

## 健康检查

健康检查脚本由配置生成器根据模板自动生成，位于各个产品的 `scripts` 目录下：

- `check_redis.sh`: 检查Redis服务
- `check_mysql.sh`: 检查MySQL服务
- `check_nginx.sh`: 检查Nginx服务

这些脚本会监控相关服务的运行状态，当服务不可用时触发Keepalived的故障切换。

## Redis主从切换

当Keepalived发生主备切换时，需要同步进行Redis的主从角色切换。每个产品都有一个 `switch_redis_master.sh` 脚本，在节点状态变更时自动执行：

- 当备节点切换为主节点时，将其Redis实例提升为主节点
- 当主节点恢复并降级为备节点时，将其Redis实例设置为从节点

## 日志管理

Keepalived日志默认保存在以下位置：

- 服务日志: `/var/log/keepalived/keepalived.log`
- 状态变更日志: `/var/log/keepalived/state_change.log`
- 产品特定日志: `/var/log/keepalived-<product>-*.log`

日志配置可通过 `keepalived-logging-setup.sh` 脚本进行定制。

## 故障排查

常见问题及解决方法：

1. **虚拟IP无法漂移**:
   - 检查网络接口配置是否正确
   - 确认防火墙是否允许VRRP通信(协议号112)
   - 检查健康检查脚本返回值

2. **服务无法启动**:
   - 检查配置文件语法: `keepalived -t -f /etc/keepalived/keepalived.conf`
   - 查看详细日志: `journalctl -u keepalived`

3. **多个主节点**:
   - 检查各节点配置是否一致
   - 确认网络通信是否正常

详细的排错步骤请参考 [高可用架构说明](README_HA.md)。

## 运维命令

```bash
# 启动/停止/重启服务
scripts/keepalived-service-ctl.sh start
scripts/keepalived-service-ctl.sh stop
scripts/keepalived-service-ctl.sh restart

# 查看状态
scripts/keepalived-health-check.sh

# 检查特定产品状态
scripts/keepalived-health-check.sh -p cloudkey

# 备份配置
scripts/keepalived-config-backup.sh

# 卸载服务
scripts/keepalived-uninstall.sh --purge  # 完全卸载并清理配置
scripts/keepalived-uninstall.sh --backup # 卸载前备份配置
``` 