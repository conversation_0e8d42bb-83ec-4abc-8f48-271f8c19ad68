version: '3'

services:
  nginx:
    image: **************:1443/btit/infra/openresty/openresty:********-3-jammy
    container_name: gateway-openresty
    restart: always
    network_mode: "host"
    volumes:
      - ./config/conf.d:/etc/nginx/conf.d:ro
      - ./config/ssl:/etc/nginx/ssl:ro
      - ./config/lua:/usr/local/openresty/nginx/lua:ro
      - ./logs:/var/log/nginx
    environment:
      - TZ=Asia/Shanghai
    healthcheck:
      test: ["CMD", "/usr/local/openresty/bin/openresty", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 5s
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "3"

# networks:  # 在host模式下不需要自定义网络
#   gateway_network:
#     driver: bridge 