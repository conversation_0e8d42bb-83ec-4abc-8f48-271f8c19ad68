#!/bin/bash
# Tomcat健康检查脚本
# 用于检查Tomcat进程和Web应用状态
# 如果检查失败，则退出容器，触发Docker的重启策略

# 日志文件
LOG_FILE="/usr/local/tomcat/logs/healthcheck.log"

# 记录日志
log() {
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> $LOG_FILE
}

# 检查Tomcat进程
check_process() {
  # 检查java进程是否存在且为Tomcat
  if pgrep -f "org.apache.catalina.startup.Bootstrap" > /dev/null; then
    log "Tomcat进程正常运行"
    return 0
  else
    log "Tomcat进程未运行"
    return 1
  fi
}

# 检查Web应用状态
check_webapp() {
  # 使用curl检查应用是否可访问
  if curl -s -f http://localhost:8080/cloudkeyserver/ > /dev/null; then
    log "Web应用正常访问"
    return 0
  else
    log "Web应用无法访问"
    return 1
  fi
}

# 主检查逻辑
main() {
  log "开始健康检查..."
  
  # 检查进程
  if ! check_process; then
    log "严重错误: Tomcat进程未运行，容器将退出"
    exit 1
  fi
  
  # 检查Web应用
  if ! check_webapp; then
    log "严重错误: Web应用无法访问，容器将退出"
    exit 1
  fi
  
  log "健康检查通过"
  exit 0
}

# 执行主函数
main 