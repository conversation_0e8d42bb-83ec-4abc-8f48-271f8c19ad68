version: '3.8'

services:
  cloudkey:
    image: **************:1443/btit/infra/tomcat:9.0.100-jdk8-btit-jammy-crypto-sc34
    container_name: cloudkey
    user: root
    restart: on-failure:5
    privileged: true
    cap_add:
      - SYS_ADMIN
      - IPC_LOCK
    environment:
      - TZ=Asia/Shanghai
    # 添加设备映射配置
    devices:
      - /dev/swcsm-pci30-0:/dev/swcsm-pci30-0:rwm
    volumes:
      # 配置文件挂载 - 单文件挂载方式
      - ./config/server.xml:/usr/local/tomcat/conf/server.xml:ro
      - ./config/bpms.jks:/usr/local/tomcat/conf/bpms.jks:ro
      - ./config/enc.jks:/usr/local/tomcat/conf/enc.jks:ro
      - ./config/cloudkey-config.properties:/usr/local/tomcat/conf/cloudkey-config.properties:ro
      - ./config/cloudkey-message.properties:/usr/local/tomcat/conf/cloudkey-message.properties:ro
      - ./config/cloudkey-notice.properties:/usr/local/tomcat/conf/cloudkey-notice.properties:ro
      - ./config/cloudkey-debug.properties:/usr/local/tomcat/conf/cloudkey-debug.properties:ro
      - ./config/logging.properties:/usr/local/tomcat/conf/logging.properties:ro
      # Tomcat setenv.sh 脚本挂载
      - ./tomcat/bin/setenv.sh:/usr/local/tomcat/bin/setenv.sh:ro
      # 脚本挂载
      - ./tomcat/bin/healthcheck.sh:/usr/local/tomcat/bin/healthcheck.sh:rwx
      # 修改为具体的 war 包挂载
      - ./tomcat/webapps/cloudkeyserver.war:/usr/local/tomcat/webapps/cloudkeyserver.war
      # 统一日志目录挂载
      - ./logs:/usr/local/tomcat/logs
    ports:
      - "8082:8080"
    healthcheck:
      test: ["CMD", "/usr/local/tomcat/bin/healthcheck.sh"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - cloudkey_network
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "3"

networks:
  cloudkey_network:
    # 改为内部网络，不再依赖外部网络
    name: cloudkey_network 