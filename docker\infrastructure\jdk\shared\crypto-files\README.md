# NETCA_CRYPTO 文件目录

本目录包含NETCA_CRYPTO组件的安装文件。

## 目录结构

```
.
└── NETCA_CRYPTO_linux32_64/  # NETCA_CRYPTO安装包
    ├── setup.sh              # 安装脚本
    └── ...                   # 其他相关文件
```

## 安装说明

NETCA_CRYPTO组件会在镜像构建过程中自动安装，安装过程：
1. 复制安装文件到容器的`/tmp/NETCA_CRYPTO_linux32_64/`目录
2. 执行`setup.sh`脚本进行安装
3. 安装完成后自动清理安装文件

## 依赖包

安装过程需要以下系统依赖：
- sqlite3
- libgmp10
- libldap-2.5-0
- libssl3
- libzip4
- lsb-release
- file
- libxml2

## 注意事项

1. 确保`setup.sh`具有可执行权限
2. 安装目录为`/usr/lib64`
3. 安装完成后会自动配置`LD_LIBRARY_PATH`
4. 安装文件使用后会被自动清理以减小镜像大小 