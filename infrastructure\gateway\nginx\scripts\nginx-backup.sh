#!/bin/bash
# Nginx配置备份脚本
# 用法: ./nginx-backup.sh [backup|restore] [备份ID]

# 脚本所在目录
SCRIPT_DIR=$(cd "$(dirname "$0")" && pwd)
BASE_DIR=$(dirname "$SCRIPT_DIR")
SERVICE_NAME="Nginx网关服务"
BACKUP_DIR="$BASE_DIR/backups"
CONFIG_DIR="$BASE_DIR/config"
DATE_TAG=$(date +"%Y%m%d%H%M%S")

# 显示使用帮助
show_usage() {
    echo "用法: $0 [backup|restore] [备份ID]"
    echo ""
    echo "命令:"
    echo "  backup           创建$SERVICE_NAME配置备份"
    echo "  restore <备份ID>  从指定备份恢复$SERVICE_NAME配置"
    echo "  list             列出所有可用备份"
    echo ""
    echo "示例:"
    echo "  $0 backup        # 创建新备份"
    echo "  $0 list          # 列出所有备份"
    echo "  $0 restore 20230415123456  # 从指定备份恢复"
}

# 创建目录
ensure_backup_dir() {
    mkdir -p "$BACKUP_DIR"
    if [ ! -d "$BACKUP_DIR" ]; then
        echo "无法创建备份目录: $BACKUP_DIR"
        exit 1
    fi
}

# 创建备份
create_backup() {
    ensure_backup_dir
    
    echo "正在创建$SERVICE_NAME配置备份..."
    
    # 创建备份目录
    BACKUP_PATH="$BACKUP_DIR/$DATE_TAG"
    mkdir -p "$BACKUP_PATH"
    
    # 备份配置文件
    echo "备份配置文件..."
    cp -r "$CONFIG_DIR"/* "$BACKUP_PATH/"
    
    # 备份docker-compose.yml
    echo "备份Docker Compose配置..."
    cp "$BASE_DIR/docker-compose.yml" "$BACKUP_PATH/" 2>/dev/null || true
    
    # 记录备份信息
    echo "记录备份信息..."
    {
        echo "备份ID: $DATE_TAG"
        echo "创建时间: $(date)"
        echo "备份内容:"
        find "$BACKUP_PATH" -type f | sort
    } > "$BACKUP_PATH/backup-info.txt"
    
    echo "备份完成！备份ID: $DATE_TAG"
    echo "备份路径: $BACKUP_PATH"
}

# 列出所有备份
list_backups() {
    ensure_backup_dir
    
    echo "可用的$SERVICE_NAME配置备份:"
    echo "----------------------------------------"
    
    # 检查是否有备份
    backup_count=$(find "$BACKUP_DIR" -maxdepth 1 -type d | wc -l)
    if [ "$backup_count" -le 1 ]; then
        echo "未找到备份。"
        return
    fi
    
    # 列出所有备份及其信息
    for backup in "$BACKUP_DIR"/*; do
        if [ -d "$backup" ] && [ -f "$backup/backup-info.txt" ]; then
            backup_id=$(basename "$backup")
            backup_time=$(grep "创建时间:" "$backup/backup-info.txt" | cut -d ":" -f 2- | sed 's/^[ \t]*//')
            echo "备份ID: $backup_id"
            echo "创建时间: $backup_time"
            echo "----------------------------------------"
        fi
    done
}

# 恢复备份
restore_backup() {
    if [ -z "$1" ]; then
        echo "错误: 未指定备份ID"
        show_usage
        exit 1
    fi
    
    BACKUP_ID="$1"
    BACKUP_PATH="$BACKUP_DIR/$BACKUP_ID"
    
    if [ ! -d "$BACKUP_PATH" ]; then
        echo "错误: 未找到备份ID为 $BACKUP_ID 的备份"
        exit 1
    fi
    
    echo "正在从备份 $BACKUP_ID 恢复$SERVICE_NAME配置..."
    
    # 先创建当前配置的备份
    echo "创建当前配置的备份..."
    TEMP_BACKUP_ID="${DATE_TAG}_before_restore"
    TEMP_BACKUP_PATH="$BACKUP_DIR/$TEMP_BACKUP_ID"
    mkdir -p "$TEMP_BACKUP_PATH"
    cp -r "$CONFIG_DIR"/* "$TEMP_BACKUP_PATH/" 2>/dev/null || true
    cp "$BASE_DIR/docker-compose.yml" "$TEMP_BACKUP_PATH/" 2>/dev/null || true
    
    # 恢复配置
    echo "正在恢复配置文件..."
    cp -r "$BACKUP_PATH"/* "$BASE_DIR/" 2>/dev/null || true
    
    # 移动docker-compose.yml到正确位置
    if [ -f "$CONFIG_DIR/docker-compose.yml" ]; then
        mv "$CONFIG_DIR/docker-compose.yml" "$BASE_DIR/"
    fi
    
    echo "配置恢复完成！"
    echo "如需重启服务以应用新配置，请运行: ./nginx-control.sh restart"
}

# 主函数
main() {
    # 检查参数
    if [ $# -lt 1 ]; then
        show_usage
        exit 1
    fi
    
    # 根据参数执行相应的操作
    case "$1" in
        backup)
            create_backup
            ;;
        list)
            list_backups
            ;;
        restore)
            restore_backup "$2"
            ;;
        *)
            show_usage
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@" 