#!/bin/bash
# Keepalived多产品部署脚本
# 支持一键部署多产品的Keepalived高可用环境

set -e

# 脚本配置
SCRIPT_DIR=$(dirname "$(readlink -f "$0")")
# 修正BASE_DIR的计算，确保不会出现重复的gateway目录
BASE_DIR=$(realpath "${SCRIPT_DIR}/..")
CONFIG_DIR="${BASE_DIR}/config"
COMMON_DIR="${CONFIG_DIR}/common"
ENV_DIR="${COMMON_DIR}/env"
GENERATE_CONFIG="${SCRIPT_DIR}/keepalived-config-generator.sh"
INSTALL_SCRIPT="${SCRIPT_DIR}/keepalived-install.sh"
SYSTEM_CONFIG_DIR="/etc/keepalived"
SYSTEM_SCRIPTS_DIR="${SYSTEM_CONFIG_DIR}/scripts"

# 日志函数
log_info() { echo -e "\033[0;32m[INFO] $1\033[0m"; }
log_warn() { echo -e "\033[0;33m[WARN] $1\033[0m"; }
log_error() { echo -e "\033[0;31m[ERROR] $1\033[0m" >&2; }

# 检查并设置脚本权限
ensure_script_permissions() {
    local script_file="$1"
    
    if [ ! -f "$script_file" ]; then
        log_error "脚本文件不存在: $script_file"
        return 1
    fi
    
    # 检查权限
    if [ ! -x "$script_file" ]; then
        log_info "设置脚本执行权限: $script_file"
        chmod +x "$script_file" || {
            log_error "无法设置脚本权限: $script_file"
            return 1
        }
    fi
    
    return 0
}

# 确保目录存在
ensure_directory() {
    local dir="$1"
    if [ ! -d "$dir" ]; then
        log_info "创建目录: $dir"
        mkdir -p "$dir" || {
            log_error "无法创建目录: $dir"
            return 1
        }
    fi
    return 0
}

# 显示帮助信息
show_help() {
    cat << EOF
Keepalived多产品部署脚本
用法: $0 [选项]

选项:
  -p, --product PRODUCT   指定产品 (cloudkey, eseal, quicksign)
  -a, --all               部署所有产品
  -r, --role ROLE         指定节点角色 (master, backup)
  -h, --help              显示帮助信息

示例:
  $0 -p cloudkey -r master    部署云密钥系统主节点
  $0 -a -r backup             部署所有产品的备节点
EOF
}

# 检查Keepalived是否已安装
check_keepalived() {
    # 确保安装脚本可执行
    ensure_script_permissions "${INSTALL_SCRIPT}" || {
        log_error "无法设置安装脚本权限"
        exit 1
    }
    
    if ! command -v keepalived &> /dev/null; then
        log_info "Keepalived未安装，正在安装..."
        ${INSTALL_SCRIPT}
    else
        log_info "Keepalived已安装，版本: $(keepalived --version 2>&1 | head -n 1)"
    fi
}

# 部署单个产品配置
deploy_product() {
    local product="$1"
    local role="$2"
    local env_file="${ENV_DIR}/${product}.env"
    local product_dir="${CONFIG_DIR}/${product}"
    local scripts_dir="${product_dir}/scripts"
    local config_file="${product_dir}/keepalived_${role}.conf"
    local notify_script="${product_dir}/notify.sh"
    local product_scripts_dir="${SYSTEM_SCRIPTS_DIR}/${product}"
    
    log_info "正在部署 ${product} ${role} 节点配置..."
    
    # 验证必要文件
    if [ ! -f "${env_file}" ]; then
        log_error "找不到 ${product} 的环境变量配置文件: ${env_file}"
        return 1
    fi
    
    if [ ! -f "${config_file}" ]; then
        log_error "找不到 ${product} 的 ${role} 配置文件: ${config_file}"
        return 1
    fi
    
    # 确保生成配置脚本可执行
    ensure_script_permissions "${GENERATE_CONFIG}" || {
        log_error "无法设置配置生成脚本权限"
        return 1
    }
    
    # 生成产品特定配置
    log_info "生成 ${product} 特定配置..."
    ${GENERATE_CONFIG} -p ${product} || {
        log_error "生成产品配置失败"
        return 1
    }
    
    # 创建系统目录
    ensure_directory "${SYSTEM_CONFIG_DIR}" || return 1
    ensure_directory "${product_scripts_dir}" || return 1
    
    # 部署配置
    log_info "部署 ${product} ${role} 配置文件..."
    cp "${config_file}" "${SYSTEM_CONFIG_DIR}/keepalived.conf" || {
        log_error "复制配置文件失败"
        return 1
    }
    
    # 将notify.sh也复制到scripts目录下，统一管理
    cp "${notify_script}" "${product_scripts_dir}/" || {
        log_error "复制通知脚本失败"
        return 1
    }
    
    # 检查源目录存在并有内容
    if [ ! -d "${scripts_dir}" ] || [ -z "$(ls -A ${scripts_dir} 2>/dev/null)" ]; then
        log_warn "产品脚本目录为空，这可能是正常的，如果脚本是由生成器创建"
    else
        cp -r "${scripts_dir}"/* "${product_scripts_dir}/" || {
            log_error "复制产品脚本失败"
            return 1
        }
    fi
    
    # 设置权限
    log_info "设置脚本执行权限..."
    chmod +x "${product_scripts_dir}/notify.sh" || log_warn "无法设置通知脚本权限"
    
    # 如果有脚本文件，则设置权限
    if ls "${product_scripts_dir}"/*.sh >/dev/null 2>&1; then
        chmod +x "${product_scripts_dir}"/*.sh || log_warn "无法设置产品脚本权限"
    fi
    
    # 验证配置
    log_info "验证 ${product} ${role} 配置..."
    if ! keepalived -t -f "${SYSTEM_CONFIG_DIR}/keepalived.conf"; then
        log_error "配置验证失败，请检查配置文件"
        return 1
    fi
    
    # 重启服务
    log_info "重启 Keepalived 服务..."
    systemctl restart keepalived || {
        log_error "重启Keepalived服务失败"
        return 1
    }
    
    log_info "${product} ${role} 节点部署完成!"
}

# 设置所有脚本权限
setup_script_permissions() {
    log_info "设置所有相关脚本的执行权限..."
    
    # 设置安装脚本权限
    find "${SCRIPT_DIR}" -name "*.sh" -type f | while read script; do
        ensure_script_permissions "$script" || log_warn "无法设置脚本权限: $script"
    done
    
    # 设置通知脚本权限
    for product in cloudkey eseal quicksign; do
        local notify_script="${CONFIG_DIR}/${product}/notify.sh"
        if [ -f "$notify_script" ]; then
            ensure_script_permissions "$notify_script" || log_warn "无法设置脚本权限: $notify_script"
        fi
        
        # 设置产品脚本权限
        local scripts_dir="${CONFIG_DIR}/${product}/scripts"
        if [ -d "$scripts_dir" ]; then
            find "$scripts_dir" -name "*.sh" -type f | while read script; do
                ensure_script_permissions "$script" || log_warn "无法设置脚本权限: $script"
            done
        fi
    done
    
    # 设置模板目录权限
    find "${COMMON_DIR}/scripts" -name "*.sh.tpl" -type f | while read template; do
        chmod +r "$template" || log_warn "无法设置模板读取权限: $template"
    done
}

# 主函数
main() {
    local deploy_all=false
    local product=""
    local role=""
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case "$1" in
            -p|--product)
                product="$2"
                shift 2
                ;;
            -a|--all)
                deploy_all=true
                shift
                ;;
            -r|--role)
                role="$2"
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知选项 $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 验证参数
    if [ "$deploy_all" = false ] && [ -z "$product" ]; then
        log_error "请指定要部署的产品或使用 -a 选项部署所有产品"
        show_help
        exit 1
    fi
    
    if [ -z "$role" ]; then
        log_error "请指定节点角色 (master 或 backup)"
        show_help
        exit 1
    fi
    
    if [ "$role" != "master" ] && [ "$role" != "backup" ]; then
        log_error "无效的角色 '$role'，必须是 'master' 或 'backup'"
        show_help
        exit 1
    fi
    
    # 设置所有脚本权限
    setup_script_permissions
    
    # 确保Keepalived已安装
    check_keepalived
    
    # 部署配置
    if [ "$deploy_all" = true ]; then
        log_info "开始部署所有产品的 ${role} 节点..."
        
        for env_file in "${ENV_DIR}"/*.env; do
            if [ -f "${env_file}" ]; then
                product_name=$(basename "${env_file}" .env)
                deploy_product "${product_name}" "${role}" || {
                    log_error "部署产品 ${product_name} 失败"
                    continue
                }
            fi
        done
    else
        log_info "开始部署 ${product} 的 ${role} 节点..."
        deploy_product "${product}" "${role}" || {
            log_error "部署产品 ${product} 失败"
            exit 1
        }
    fi
    
    # 检查部署状态
    log_info "部署完成，检查服务状态..."
    systemctl status keepalived --no-pager || log_warn "无法获取服务状态"
    
    log_info "可以使用 keepalived-health-check.sh 脚本查看详细状态"
}

# 执行主函数
main "$@" 