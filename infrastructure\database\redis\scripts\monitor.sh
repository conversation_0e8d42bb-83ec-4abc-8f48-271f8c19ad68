#!/bin/bash
#
# Redis 监控脚本
# 用于监控Redis主从架构的状态和性能
#

# 设置错误时退出
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # 无颜色

# 脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
BASE_DIR="$(dirname "$SCRIPT_DIR")"

# 默认产品名称
PRODUCT_NAME=${1:-"cloudkey"}
REPORT_TYPE=${2:-"simple"}

# 检查参数
if [[ "$PRODUCT_NAME" == "--help" || "$PRODUCT_NAME" == "-h" ]]; then
  echo "用法: $0 [产品名称] [报告类型]"
  echo "产品名称: cloudkey, eseal, quicksign (默认: cloudkey)"
  echo "报告类型:"
  echo "  simple   - 简单报告，包括基本状态信息 (默认)"
  echo "  detailed - 详细报告，包括内存使用、性能指标等"
  echo "  full     - 完整报告，包括所有可用信息"
  exit 0
fi

# 检查compose目录和配置文件
COMPOSE_DIR="$BASE_DIR/compose"
COMPOSE_MASTER_FILE="$COMPOSE_DIR/$PRODUCT_NAME-redis-master.yml"
COMPOSE_SLAVE_FILE="$COMPOSE_DIR/$PRODUCT_NAME-redis-slave.yml"

if [ ! -f "$COMPOSE_MASTER_FILE" ]; then
  echo -e "${RED}错误: 找不到主节点compose文件 $COMPOSE_MASTER_FILE${NC}"
  echo -e "${YELLOW}请先运行部署脚本: ${GREEN}$SCRIPT_DIR/deploy-redis.sh $PRODUCT_NAME${NC}"
  exit 1
fi

# 如果密码文件存在，设置密码环境变量
if [ -f "$BASE_DIR/secrets/redis_password.txt" ]; then
  export REDIS_PASSWORD=$(cat "$BASE_DIR/secrets/redis_password.txt")
else
  echo -e "${RED}错误: 找不到密码文件 $BASE_DIR/secrets/redis_password.txt${NC}"
  exit 1
fi

# 获取当前时间
CURRENT_TIME=$(date "+%Y-%m-%d %H:%M:%S")
REPORT_FILE="$BASE_DIR/logs/$PRODUCT_NAME/health_$(date "+%Y%m%d_%H%M%S").log"

# 输出到屏幕和文件
log() {
  echo -e "$1" | tee -a "$REPORT_FILE"
}

# 函数: 生成监控报告头部
generate_report_header() {
  log "${BLUE}====================================${NC}"
  log "${BLUE}     Redis 健康状态监控报告        ${NC}"
  log "${BLUE}====================================${NC}"
  log "${YELLOW}产品名称: ${GREEN}$PRODUCT_NAME${NC}"
  log "${YELLOW}报告时间: ${GREEN}$CURRENT_TIME${NC}"
  log "${YELLOW}报告类型: ${GREEN}$REPORT_TYPE${NC}"
  log "${BLUE}====================================${NC}"
  log ""
}

# 函数: 检查容器状态
check_container_status() {
  log "${BLUE}[1] 容器状态检查${NC}"
  
  # 检查主节点状态
  log "${YELLOW}主节点状态:${NC}"
  if docker ps | grep -q "$PRODUCT_NAME-redis-master"; then
    log "${GREEN}  运行中${NC}"
    MASTER_ID=$(docker ps -q -f name="$PRODUCT_NAME-redis-master")
    MASTER_INFO=$(docker inspect "$MASTER_ID" | grep -E '"Status"|"StartedAt"|"Id"' | sed 's/^[ \t]*/  /')
    log "$MASTER_INFO"
  else
    log "${RED}  未运行${NC}"
  fi
  
  # 检查从节点状态
  log "${YELLOW}从节点状态:${NC}"
  if docker ps | grep -q "$PRODUCT_NAME-redis-slave"; then
    log "${GREEN}  运行中${NC}"
    SLAVE_ID=$(docker ps -q -f name="$PRODUCT_NAME-redis-slave")
    SLAVE_INFO=$(docker inspect "$SLAVE_ID" | grep -E '"Status"|"StartedAt"|"Id"' | sed 's/^[ \t]*/  /')
    log "$SLAVE_INFO"
  else
    log "${RED}  未运行${NC}"
  fi
  
  log ""
}

# 函数: 检查Redis服务状态
check_redis_status() {
  log "${BLUE}[2] Redis服务状态检查${NC}"
  
  # 检查主节点Redis服务
  log "${YELLOW}主节点Redis服务:${NC}"
  if docker ps | grep -q "$PRODUCT_NAME-redis-master"; then
    # 尝试ping主节点
    if docker exec -i "$PRODUCT_NAME-redis-master" redis-cli -a "$REDIS_PASSWORD" ping | grep -q "PONG"; then
      log "${GREEN}  服务正常${NC}"
    else
      log "${RED}  服务异常${NC}"
    fi
  else
    log "${RED}  容器未运行${NC}"
  fi
  
  # 检查从节点Redis服务
  log "${YELLOW}从节点Redis服务:${NC}"
  if docker ps | grep -q "$PRODUCT_NAME-redis-slave"; then
    # 尝试ping从节点
    if docker exec -i "$PRODUCT_NAME-redis-slave" redis-cli -a "$REDIS_PASSWORD" ping | grep -q "PONG"; then
      log "${GREEN}  服务正常${NC}"
    else
      log "${RED}  服务异常${NC}"
    fi
  else
    log "${RED}  容器未运行${NC}"
  fi
  
  log ""
}

# 函数: 检查主从复制状态
check_replication_status() {
  log "${BLUE}[3] 主从复制状态检查${NC}"
  
  # 检查主节点复制信息
  log "${YELLOW}主节点复制信息:${NC}"
  if docker ps | grep -q "$PRODUCT_NAME-redis-master"; then
    MASTER_REPL_INFO=$(docker exec -i "$PRODUCT_NAME-redis-master" redis-cli -a "$REDIS_PASSWORD" info replication | grep -v "^#")
    log "$(echo "$MASTER_REPL_INFO" | sed 's/^/  /')"
  else
    log "${RED}  主节点容器未运行${NC}"
  fi
  
  # 检查从节点复制信息
  log "${YELLOW}从节点复制信息:${NC}"
  if docker ps | grep -q "$PRODUCT_NAME-redis-slave"; then
    SLAVE_REPL_INFO=$(docker exec -i "$PRODUCT_NAME-redis-slave" redis-cli -a "$REDIS_PASSWORD" info replication | grep -v "^#")
    
    # 检查复制状态
    if echo "$SLAVE_REPL_INFO" | grep -q "master_link_status:up"; then
      log "${GREEN}  主从复制正常${NC}"
    else
      log "${RED}  主从复制异常${NC}"
    fi
    
    log "$(echo "$SLAVE_REPL_INFO" | sed 's/^/  /')"
  else
    log "${RED}  从节点容器未运行${NC}"
  fi
  
  log ""
}

# 函数: 检查资源使用情况
check_resource_usage() {
  log "${BLUE}[4] 资源使用情况${NC}"
  
  # 获取资源使用情况
  log "${YELLOW}容器资源使用:${NC}"
  
  # 主节点资源使用
  log "${YELLOW}主节点资源:${NC}"
  if docker ps | grep -q "$PRODUCT_NAME-redis-master"; then
    MASTER_STATS=$(docker stats --no-stream "$PRODUCT_NAME-redis-master" | sed -n '1p;2p')
    log "$(echo "$MASTER_STATS" | sed 's/^/  /')"
  else
    log "${RED}  主节点容器未运行${NC}"
  fi
  
  # 从节点资源使用
  log "${YELLOW}从节点资源:${NC}"
  if docker ps | grep -q "$PRODUCT_NAME-redis-slave"; then
    SLAVE_STATS=$(docker stats --no-stream "$PRODUCT_NAME-redis-slave" | sed -n '1p;2p')
    log "$(echo "$SLAVE_STATS" | sed 's/^/  /')"
  else
    log "${RED}  从节点容器未运行${NC}"
  fi
  
  log ""
}

# 函数: 检查Redis内存使用情况
check_memory_usage() {
  if [[ "$REPORT_TYPE" == "simple" ]]; then
    return
  fi
  
  log "${BLUE}[5] Redis内存使用情况${NC}"
  
  # 主节点内存使用
  log "${YELLOW}主节点内存使用:${NC}"
  if docker ps | grep -q "$PRODUCT_NAME-redis-master"; then
    MASTER_MEM_INFO=$(docker exec -i "$PRODUCT_NAME-redis-master" redis-cli -a "$REDIS_PASSWORD" info memory | grep -v "^#")
    log "$(echo "$MASTER_MEM_INFO" | grep -E "used_memory|used_memory_human|used_memory_peak|used_memory_peak_human|mem_fragmentation_ratio" | sed 's/^/  /')"
  else
    log "${RED}  主节点容器未运行${NC}"
  fi
  
  # 从节点内存使用
  log "${YELLOW}从节点内存使用:${NC}"
  if docker ps | grep -q "$PRODUCT_NAME-redis-slave"; then
    SLAVE_MEM_INFO=$(docker exec -i "$PRODUCT_NAME-redis-slave" redis-cli -a "$REDIS_PASSWORD" info memory | grep -v "^#")
    log "$(echo "$SLAVE_MEM_INFO" | grep -E "used_memory|used_memory_human|used_memory_peak|used_memory_peak_human|mem_fragmentation_ratio" | sed 's/^/  /')"
  else
    log "${RED}  从节点容器未运行${NC}"
  fi
  
  log ""
}

# 函数: 检查Redis性能指标
check_performance_metrics() {
  if [[ "$REPORT_TYPE" == "simple" ]]; then
    return
  fi
  
  log "${BLUE}[6] Redis性能指标${NC}"
  
  # 主节点性能指标
  log "${YELLOW}主节点性能指标:${NC}"
  if docker ps | grep -q "$PRODUCT_NAME-redis-master"; then
    MASTER_STATS_INFO=$(docker exec -i "$PRODUCT_NAME-redis-master" redis-cli -a "$REDIS_PASSWORD" info stats | grep -v "^#")
    log "$(echo "$MASTER_STATS_INFO" | grep -E "total_connections_received|total_commands_processed|instantaneous_ops_per_sec|instantaneous_input_kbps|instantaneous_output_kbps|rejected_connections|keyspace_hits|keyspace_misses" | sed 's/^/  /')"
  else
    log "${RED}  主节点容器未运行${NC}"
  fi
  
  # 从节点性能指标
  log "${YELLOW}从节点性能指标:${NC}"
  if docker ps | grep -q "$PRODUCT_NAME-redis-slave"; then
    SLAVE_STATS_INFO=$(docker exec -i "$PRODUCT_NAME-redis-slave" redis-cli -a "$REDIS_PASSWORD" info stats | grep -v "^#")
    log "$(echo "$SLAVE_STATS_INFO" | grep -E "total_connections_received|total_commands_processed|instantaneous_ops_per_sec|instantaneous_input_kbps|instantaneous_output_kbps|rejected_connections|keyspace_hits|keyspace_misses" | sed 's/^/  /')"
  else
    log "${RED}  从节点容器未运行${NC}"
  fi
  
  log ""
}

# 函数: 检查数据库信息
check_keyspace_info() {
  if [[ "$REPORT_TYPE" == "simple" ]]; then
    return
  fi
  
  log "${BLUE}[7] 数据库信息${NC}"
  
  # 主节点数据库信息
  log "${YELLOW}主节点数据库信息:${NC}"
  if docker ps | grep -q "$PRODUCT_NAME-redis-master"; then
    MASTER_KEYSPACE_INFO=$(docker exec -i "$PRODUCT_NAME-redis-master" redis-cli -a "$REDIS_PASSWORD" info keyspace | grep -v "^#")
    if [[ -z "$MASTER_KEYSPACE_INFO" ]]; then
      log "  没有数据库信息"
    else
      log "$(echo "$MASTER_KEYSPACE_INFO" | sed 's/^/  /')"
    fi
  else
    log "${RED}  主节点容器未运行${NC}"
  fi
  
  # 从节点数据库信息
  log "${YELLOW}从节点数据库信息:${NC}"
  if docker ps | grep -q "$PRODUCT_NAME-redis-slave"; then
    SLAVE_KEYSPACE_INFO=$(docker exec -i "$PRODUCT_NAME-redis-slave" redis-cli -a "$REDIS_PASSWORD" info keyspace | grep -v "^#")
    if [[ -z "$SLAVE_KEYSPACE_INFO" ]]; then
      log "  没有数据库信息"
    else
      log "$(echo "$SLAVE_KEYSPACE_INFO" | sed 's/^/  /')"
    fi
  else
    log "${RED}  从节点容器未运行${NC}"
  fi
  
  log ""
}

# 函数: 生成完整报告
generate_full_report() {
  if [[ "$REPORT_TYPE" != "full" ]]; then
    return
  fi
  
  log "${BLUE}[8] 完整Redis信息${NC}"
  
  # 主节点完整信息
  log "${YELLOW}主节点完整信息:${NC}"
  if docker ps | grep -q "$PRODUCT_NAME-redis-master"; then
    log "  详细信息已保存到: $BASE_DIR/logs/$PRODUCT_NAME/master_info_$(date "+%Y%m%d_%H%M%S").log"
    docker exec -i "$PRODUCT_NAME-redis-master" redis-cli -a "$REDIS_PASSWORD" info all > "$BASE_DIR/logs/$PRODUCT_NAME/master_info_$(date "+%Y%m%d_%H%M%S").log"
  else
    log "${RED}  主节点容器未运行${NC}"
  fi
  
  # 从节点完整信息
  log "${YELLOW}从节点完整信息:${NC}"
  if docker ps | grep -q "$PRODUCT_NAME-redis-slave"; then
    log "  详细信息已保存到: $BASE_DIR/logs/$PRODUCT_NAME/slave_info_$(date "+%Y%m%d_%H%M%S").log"
    docker exec -i "$PRODUCT_NAME-redis-slave" redis-cli -a "$REDIS_PASSWORD" info all > "$BASE_DIR/logs/$PRODUCT_NAME/slave_info_$(date "+%Y%m%d_%H%M%S").log"
  else
    log "${RED}  从节点容器未运行${NC}"
  fi
  
  log ""
}

# 函数: 生成健康状况总结
generate_health_summary() {
  log "${BLUE}[最终] 健康状况总结${NC}"
  
  # 检查主节点健康状态
  log "${YELLOW}主节点健康状态:${NC}"
  if docker ps | grep -q "$PRODUCT_NAME-redis-master"; then
    if docker exec -i "$PRODUCT_NAME-redis-master" redis-cli -a "$REDIS_PASSWORD" ping | grep -q "PONG"; then
      log "${GREEN}  状态: 健康${NC}"
    else
      log "${RED}  状态: 异常${NC}"
    fi
  else
    log "${RED}  状态: 未运行${NC}"
  fi
  
  # 检查从节点健康状态
  log "${YELLOW}从节点健康状态:${NC}"
  if docker ps | grep -q "$PRODUCT_NAME-redis-slave"; then
    if docker exec -i "$PRODUCT_NAME-redis-slave" redis-cli -a "$REDIS_PASSWORD" ping | grep -q "PONG"; then
      # 还需检查复制状态
      SLAVE_REPL_INFO=$(docker exec -i "$PRODUCT_NAME-redis-slave" redis-cli -a "$REDIS_PASSWORD" info replication)
      if echo "$SLAVE_REPL_INFO" | grep -q "master_link_status:up"; then
        log "${GREEN}  状态: 健康${NC}"
      else
        log "${YELLOW}  状态: 运行中但复制异常${NC}"
      fi
    else
      log "${RED}  状态: 异常${NC}"
    fi
  else
    log "${RED}  状态: 未运行${NC}"
  fi
  
  # 总体健康状态
  log "${YELLOW}总体健康状态:${NC}"
  if docker ps | grep -q "$PRODUCT_NAME-redis-master" && \
     docker ps | grep -q "$PRODUCT_NAME-redis-slave" && \
     docker exec -i "$PRODUCT_NAME-redis-master" redis-cli -a "$REDIS_PASSWORD" ping | grep -q "PONG" && \
     docker exec -i "$PRODUCT_NAME-redis-slave" redis-cli -a "$REDIS_PASSWORD" ping | grep -q "PONG" && \
     docker exec -i "$PRODUCT_NAME-redis-slave" redis-cli -a "$REDIS_PASSWORD" info replication | grep -q "master_link_status:up"; then
    log "${GREEN}  状态: 健康${NC}"
  elif docker ps | grep -q "$PRODUCT_NAME-redis-master" && \
       docker exec -i "$PRODUCT_NAME-redis-master" redis-cli -a "$REDIS_PASSWORD" ping | grep -q "PONG"; then
    log "${YELLOW}  状态: 部分可用(主节点正常)${NC}"
  else
    log "${RED}  状态: 不可用${NC}"
  fi
  
  log ""
  log "${BLUE}====================================${NC}"
  log "${YELLOW}报告已保存到: ${GREEN}$REPORT_FILE${NC}"
  log "${BLUE}====================================${NC}"
}

# 主函数
main() {
  # 创建日志目录
  mkdir -p "$BASE_DIR/logs/$PRODUCT_NAME"
  
  # 生成报告
  generate_report_header
  check_container_status
  check_redis_status
  check_replication_status
  check_resource_usage
  check_memory_usage
  check_performance_metrics
  check_keyspace_info
  generate_full_report
  generate_health_summary
}

# 执行主函数
main 