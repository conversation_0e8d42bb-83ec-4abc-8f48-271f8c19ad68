#!/bin/bash
# MySQL 服务管理功能
# 包含服务的启动、停止和重启功能

# 引入依赖
source "$(dirname "${BASH_SOURCE[0]}")/utils.sh"

# 服务选择菜单
select_service() {
  local product="$1"
  local master1=$(get_master1_name "$product")
  local master2=$(get_master2_name "$product")
  local is_local_only="$2"
  
  if [ "$is_local_only" = "true" ]; then
    # 只有一个本地节点
    log_info "选择服务:"
    log_info "1) 本地主节点 ($master1)"
    log_info "0) 返回"
    log_prompt "请选择 [0-1]: "
  else
    # 有多个节点可选择
    log_info "选择服务:"
    log_info "1) 主节点1 ($master1)"
    log_info "2) 主节点2 ($master2)"
    log_info "3) 所有节点"
    log_info "0) 返回"
    log_prompt "请选择 [0-3]: "
  fi
}

# 启动服务
start_services() {
  local product="$1"
  log_info "启动 $product MySQL 服务..."
  
  # 检查当前环境中存在的配置文件
  log_info "检测本地节点配置..."
  local master1_compose_file=$(get_master1_compose_file "$product")
  local master2_compose_file=$(get_master2_compose_file "$product")
  local is_master1_local=false
  local is_master2_local=false
  
  if [ -f "$master1_compose_file" ]; then
    is_master1_local=true
    log_info "检测到主节点1配置文件: $(basename "$master1_compose_file")"
  fi
  
  if [ -f "$master2_compose_file" ]; then
    is_master2_local=true
    log_info "检测到主节点2配置文件: $(basename "$master2_compose_file")"
  fi
  
  if [ "$is_master1_local" = "false" ] && [ "$is_master2_local" = "false" ]; then
    log_error "在当前主机上未找到任何MySQL节点的配置文件"
    return 1
  fi
  
  # 确定本次操作针对的是本地还是所有节点
  local is_local_only=true
  if [ "$is_master1_local" = "true" ] && [ "$is_master2_local" = "true" ]; then
    is_local_only=false
    log_info "检测到多个本地节点，需要选择要操作的节点"
  else
    log_info "检测到单个本地节点，将自动选择该节点"
  fi
  
  local master1=$(get_master1_name "$product")
  local master2=$(get_master2_name "$product")
  
  # 确定服务选择
  local service_choice
  if [ "$is_local_only" = "true" ]; then
    # 只有一个本地节点时，自动选择该节点
    service_choice="local"
    if [ "$is_master1_local" = "true" ]; then
      log_info "自动选择唯一的本地节点: $master1 (主节点1)"
    else
      log_info "自动选择唯一的本地节点: $master2 (主节点2)"
    fi
  else
    # 有多个节点可选择时，显示选择菜单
    log_info "请选择要操作的节点:"
    select_service "$product" "$is_local_only"
    read service_choice
    case $service_choice in
      1) service_choice="master1" ;;
      2) service_choice="master2" ;;
      3) service_choice="all" ;;
      *) service_choice="" ;;
    esac
  fi
  
  if [ -z "$service_choice" ]; then
    log_error "操作已取消"
    return 1
  fi
  
  log_info "节点选择完成，开始执行启动操作..."
  
  case $service_choice in
    "local")
      if [ "$is_master1_local" = "true" ]; then
        log_info "启动本地主节点 ($master1)..."
        (cd "$COMPOSE_DIR" && docker-compose -f $(basename "$master1_compose_file") up -d)
      elif [ "$is_master2_local" = "true" ]; then
        log_info "启动本地主节点 ($master2)..."
        (cd "$COMPOSE_DIR" && docker-compose -f $(basename "$master2_compose_file") up -d)
      fi
      ;;
    "master1")
      if [ "$is_master1_local" = "true" ]; then
        log_info "启动主节点1 ($master1)..."
        (cd "$COMPOSE_DIR" && docker-compose -f $(basename "$master1_compose_file") up -d)
      else
        log_warning "主节点1 ($master1) 不在本地，无法启动"
        return 1
      fi
      ;;
    "master2")
      if [ "$is_master2_local" = "true" ]; then
        log_info "启动主节点2 ($master2)..."
        (cd "$COMPOSE_DIR" && docker-compose -f $(basename "$master2_compose_file") up -d)
      else
        log_warning "主节点2 ($master2) 不在本地，无法启动"
        return 1
      fi
      ;;
    "remote")
      log_warning "无法直接启动远程主机上的MySQL节点"
      log_info "请登录远程主机并在那里执行此脚本"
      return 1
      ;;
    "all")
      if [ "$is_master1_local" = "true" ]; then
        log_info "启动主节点1 ($master1)..."
        (cd "$COMPOSE_DIR" && docker-compose -f $(basename "$master1_compose_file") up -d)
      fi
      
      if [ "$is_master2_local" = "true" ]; then
        log_info "启动主节点2 ($master2)..."
        (cd "$COMPOSE_DIR" && docker-compose -f $(basename "$master2_compose_file") up -d)
      fi
      
      log_warning "远程主机上的节点需要单独启动"
      log_info "启动完成后，请使用setup-master-master.sh脚本配置主主复制"
      ;;
    *)
      log_error "无效的选择"
      return 1
      ;;
  esac
  
  if [ $? -eq 0 ]; then
    log_success "服务启动成功"
    wait_for_service 10
    check_status "$product"
    return 0
  else
    log_error "服务启动失败"
    return 1
  fi
}

# 停止服务
stop_services() {
  local product="$1"
  log_info "停止 $product MySQL 服务..."
  
  # 检查当前环境中存在的配置文件
  log_info "检测本地节点配置..."
  local master1_compose_file=$(get_master1_compose_file "$product")
  local master2_compose_file=$(get_master2_compose_file "$product")
  local is_master1_local=false
  local is_master2_local=false
  
  if [ -f "$master1_compose_file" ]; then
    is_master1_local=true
    log_info "检测到主节点1配置文件: $(basename "$master1_compose_file")"
  fi
  
  if [ -f "$master2_compose_file" ]; then
    is_master2_local=true
    log_info "检测到主节点2配置文件: $(basename "$master2_compose_file")"
  fi
  
  if [ "$is_master1_local" = "false" ] && [ "$is_master2_local" = "false" ]; then
    log_error "在当前主机上未找到任何MySQL节点的配置文件"
    return 1
  fi
  
  # 确定本次操作针对的是本地还是所有节点
  local is_local_only=true
  if [ "$is_master1_local" = "true" ] && [ "$is_master2_local" = "true" ]; then
    is_local_only=false
    log_info "检测到多个本地节点，需要选择要操作的节点"
  else
    log_info "检测到单个本地节点，将自动选择该节点"
  fi
  
  local master1=$(get_master1_name "$product")
  local master2=$(get_master2_name "$product")
  
  # 确定服务选择
  local service_choice
  if [ "$is_local_only" = "true" ]; then
    # 只有一个本地节点时，自动选择该节点
    service_choice="local"
    if [ "$is_master1_local" = "true" ]; then
      log_info "自动选择唯一的本地节点: $master1 (主节点1)"
    else
      log_info "自动选择唯一的本地节点: $master2 (主节点2)"
    fi
  else
    # 有多个节点可选择时，显示选择菜单
    log_info "请选择要操作的节点:"
    select_service "$product" "$is_local_only"
    read service_choice
    case $service_choice in
      1) service_choice="master1" ;;
      2) service_choice="master2" ;;
      3) service_choice="all" ;;
      *) service_choice="" ;;
    esac
  fi
  
  if [ -z "$service_choice" ]; then
    log_error "操作已取消"
    return 1
  fi
  
  log_info "节点选择完成，开始执行停止操作..."
  
  case $service_choice in
    "local")
      if [ "$is_master1_local" = "true" ] && is_container_running "$master1"; then
        log_info "停止本地主节点 ($master1)..."
        (cd "$COMPOSE_DIR" && docker-compose -f $(basename "$master1_compose_file") down)
      elif [ "$is_master2_local" = "true" ] && is_container_running "$master2"; then
        log_info "停止本地主节点 ($master2)..."
        (cd "$COMPOSE_DIR" && docker-compose -f $(basename "$master2_compose_file") down)
      else
        log_warning "本地MySQL节点当前未运行"
        return 0
      fi
      ;;
    "master1")
      if [ "$is_master1_local" = "true" ] && is_container_running "$master1"; then
        log_info "停止主节点1 ($master1)..."
        (cd "$COMPOSE_DIR" && docker-compose -f $(basename "$master1_compose_file") down)
      else
        log_warning "主节点1 ($master1) 当前未运行或不在本地"
        return 0
      fi
      ;;
    "master2")
      if [ "$is_master2_local" = "true" ] && is_container_running "$master2"; then
        log_info "停止主节点2 ($master2)..."
        (cd "$COMPOSE_DIR" && docker-compose -f $(basename "$master2_compose_file") down)
      else
        log_warning "主节点2 ($master2) 当前未运行或不在本地"
        return 0
      fi
      ;;
    "remote")
      log_warning "无法直接停止远程主机上的MySQL节点"
      log_info "请登录远程主机并在那里执行此脚本"
      return 1
      ;;
    "all")
      local any_running=false
      
      if [ "$is_master1_local" = "true" ] && is_container_running "$master1"; then
        log_info "停止主节点1 ($master1)..."
        (cd "$COMPOSE_DIR" && docker-compose -f $(basename "$master1_compose_file") down)
        any_running=true
      fi
      
      if [ "$is_master2_local" = "true" ] && is_container_running "$master2"; then
        log_info "停止主节点2 ($master2)..."
        (cd "$COMPOSE_DIR" && docker-compose -f $(basename "$master2_compose_file") down)
        any_running=true
      fi
      
      if [ "$any_running" = "false" ]; then
        log_warning "本地MySQL节点当前未运行"
      fi
      
      log_warning "远程主机上的节点需要单独停止"
      ;;
    *)
      log_error "无效的选择"
      return 1
      ;;
  esac
  
  if [ $? -eq 0 ]; then
    log_success "服务已停止"
    return 0
  else
    log_error "停止服务失败"
    return 1
  fi
}

# 重启服务
restart_services() {
  local product="$1"
  log_info "重启 $product MySQL 服务..."
  
  # 检查当前环境中存在的配置文件
  log_info "检测本地节点配置..."
  local master1_compose_file=$(get_master1_compose_file "$product")
  local master2_compose_file=$(get_master2_compose_file "$product")
  local is_master1_local=false
  local is_master2_local=false
  
  if [ -f "$master1_compose_file" ]; then
    is_master1_local=true
    log_info "检测到主节点1配置文件: $(basename "$master1_compose_file")"
  fi
  
  if [ -f "$master2_compose_file" ]; then
    is_master2_local=true
    log_info "检测到主节点2配置文件: $(basename "$master2_compose_file")"
  fi
  
  if [ "$is_master1_local" = "false" ] && [ "$is_master2_local" = "false" ]; then
    log_error "在当前主机上未找到任何MySQL节点的配置文件"
    return 1
  fi
  
  # 确定本次操作针对的是本地还是所有节点
  local is_local_only=true
  if [ "$is_master1_local" = "true" ] && [ "$is_master2_local" = "true" ]; then
    is_local_only=false
    log_info "检测到多个本地节点，需要选择要操作的节点"
  else
    log_info "检测到单个本地节点，将自动选择该节点"
  fi
  
  local master1=$(get_master1_name "$product")
  local master2=$(get_master2_name "$product")
  
  # 确定服务选择
  local service_choice
  if [ "$is_local_only" = "true" ]; then
    # 只有一个本地节点时，自动选择该节点
    service_choice="local"
    if [ "$is_master1_local" = "true" ]; then
      log_info "自动选择唯一的本地节点: $master1 (主节点1)"
    else
      log_info "自动选择唯一的本地节点: $master2 (主节点2)"
    fi
  else
    # 有多个节点可选择时，显示选择菜单
    log_info "请选择要操作的节点:"
    select_service "$product" "$is_local_only"
    read service_choice
    case $service_choice in
      1) service_choice="master1" ;;
      2) service_choice="master2" ;;
      3) service_choice="all" ;;
      *) service_choice="" ;;
    esac
  fi
  
  if [ -z "$service_choice" ]; then
    log_error "操作已取消"
    return 1
  fi
  
  log_info "节点选择完成，开始执行重启操作..."
  
  case $service_choice in
    "local")
      if [ "$is_master1_local" = "true" ]; then
        log_info "重启本地主节点 ($master1)..."
        (cd "$COMPOSE_DIR" && docker-compose -f $(basename "$master1_compose_file") restart)
      elif [ "$is_master2_local" = "true" ]; then
        log_info "重启本地主节点 ($master2)..."
        (cd "$COMPOSE_DIR" && docker-compose -f $(basename "$master2_compose_file") restart)
      fi
      ;;
    "master1")
      if [ "$is_master1_local" = "true" ]; then
        log_info "重启主节点1 ($master1)..."
        (cd "$COMPOSE_DIR" && docker-compose -f $(basename "$master1_compose_file") restart)
      else
        log_warning "主节点1 ($master1) 不在本地，无法重启"
        return 1
      fi
      ;;
    "master2")
      if [ "$is_master2_local" = "true" ]; then
        log_info "重启主节点2 ($master2)..."
        (cd "$COMPOSE_DIR" && docker-compose -f $(basename "$master2_compose_file") restart)
      else
        log_warning "主节点2 ($master2) 不在本地，无法重启"
        return 1
      fi
      ;;
    "remote")
      log_warning "无法直接重启远程主机上的MySQL节点"
      log_info "请登录远程主机并在那里执行此脚本"
      return 1
      ;;
    "all")
      if [ "$is_master1_local" = "true" ]; then
        log_info "重启主节点1 ($master1)..."
        (cd "$COMPOSE_DIR" && docker-compose -f $(basename "$master1_compose_file") restart)
      fi
      
      if [ "$is_master2_local" = "true" ]; then
        log_info "重启主节点2 ($master2)..."
        (cd "$COMPOSE_DIR" && docker-compose -f $(basename "$master2_compose_file") restart)
      fi
      
      log_warning "远程主机上的节点需要单独重启"
      ;;
    *)
      log_error "无效的选择"
      return 1
      ;;
  esac
  
  if [ $? -eq 0 ]; then
    log_success "服务已重启"
    wait_for_service 10
    check_status "$product"
    return 0
  else
    log_error "重启服务失败"
    return 1
  fi
} 