# 使用官方Redis 7.2镜像作为基础镜像
FROM redis:7.2

# 添加标签信息
LABEL maintainer="Medical Products Team"
LABEL description="Redis cache for medical products"
LABEL version="1.0"

# 设置环境变量
ENV TZ=Asia/Shanghai

# 复制自定义配置文件
COPY config/redis.conf /usr/local/etc/redis/redis.conf

# 创建数据和日志目录
RUN mkdir -p /data/redis \
    && mkdir -p /var/log/redis \
    && chown redis:redis /data/redis \
    && chown redis:redis /var/log/redis

# 设置工作目录
WORKDIR /data

# 暴露端口
EXPOSE 6379

# 设置健康检查
HEALTHCHECK --interval=30s --timeout=3s \
  CMD redis-cli ping || exit 1

# 使用自定义配置文件启动Redis
CMD ["redis-server", "/usr/local/etc/redis/redis.conf"] 