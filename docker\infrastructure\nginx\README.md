# Nginx 容器镜像

## 简介
此目录包含用于构建医疗产品网关服务的Nginx容器镜像配置。基于官方Nginx 1.24镜像，针对医疗产品场景进行了优化配置。

## 目录结构
```
nginx/
├── Dockerfile          # 镜像构建文件
└── config/            # 配置文件目录
    ├── nginx.conf    # Nginx主配置文件
    ├── conf.d/       # 站点配置目录
    │   └── default.conf  # 默认站点配置
    └── ssl/          # SSL证书目录
```

## 特性
- 基于Nginx 1.24官方镜像
- SSL/TLS配置优化
- HTTP/2支持
- Gzip压缩
- 安全头部配置
- 内置健康检查
- 完整的日志记录

## 配置说明
### 主要配置参数
- 工作进程：自动（根据CPU核心数）
- 最大连接数：1024
- 支持的SSL协议：TLSv1.2, TLSv1.3
- 默认字符集：UTF-8
- 客户端最大请求体：10M

### 日志配置
使用Docker命名卷进行日志持久化：
```yaml
volumes:
  - nginx_logs:/var/log/nginx
```

### SSL配置
SSL证书文件需要放置在 `config/ssl/` 目录下：
- server.crt：服务器证书
- server.key：私钥文件

## 使用方法
1. 构建镜像
```bash
docker build -t medical/nginx:1.24 .
```

2. 运行容器
```bash
docker run -d \
  --name nginx \
  -v nginx_logs:/var/log/nginx \
  -v nginx_conf:/etc/nginx/conf.d \
  -v nginx_ssl:/etc/nginx/ssl \
  -p 80:80 \
  -p 443:443 \
  medical/nginx:1.24
```

## 安全注意事项
1. 及时更新SSL证书
2. 定期更新安全补丁
3. 配置适当的访问控制
4. 监控异常访问

## 维护说明
1. 日志位置：/var/log/nginx/
2. 配置文件：
   - 主配置：/etc/nginx/nginx.conf
   - 站点配置：/etc/nginx/conf.d/
3. SSL证书：/etc/nginx/ssl/

## 健康检查
镜像内置健康检查机制，每30秒执行一次，通过HTTP请求检查服务状态。

## 性能优化
1. 启用Gzip压缩
2. 配置缓存策略
3. 启用HTTP/2
4. 优化SSL配置
5. 启用多工作进程

## 监控指标
1. 请求响应时间
2. 错误率
3. 并发连接数
4. SSL握手时间

## 故障排除
1. 查看访问日志：
```bash
docker exec nginx tail -f /var/log/nginx/access.log
```

2. 查看错误日志：
```bash
docker exec nginx tail -f /var/log/nginx/error.log
```

3. 测试配置文件：
```bash
docker exec nginx nginx -t
```

## 版本历史
- 1.0.0: 初始版本，基于Nginx 1.24
