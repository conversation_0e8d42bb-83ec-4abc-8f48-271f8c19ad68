version: '3.8'

services:
  redis-slave:
    image: **************:1443/btit/infra/redis:7.4.2
    container_name: ${PRODUCT_NAME:-quicksign}-redis-slave
    command: redis-server /usr/local/etc/redis/redis.conf --requirepass "${REDIS_PASSWORD}" --masterauth "${REDIS_PASSWORD}" --replicaof "${REDIS_MASTER_HOST:-localhost}" "${REDIS_MASTER_PORT:-16399}"
    volumes:
      - ../config/redis.conf:/usr/local/etc/redis/redis.conf:ro
      - ../data/${PRODUCT_NAME:-quicksign}/slave:/data
      - ../logs/${PRODUCT_NAME:-quicksign}/slave:/var/log/redis
    ports:
      - "${REDIS_PORT:-16399}:6379"
    restart: always
    networks:
      - redis-net
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    env_file:
      - ../env/quicksign.env

networks:
  redis-net:
    driver: bridge 