#!/bin/bash
# MySQL 日志查看功能
# 包含查看各种日志的功能

# 引入依赖
source "$(dirname "${BASH_SOURCE[0]}")/utils.sh"

# 日志类型菜单
select_log_type() {
  echo "选择日志类型:"
  echo "1) 错误日志"
  echo "2) 慢查询日志"
  echo "3) 容器日志"
  echo -n "请选择 [1-3]: "
  local log_choice
  read log_choice
  
  echo "$log_choice"
}

# 查看错误日志
show_error_log() {
  local node="$1"
  
  log_info "显示错误日志..."
  docker exec $node bash -c "if [ -f /var/log/mysql/error.log ]; then tail -n 100 /var/log/mysql/error.log; elif [ -f /var/lib/mysql/*.err ]; then tail -n 100 /var/lib/mysql/*.err; else echo '找不到错误日志文件'; fi"
}

# 查看慢查询日志
show_slow_query_log() {
  local node="$1"
  
  log_info "显示慢查询日志..."
  docker exec $node bash -c "if [ -f /var/log/mysql/mysql-slow.log ]; then tail -n 100 /var/log/mysql/mysql-slow.log; else echo '找不到慢查询日志文件'; fi"
}

# 查看容器日志
show_container_log() {
  local node="$1"
  
  log_info "显示容器日志..."
  docker logs --tail 100 $node
}

# 显示日志
show_logs() {
  local product="$1"
  log_info "显示 $product MySQL 日志..."
  
  local node=$(select_node "$product")
  if [ $? -ne 0 ]; then
    return 1
  fi
  
  # 检查节点是否运行
  if ! is_container_running "$node"; then
    log_error "节点 $node 未运行"
    return 1
  fi
  
  local log_choice=$(select_log_type)
  
  case $log_choice in
    1)
      show_error_log "$node"
      ;;
    2)
      show_slow_query_log "$node"
      ;;
    3)
      show_container_log "$node"
      ;;
    *)
      log_error "无效的选择"
      return 1
      ;;
  esac
  
  return 0
}

# 显示数据库连接信息
show_connection_info() {
  local product="$1"
  log_info "显示 $product MySQL 连接信息..."
  
  local master1=$(get_master1_name "$product")
  local master2=$(get_master2_name "$product")
  local port=$(get_mysql_port "$product")
  
  local master1_ip=$(docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' $master1 2>/dev/null || echo "未运行")
  local master2_ip=$(docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' $master2 2>/dev/null || echo "未运行")
  
  # 获取Docker容器对外端口映射
  local master1_port=$(docker port $master1 3306 2>/dev/null | cut -d':' -f2 || echo "$port")
  local master2_port=$(docker port $master2 3306 2>/dev/null | cut -d':' -f2 || echo "$port")
  
  local password=$(get_mysql_root_password 2>/dev/null || echo "无法读取密码文件")
  
  echo -e "${GREEN}=== MySQL 连接信息 - $product ===${NC}"
  echo "产品: $product (端口: $port)"
  echo "主节点1 ($master1):"
  echo "  - 内部IP: $master1_ip"
  echo "  - 外部端口: $master1_port"
  echo "主节点2 ($master2):"
  echo "  - 内部IP: $master2_ip"
  echo "  - 外部端口: $master2_port"
  echo "数据库: $product"
  echo "用户名: root"
  echo "密码: $password"
  echo ""
  echo "JDBC URL: *************************************************************************"
  echo ""
  echo "命令行连接示例:"
  echo "  mysql -h127.0.0.1 -P$port -uroot -p$password $product"
  echo -e "${GREEN}===================================${NC}"
  
  return 0
} 