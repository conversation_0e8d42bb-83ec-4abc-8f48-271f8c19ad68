# Keepalived RPM 安装方式

本目录包含通过 RPM 包安装 Keepalived 的相关文件和脚本。RPM 安装方式相比源码编译安装更加简便快捷，适用于大多数企业级 Linux 发行版。

> **重要提示**: 此目录下的安装脚本已迁移到统一安装脚本 (`scripts/install-keepalived.sh`)。请使用统一安装脚本进行安装，可通过 `--rpm` 参数指定使用RPM方式。详情请参考[主文档](../README.md)。

## 目录结构

```
rpm/
  ├── README.md                  # RPM安装说明文档
  └── packages/                  # RPM包存放目录
      ├── rhel/                  # RHEL/CentOS系列RPM包
      │   ├── keepalived-2.2.7-1.el8.x86_64.rpm  # RHEL 8 RPM包
      │   └── keepalived-2.2.7-1.el7.x86_64.rpm  # RHEL 7 RPM包
      ├── openeuler/             # openEuler系列RPM包
      │   ├── keepalived-2.2.4-1.oe2203.x86_64.rpm # openEuler 22.03 RPM包
      │   └── keepalived-help-2.2.4-1.oe2203.noarch.rpm # openEuler 22.03 帮助包
      └── suse/                  # SUSE系列RPM包
          └── keepalived-2.2.7-1.suse15.x86_64.rpm # SUSE 15 RPM包
```

## 支持的操作系统

- **RHEL/CentOS**: 7.x, 8.x
- **openEuler**: 20.03, 22.03
- **SUSE Linux Enterprise**: 15
- **其他基于 RPM 的系统**: 只要依赖库版本兼容即可使用

## 安装前准备

1. 确认系统版本与 RPM 包版本匹配
2. 确保系统已安装基本依赖包
3. 建议在安装前备份系统配置

## RPM 包获取

您可以从以下渠道获取 Keepalived RPM 包：

### openEuler 22.03 RPM包

```bash
# 从官方软件源下载主程序包
curl -O https://repo.openeuler.org/openEuler-22.03-LTS/everything/x86_64/Packages/keepalived-2.2.4-1.oe2203.x86_64.rpm

# 下载帮助包（可选）
curl -O https://repo.openeuler.org/openEuler-22.03-LTS/everything/noarch/Packages/keepalived-help-2.2.4-1.oe2203.noarch.rpm

# 或者使用wget
wget https://repo.openeuler.org/openEuler-22.03-LTS/everything/x86_64/Packages/keepalived-2.2.4-1.oe2203.x86_64.rpm
wget https://repo.openeuler.org/openEuler-22.03-LTS/everything/noarch/Packages/keepalived-help-2.2.4-1.oe2203.noarch.rpm
```

### RHEL/CentOS 7.x/8.x RPM包

```bash
# CentOS 7
curl -O http://mirror.centos.org/centos/7/os/x86_64/Packages/keepalived-2.0.20-2.el7.x86_64.rpm

# CentOS 8/RHEL 8
curl -O http://mirror.centos.org/centos/8-stream/AppStream/x86_64/os/Packages/keepalived-2.0.20-2.el8.x86_64.rpm
```

### SUSE Linux Enterprise 15 RPM包

从SUSE软件源下载，需要有SUSE订阅权限或使用openSUSE镜像。

## 使用统一安装脚本

```bash
# 进入 keepalived 目录
cd /path/to/infrastructure/gateway/keepalived

# 使用统一安装脚本，指定RPM安装方式
scripts/install-keepalived.sh --rpm master  # 安装为主节点
scripts/install-keepalived.sh --rpm backup  # 安装为备节点

# 安装并配置高可用环境
scripts/install-keepalived.sh --rpm --setup-ha master
```

## 手动安装方法

如果需要手动安装，可以使用以下命令：

```bash
# 安装RPM包
rpm -Uvh rpm/packages/rhel/keepalived-2.2.7-1.el8.x86_64.rpm  # RHEL/CentOS 8
# 或
rpm -Uvh rpm/packages/openeuler/keepalived-2.2.4-1.oe2203.x86_64.rpm  # openEuler 22.03
rpm -Uvh rpm/packages/openeuler/keepalived-help-2.2.4-1.oe2203.noarch.rpm  # openEuler 22.03 帮助包(可选)
# 或
rpm -Uvh rpm/packages/suse/keepalived-2.2.7-1.suse15.x86_64.rpm  # SUSE 15

# 然后手动配置
mkdir -p /etc/keepalived/scripts
cp -f config/keepalived_master.conf /etc/keepalived/keepalived.conf  # 主节点
# 或
cp -f config/keepalived_backup.conf /etc/keepalived/keepalived.conf  # 备节点

# 复制其他必要文件
cp -f config/notify.sh /etc/keepalived/
cp -f config/scripts/* /etc/keepalived/scripts/
chmod +x /etc/keepalived/notify.sh
chmod +x /etc/keepalived/scripts/*.sh

# 启动服务
systemctl enable keepalived
systemctl start keepalived
```

## 离线安装说明

本方案主要针对离线环境设计，所有必要的 RPM 包应预先下载并放置在对应目录中。确保下载的RPM包及其依赖存放在正确的目录结构中。

## 常见问题

1. **不同版本系统兼容性**
   
   每个 RPM 包都针对特定的操作系统版本构建。如果您的系统版本与提供的 RPM 包不匹配，可能需要重新构建 RPM 包或考虑使用源码安装方式。

2. **依赖关系解决**

   如遇到依赖问题，请参考系统文档手动解决。在离线环境中，应确保所有依赖包一并下载。

3. **配置文件冲突**

   如果系统中已存在 Keepalived 配置文件，安装时可能会询问是否覆盖。建议先备份现有配置。 