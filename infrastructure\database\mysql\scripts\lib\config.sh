#!/bin/bash
# MySQL 配置文件
# 包含所有MySQL维护脚本使用的配置项和常量

# 颜色定义 - 非只读，避免与其他脚本冲突
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # 无颜色

# 目录路径定义 - 非只读，避免与其他脚本冲突
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
BASE_DIR="$(dirname "$SCRIPT_DIR")"
COMPOSE_DIR="$BASE_DIR/compose"
DATA_DIR="$BASE_DIR/data"
CONFIG_DIR="$BASE_DIR/config"
BACKUP_DIR="$BASE_DIR/backups"
SECRETS_DIR="$BASE_DIR/secrets"
LIB_DIR="$SCRIPT_DIR/lib"

# 默认产品配置 - 非只读
DEFAULT_PRODUCT="cloudkey"

# 产品端口映射
declare -A PRODUCT_PORTS
PRODUCT_PORTS["cloudkey"]=13306
PRODUCT_PORTS["eseal"]=13316
PRODUCT_PORTS["quicksign"]=13326

# MySQL容器名称格式
get_master1_name() {
  local product="$1"
  echo "${product}-mysql-master1"
}

get_master2_name() {
  local product="$1"
  echo "${product}-mysql-master2"
}

# MySQL Compose 文件
get_master1_compose_file() {
  local product="$1"
  echo "${COMPOSE_DIR}/${product}-mysql-master1.yml"
}

get_master2_compose_file() {
  local product="$1"
  echo "${COMPOSE_DIR}/${product}-mysql-master2.yml"
}

get_mysql_root_password_file() {
  echo "${SECRETS_DIR}/mysql_root_password.txt"
}

# 获取MySQL端口
get_mysql_port() {
  local product="$1"
  echo "${PRODUCT_PORTS[$product]:-13306}"
} 