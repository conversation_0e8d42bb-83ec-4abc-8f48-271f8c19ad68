# JDK 基础镜像

## 简介
此目录包含用于构建Java应用基础环境的JDK容器镜像配置。基于Eclipse Temurin JDK镜像，提供多个JDK版本支持。

## 目录结构
```
jdk/
├── Dockerfile.template  # 镜像构建模板文件
├── shared/            # 共享资源目录
│   ├── crypto-files/  # JDK加密组件
│   ├── certs/        # 证书文件
│   └── scripts/      # 通用脚本
└── versions.json     # 版本配置文件
```

## 特性
- 支持多个JDK版本（8、17）
- 基于Eclipse Temurin官方镜像
- 内置加密组件支持
- 证书管理
- 性能优化配置
- UTF-8编码支持

## 版本说明
### JDK 8
- 基础镜像：eclipse-temurin:8-jdk-jammy
- 适用场景：传统应用、遗留系统
- 支持特性：完整Java 8功能

### JDK 17
- 基础镜像：eclipse-temurin:17-jdk-jammy
- 适用场景：当前LTS版本
- 支持特性：密封类、模式匹配等

## 构建说明
所有JDK镜像通过脚本动态生成Dockerfile构建，不再在代码库中保存特定版本的Dockerfile。

1. 使用构建脚本构建镜像
```bash
# 构建所有JDK版本镜像
./docker/scripts/build/build-jdk.sh

# 构建特定版本镜像
./docker/scripts/build/build-jdk.sh 8

# 构建调试模式（保留临时文件）
DEBUG=true ./docker/scripts/build/build-jdk.sh
```

2. 查看支持的版本
```bash
# 查看所有支持的版本
cat docker/infrastructure/jdk/versions.json
```

## 使用方法
1. 作为基础镜像
```dockerfile
# 使用JDK 17
FROM btit/jdk:17-temurin-jammy

WORKDIR /app
COPY . .
RUN ./mvnw package

CMD ["java", "-jar", "target/app.jar"]
```

2. 直接运行Java应用
```bash
docker run -d \
  --name myapp \
  -v app_logs:/app/logs \
  -p 8080:8080 \
  btit/jdk:17-temurin-jammy \
  java -jar app.jar
```

## 配置说明
### JVM配置
默认JVM参数：
```
-XX:+UseContainerSupport
-XX:MaxRAMPercentage=75.0
-XX:InitialRAMPercentage=50.0
```

### 环境变量
- `JAVA_TOOL_OPTIONS`: JVM参数
- `JAVA_HOME`: JDK安装路径
- `TZ`: 时区，默认Asia/Shanghai

## 安全说明
1. 加密组件
   - 位置：shared/crypto-files/
   - 支持算法：AES-256等
   - 版本通过versions.json中的crypto_files_version控制

2. 证书管理
   - 位置：shared/certs/
   - 支持格式：JKS、PKCS12

## 性能优化
1. 容器感知
2. 内存管理优化
3. GC优化
4. 线程池配置

## 故障排除
1. 查看JVM状态：
```bash
docker exec myapp jcmd 1 VM.info
```

2. 查看线程转储：
```bash
docker exec myapp jcmd 1 Thread.print
```

## 版本历史
- 1.0.0: 初始版本，支持JDK 8/17
- 1.1.0: 优化目录结构，改为动态生成Dockerfile
