#!/bin/bash

# 导入通用函数库
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${SCRIPT_DIR}/common.sh"

# 解析版本号为数组
# 用法: parse_version "1.2.3" version_array
parse_version() {
    local version_str="$1"
    local -n version_array="$2"  # 通过引用传递数组
    
    # 清空数组
    version_array=()
    
    # 使用 IFS 分割版本号
    local IFS='.'
    read -ra version_array <<< "$version_str"
}

# 比较两个版本号
# 返回: 0=相等 1=第一个大 2=第二个大
compare_versions() {
    local version1="$1"
    local version2="$2"
    
    local v1_array=()
    local v2_array=()
    
    parse_version "$version1" v1_array
    parse_version "$version2" v2_array
    
    # 获取最大长度
    local max_length
    if [ ${#v1_array[@]} -gt ${#v2_array[@]} ]; then
        max_length=${#v1_array[@]}
    else
        max_length=${#v2_array[@]}
    fi
    
    # 补齐短的版本号为0
    while [ ${#v1_array[@]} -lt $max_length ]; do
        v1_array+=("0")
    done
    while [ ${#v2_array[@]} -lt $max_length ]; do
        v2_array+=("0")
    done
    
    # 逐位比较
    for ((i=0; i<max_length; i++)); do
        if [ "${v1_array[$i]}" -gt "${v2_array[$i]}" ]; then
            return 1
        elif [ "${v1_array[$i]}" -lt "${v2_array[$i]}" ]; then
            return 2
        fi
    done
    
    return 0
}

# 检查版本号是否满足最低要求
# 用法: check_min_version "当前版本" "最低要求版本"
check_min_version() {
    local current_version="$1"
    local min_version="$2"
    
    compare_versions "$current_version" "$min_version"
    local result=$?
    
    if [ $result -eq 2 ]; then
        log_error "版本 $current_version 低于最低要求 $min_version"
        return 1
    fi
    return 0
}

# 检查版本号是否在指定范围内
# 用法: check_version_range "当前版本" "最低版本" "最高版本"
check_version_range() {
    local current_version="$1"
    local min_version="$2"
    local max_version="$3"
    
    # 检查最低版本
    if ! check_min_version "$current_version" "$min_version"; then
        return 1
    fi
    
    # 检查最高版本
    compare_versions "$current_version" "$max_version"
    local result=$?
    
    if [ $result -eq 1 ]; then
        log_error "版本 $current_version 高于最高要求 $max_version"
        return 1
    fi
    return 0
}

# 从文件中读取版本信息
# 用法: read_version_from_file "版本文件路径" "版本键名"
read_version_from_file() {
    local version_file="$1"
    local version_key="$2"
    
    if [ ! -f "$version_file" ]; then
        log_error "版本文件不存在: $version_file"
        return 1
    fi
    
    local version
    version=$(grep "^${version_key}=" "$version_file" | cut -d'=' -f2)
    
    if [ -z "$version" ]; then
        log_error "在文件 $version_file 中未找到版本信息: $version_key"
        return 1
    fi
    
    echo "$version"
    return 0
}

# 更新版本文件中的版本号
# 用法: update_version_in_file "版本文件路径" "版本键名" "新版本号"
update_version_in_file() {
    local version_file="$1"
    local version_key="$2"
    local new_version="$3"
    
    if [ ! -f "$version_file" ]; then
        log_error "版本文件不存在: $version_file"
        return 1
    fi
    
    # 创建临时文件
    local temp_file
    temp_file=$(mktemp)
    
    # 更新版本号
    if grep -q "^${version_key}=" "$version_file"; then
        sed "s/^${version_key}=.*/${version_key}=${new_version}/" "$version_file" > "$temp_file"
    else
        cp "$version_file" "$temp_file"
        echo "${version_key}=${new_version}" >> "$temp_file"
    fi
    
    # 替换原文件
    mv "$temp_file" "$version_file"
    
    log_info "版本已更新: ${version_key}=${new_version}"
    return 0
}

# 生成下一个版本号
# 用法: generate_next_version "当前版本" "版本类型(major|minor|patch)"
generate_next_version() {
    local current_version="$1"
    local version_type="$2"
    
    local version_array=()
    parse_version "$current_version" version_array
    
    case "$version_type" in
        major)
            ((version_array[0]++))
            version_array[1]=0
            version_array[2]=0
            ;;
        minor)
            ((version_array[1]++))
            version_array[2]=0
            ;;
        patch)
            ((version_array[2]++))
            ;;
        *)
            log_error "无效的版本类型: $version_type"
            return 1
            ;;
    esac
    
    echo "${version_array[0]}.${version_array[1]}.${version_array[2]}"
    return 0
}

# 验证版本号格式
# 用法: validate_version "版本号"
validate_version() {
    local version="$1"
    
    if [[ ! "$version" =~ ^[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        log_error "无效的版本号格式: $version"
        log_error "版本号必须符合格式: X.Y.Z"
        return 1
    fi
    return 0
}
