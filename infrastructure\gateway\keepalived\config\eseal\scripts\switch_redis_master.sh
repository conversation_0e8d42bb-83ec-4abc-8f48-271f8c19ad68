#!/bin/bash
#
# Redis主从切换脚本 - 电子签章系统
# 当keepalived发生状态变更时，触发Redis主从角色切换
#

# 将脚本输出记录到日志文件
exec 1>> /var/log/redis_eseal_ha.log 2>&1

# 记录脚本开始执行的时间
echo "$(date +'%Y-%m-%d %H:%M:%S') 开始执行电子签章系统Redis主从切换"

# 配置项
HA_MASTER_HOST="************"  # 主节点IP（原主节点）
HA_BACKUP_HOST="************"  # 备节点IP（将晋升为主节点）
REDIS_PORT="16379"
REDIS_PASSWORD="ESEAL_Redis@2006"  # Redis密码
REDIS_CONTAINER="eseal-redis"  # Redis容器名称

# 当前主机IP地址（用于确定当前执行节点的角色）
CURRENT_HOST_IP=$(hostname -I | awk '{print $1}')

# 如果存在密码，构建认证参数
AUTH_PARAM=""
if [ -n "$REDIS_PASSWORD" ]; then
    AUTH_PARAM="-a $REDIS_PASSWORD"
fi

# 获取当前Redis节点角色
get_redis_role() {
    local host=$1
    local role=$(docker exec -i $REDIS_CONTAINER redis-cli -h $host -p $REDIS_PORT $AUTH_PARAM info replication 2>/dev/null | grep "^role:" | cut -d ":" -f2 | tr -d '[:space:]')
    echo $role
}

# 将Redis节点提升为主节点
promote_to_master() {
    local host=$1
    echo "尝试将 $host 提升为Redis主节点..."
    docker exec -i $REDIS_CONTAINER redis-cli -h $host -p $REDIS_PORT $AUTH_PARAM slaveof no one
    
    # 检查是否成功切换为主节点
    sleep 3
    local new_role=$(get_redis_role $host)
    if [ "$new_role" = "master" ]; then
        echo "成功: $host 已晋升为Redis主节点"
        return 0
    else
        echo "失败: $host 未能晋升为Redis主节点，当前角色: $new_role"
        return 1
    fi
}

# 将节点设置为从节点
set_as_slave() {
    local slave_host=$1
    local master_host=$2
    
    echo "尝试将 $slave_host 设置为 $master_host 的Redis从节点..."
    docker exec -i $REDIS_CONTAINER redis-cli -h $slave_host -p $REDIS_PORT $AUTH_PARAM slaveof $master_host $REDIS_PORT
    
    # 检查是否成功切换为从节点
    sleep 3
    local replication_info=$(docker exec -i $REDIS_CONTAINER redis-cli -h $slave_host -p $REDIS_PORT $AUTH_PARAM info replication)
    
    # 验证主节点设置是否正确
    echo "$replication_info" | grep -q "master_host:$master_host"
    if [ $? -eq 0 ]; then
        local role=$(echo "$replication_info" | grep "^role:" | cut -d ":" -f2 | tr -d '[:space:]')
        if [ "$role" = "slave" ]; then
            echo "成功: $slave_host 已成为 $master_host 的Redis从节点"
            return 0
        fi
    fi
    
    echo "失败: $slave_host 未能正确设置为 $master_host 的Redis从节点"
    return 1
}

# 主函数 - 根据当前主机IP决定操作
main() {
    # 获取keepalived状态（从环境变量）
    STATE=$1
    
    echo "当前主机IP: $CURRENT_HOST_IP, Keepalived状态: $STATE"
    
    if [ "$STATE" = "MASTER" ]; then
        # 当前节点成为Keepalived主节点
        if [ "$CURRENT_HOST_IP" = "$HA_BACKUP_HOST" ]; then
            # 当前节点是原备节点，需要晋升为主节点
            echo "当前节点(原备节点)需要晋升为Redis主节点"
            promote_to_master $CURRENT_HOST_IP
        else
            # 当前节点是原主节点，已经是主节点，不需要操作
            echo "当前节点已经是Redis主节点，不需要操作"
        fi
    elif [ "$STATE" = "BACKUP" ]; then
        # 当前节点成为Keepalived备份节点
        if [ "$CURRENT_HOST_IP" = "$HA_MASTER_HOST" ]; then
            # 当前节点是原主节点，需要降级为从节点
            # 此时应该找到当前的Redis主节点（应该是原从节点）
            echo "原主节点恢复后，降级为Redis从节点"
            set_as_slave $CURRENT_HOST_IP $HA_BACKUP_HOST
        fi
    elif [ "$STATE" = "FAULT" ]; then
        # 当前节点处于故障状态，不执行操作
        echo "当前节点处于故障状态，不执行操作"
    fi
}

# 检查参数
if [ $# -ne 1 ]; then
    echo "用法: $0 <MASTER|BACKUP|FAULT>"
    exit 1
fi

# 执行主函数
main $1

echo "$(date +'%Y-%m-%d %H:%M:%S') 电子签章系统Redis主从切换脚本执行完成"
exit 0 