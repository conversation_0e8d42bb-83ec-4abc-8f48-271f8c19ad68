#!/bin/bash
# Nginx服务控制脚本
# 用法: ./nginx-control.sh [start|stop|restart|status|undeploy|reload|test|testfull]

# 脚本所在目录
SCRIPT_DIR=$(cd "$(dirname "$0")" && pwd)
BASE_DIR=$(dirname "$SCRIPT_DIR")
SERVICE_NAME="Nginx网关服务"
CONTAINER_NAME="gateway-nginx"

# 在文件开头添加 Docker Compose 命令变量
DOCKER_COMPOSE_CMD=""

# 在颜色定义后添加函数
# 确定使用哪个版本的 Docker Compose 命令
determine_docker_compose_cmd() {
    # 首先尝试 Docker Compose V2
    if docker compose version &> /dev/null; then
        DOCKER_COMPOSE_CMD="docker compose"
        return 0
    fi
    
    # 如果 V2 不可用，尝试传统版本
    if command -v docker-compose &> /dev/null; then
        DOCKER_COMPOSE_CMD="docker-compose"
        return 0
    fi
    
    # 如果都不可用
    echo "错误: 未找到可用的 Docker Compose 命令"
    return 1
}

# 在主要逻辑开始前调用检测函数
if ! determine_docker_compose_cmd; then
    exit 1
fi

# 显示使用帮助
show_usage() {
    echo "用法: $0 [start|stop|restart|status|undeploy|reload|test|testfull]"
    echo ""
    echo "命令:"
    echo "  start     启动$SERVICE_NAME"
    echo "  stop      停止$SERVICE_NAME"
    echo "  restart   重启$SERVICE_NAME"
    echo "  status    查看$SERVICE_NAME状态"
    echo "  undeploy  卸载$SERVICE_NAME"
    echo "  reload    重新加载$SERVICE_NAME配置"
    echo "  test      测试$SERVICE_NAME配置文件语法"
    echo "  testfull  测试$SERVICE_NAME配置文件语法并显示完整配置"
    echo ""
}

# 启动服务
start_service() {
    echo "正在启动$SERVICE_NAME..."
    cd "$BASE_DIR" || exit 1
    
    # 检查服务是否已经运行
    if docker ps | grep -q "$CONTAINER_NAME"; then
        echo "$SERVICE_NAME已经在运行中！"
        return 0
    fi
    
    # 检查服务是否存在但已停止
    if docker ps -a | grep -q "$CONTAINER_NAME"; then
        echo "检测到已停止的$SERVICE_NAME，正在启动..."
        $DOCKER_COMPOSE_CMD start
    else
        echo "未检测到已存在的$SERVICE_NAME，正在创建并启动..."
        $DOCKER_COMPOSE_CMD up -d
    fi
    
    if [ $? -eq 0 ]; then
        echo "$SERVICE_NAME启动成功！"
    else
        echo "$SERVICE_NAME启动失败，请检查配置和日志！"
        exit 1
    fi
}

# 停止服务
stop_service() {
    echo "正在停止$SERVICE_NAME..."
    cd "$BASE_DIR" || exit 1
    
    # 检查服务是否正在运行
    if ! docker ps | grep -q "$CONTAINER_NAME"; then
        echo "$SERVICE_NAME未在运行！"
        return 0
    fi
    
    # 停止服务
    $DOCKER_COMPOSE_CMD stop
    
    if [ $? -eq 0 ]; then
        echo "$SERVICE_NAME已停止！"
    else
        echo "$SERVICE_NAME停止失败，请手动检查！"
        exit 1
    fi
}

# 重启服务
restart_service() {
    echo "正在重启$SERVICE_NAME..."
    cd "$BASE_DIR" || exit 1
    
    # 检查服务是否存在
    if ! docker ps -a | grep -q "$CONTAINER_NAME"; then
        echo "未检测到$SERVICE_NAME，将创建并启动..."
        $DOCKER_COMPOSE_CMD up -d
    else
        # 重启服务
        $DOCKER_COMPOSE_CMD restart
    fi
    
    if [ $? -eq 0 ]; then
        echo "$SERVICE_NAME重启成功！"
    else
        echo "$SERVICE_NAME重启失败，请检查配置和日志！"
        exit 1
    fi
}

# 检查服务状态
check_status() {
    echo "正在检查$SERVICE_NAME状态..."
    cd "$BASE_DIR" || exit 1
    
    # 检查服务是否存在
    if ! docker ps -a | grep -q "$CONTAINER_NAME"; then
        echo "未检测到$SERVICE_NAME！"
        return 1
    fi
    
    # 检查服务是否运行
    if docker ps | grep -q "$CONTAINER_NAME"; then
        echo "$SERVICE_NAME状态: 运行中"
        
        # 显示容器详情
        echo "容器详情:"
        docker ps --filter "name=$CONTAINER_NAME" --format "表格 {{.ID}}\t{{.Image}}\t{{.Status}}\t{{.Ports}}"
        
        # 显示健康状态
        echo "服务健康状态:"
        CONTAINER_ID=$(docker ps -q --filter "name=$CONTAINER_NAME")
        if docker exec "$CONTAINER_ID" nginx -t &>/dev/null; then
            echo "配置检查: 正常"
        else
            echo "配置检查: 异常"
            docker exec "$CONTAINER_ID" nginx -t
        fi
        
        echo "HTTP端口状态:"
        if netstat -tuln 2>/dev/null | grep -q ":6443" || ss -tuln 2>/dev/null | grep -q ":6443"; then
            echo "6443端口: 监听中"
        else
            echo "6443端口: 未监听"
        fi
    else
        echo "$SERVICE_NAME状态: 已停止"
    fi
}

# 卸载服务
undeploy_service() {
    echo "正在卸载$SERVICE_NAME..."
    cd "$BASE_DIR" || exit 1
    
    # 停止并移除服务
    $DOCKER_COMPOSE_CMD down
    
    if [ $? -eq 0 ]; then
        echo "$SERVICE_NAME已停止并移除！"
    else
        echo "$SERVICE_NAME停止失败，请手动检查！"
        exit 1
    fi
    
    # 询问是否清理日志数据
    echo "是否清理日志数据? [y/N]"
    read -r answer
    if [[ "$answer" =~ ^[Yy]$ ]]; then
        echo "正在清理日志数据..."
        rm -rf "$BASE_DIR/logs"/*
        echo "日志数据已清理！"
    else
        echo "保留日志数据。"
    fi
    
    echo "$SERVICE_NAME卸载完成！"
}

# 重新加载服务配置
reload_service() {
    echo "正在重新加载$SERVICE_NAME配置..."
    
    # 检查服务是否在运行
    if ! docker ps | grep -q "$CONTAINER_NAME"; then
        echo "$SERVICE_NAME未在运行！无法重新加载配置。"
        return 1
    fi
    
    # 获取容器ID
    CONTAINER_ID=$(docker ps -q --filter "name=$CONTAINER_NAME")
    
    # 先检查Nginx配置是否正确
    echo "检查Nginx配置文件..."
    if ! docker exec "$CONTAINER_ID" nginx -t; then
        echo "Nginx配置文件存在错误，无法重新加载！请修复配置后再试。"
        return 1
    fi
    
    # 重新加载Nginx配置
    echo "重新加载Nginx配置..."
    docker exec "$CONTAINER_ID" nginx -s reload
    
    if [ $? -eq 0 ]; then
        echo "$SERVICE_NAME配置已成功重新加载！"
    else
        echo "$SERVICE_NAME配置重新加载失败，请检查日志！"
        return 1
    fi
}

# 测试配置文件语法
test_config() {
    echo "正在测试$SERVICE_NAME配置文件语法..."
    
    # 检查服务是否在运行
    if ! docker ps | grep -q "$CONTAINER_NAME"; then
        echo "$SERVICE_NAME未在运行！无法测试配置。"
        return 1
    fi
    
    # 获取容器ID
    CONTAINER_ID=$(docker ps -q --filter "name=$CONTAINER_NAME")
    
    # 测试Nginx配置文件
    echo "执行 nginx -t 命令..."
    docker exec "$CONTAINER_ID" nginx -t
    
    if [ $? -eq 0 ]; then
        echo "配置测试结果: 成功 ✓"
    else
        echo "配置测试结果: 失败 ✗"
        return 1
    fi
}

# 测试配置文件语法并显示完整配置
test_config_full() {
    echo "正在测试$SERVICE_NAME配置文件语法并显示完整配置..."
    
    # 检查服务是否在运行
    if ! docker ps | grep -q "$CONTAINER_NAME"; then
        echo "$SERVICE_NAME未在运行！无法测试配置。"
        return 1
    fi
    
    # 获取容器ID
    CONTAINER_ID=$(docker ps -q --filter "name=$CONTAINER_NAME")
    
    # 测试Nginx配置文件并显示完整配置
    echo "执行 nginx -T 命令..."
    docker exec "$CONTAINER_ID" nginx -T
    
    if [ $? -eq 0 ]; then
        echo "配置测试结果: 成功 ✓"
    else
        echo "配置测试结果: 失败 ✗"
        return 1
    fi
}

# 主函数
main() {
    # 检查参数
    if [ $# -ne 1 ]; then
        show_usage
        exit 1
    fi
    
    # 根据参数执行相应的操作
    case "$1" in
        start)
            echo "======== 开始启动$SERVICE_NAME ========"
            start_service
            echo "======== $SERVICE_NAME启动完成 ========"
            ;;
        stop)
            echo "======== 开始停止$SERVICE_NAME ========"
            stop_service
            echo "======== $SERVICE_NAME停止完成 ========"
            ;;
        restart)
            echo "======== 开始重启$SERVICE_NAME ========"
            restart_service
            echo "======== $SERVICE_NAME重启完成 ========"
            ;;
        status)
            echo "======== $SERVICE_NAME状态 ========"
            check_status
            echo "===================================="
            ;;
        undeploy)
            echo "======== 开始卸载$SERVICE_NAME ========"
            undeploy_service
            echo "======== $SERVICE_NAME卸载完成 ========"
            ;;
        reload)
            echo "======== 开始重新加载$SERVICE_NAME配置 ========"
            reload_service
            echo "======== $SERVICE_NAME配置重新加载完成 ========"
            ;;
        test)
            echo "======== 测试$SERVICE_NAME配置文件 ========"
            test_config
            echo "======== $SERVICE_NAME配置文件测试完成 ========"
            ;;
        testfull)
            echo "======== 测试$SERVICE_NAME完整配置 ========"
            test_config_full
            echo "======== $SERVICE_NAME完整配置测试完成 ========"
            ;;
        *)
            show_usage
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@" 