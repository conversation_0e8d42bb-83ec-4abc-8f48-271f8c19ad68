# 故障排除指南

## Docker容器网络问题

### 问题描述：Docker容器无法启动，提示网络错误

在部署过程中，可能会遇到以下错误：

```
Cannot connect to the Docker daemon at unix:///var/run/docker.sock. Is the docker daemon running?
```

或者容器无法正常启动，`docker0` 网桥显示为 `DOWN` 状态。

### 原因分析

这通常是由以下原因导致：

1. Docker网络（通常是**********/16网段）与宿主机现有网络冲突
2. Docker服务启动时网络初始化失败
3. 防火墙规则阻止了Docker网络通信

### 解决方案

1. 检查网络状态：
```bash
ip addr show docker0
```

2. 如果docker0接口显示为DOWN状态，执行以下步骤：

```bash
# 停止docker服务
systemctl stop docker

# 删除docker0接口
ip link delete docker0

# 修改Docker默认网段（如果需要）
cat > /etc/docker/daemon.json <<EOF
{
    "bip": "***********/24",
    "default-address-pools": [
        {
            "base": "***********/16",
            "size": 24
        }
    ]
}
EOF

# 重启docker服务
systemctl restart docker
```

3. 验证解决方案：
```bash
# 检查docker0接口状态
ip addr show docker0

# 检查docker服务状态
systemctl status docker
```

### 预防措施

1. 在部署前检查网络规划，避免网段冲突
2. 记录在`daemon.json`中配置的网段信息
3. 在防火墙配置中预留Docker使用的网段

### 相关文档

- [Docker Network Overview](https://docs.docker.com/network/)
- [Customize the docker0 bridge](https://docs.docker.com/network/bridge/#configure-the-default-bridge-network)
- [Docker daemon configuration](https://docs.docker.com/engine/reference/commandline/dockerd/#daemon-configuration-file)

### 注意事项

1. 修改Docker默认网段时，确保新的网段不会与现有网络冲突
2. 如果有运行中的容器，需要重新创建容器才能应用新的网络配置
3. 建议在修改网络配置前备份重要的容器数据 