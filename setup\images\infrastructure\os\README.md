# 操作系统预打包镜像

本目录存放各种预构建的操作系统基础镜像，用于离线部署环境。

## 目录结构

- `alpine/`: Alpine Linux预构建镜像
- `ubuntu/`: Ubuntu预构建镜像
- `centos/`: CentOS预构建镜像
- `rhel/`: Red Hat Enterprise Linux预构建镜像
- `openeuler/`: openEuler预构建镜像

## 使用说明

这些预构建镜像用于在无网络环境下快速部署容器化应用。在离线环境中，可通过以下命令加载这些镜像：

```bash
# 加载Ubuntu镜像
docker load -i ubuntu/ubuntu-22.04.tar.gz

# 加载Alpine镜像
docker load -i alpine/alpine-3.17.tar.gz

# 加载CentOS镜像
docker load -i centos/centos-7.tar.gz
```

## 镜像构建

这些预构建镜像由`docker/infrastructure/os`目录下的构建脚本生成。镜像构建后会自动复制到此目录进行归档。

## 镜像版本

每个操作系统子目录中包含不同版本的基础镜像，命名格式为：
`<os>-<version>-<arch>.tar.gz`

例如：
- `ubuntu-22.04-amd64.tar.gz`
- `alpine-3.17-amd64.tar.gz`
- `centos-7-amd64.tar.gz` 