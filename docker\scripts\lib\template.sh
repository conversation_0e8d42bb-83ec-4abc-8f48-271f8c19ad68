#!/bin/bash

# 导入通用函数库
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${SCRIPT_DIR}/common.sh"

# 替换模板中的变量
# 用法: replace_template_vars "模板文件" "输出文件" "变量文件"
replace_template_vars() {
    local template_file="$1"
    local output_file="$2"
    local vars_file="$3"
    
    if [ ! -f "$template_file" ]; then
        log_error "模板文件不存在: $template_file"
        return 1
    fi
    
    if [ ! -f "$vars_file" ]; then
        log_error "变量文件不存在: $vars_file"
        return 1
    fi
    
    # 创建临时文件
    local temp_file
    temp_file=$(mktemp)
    
    # 复制模板到临时文件
    cp "$template_file" "$temp_file"
    
    # 读取变量文件并替换
    while IFS='=' read -r key value; do
        # 跳过空行和注释
        [[ -z "$key" || "$key" =~ ^[[:space:]]*# ]] && continue
        
        # 清理key和value
        key=$(echo "$key" | tr -d '[:space:]')
        value=$(echo "$value" | tr -d '[:space:]')
        
        # 替换模板中的变量
        sed -i "s|{{[[:space:]]*${key}[[:space:]]*}}|${value}|g" "$temp_file"
    done < "$vars_file"
    
    # 检查是否还有未替换的变量
    if grep -q '{{[^}]*}}' "$temp_file"; then
        log_warn "以下变量未被替换:"
        grep -o '{{[^}]*}}' "$temp_file" | sort -u
    fi
    
    # 移动到目标文件
    mv "$temp_file" "$output_file"
    
    log_info "模板处理完成: $output_file"
    return 0
}

# 从环境变量替换模板变量
# 用法: replace_template_from_env "模板文件" "输出文件" "变量前缀"
replace_template_from_env() {
    local template_file="$1"
    local output_file="$2"
    local prefix="${3:-}"
    
    if [ ! -f "$template_file" ]; then
        log_error "模板文件不存在: $template_file"
        return 1
    fi
    
    # 创建临时文件
    local temp_file
    temp_file=$(mktemp)
    
    # 复制模板到临时文件
    cp "$template_file" "$temp_file"
    
    # 获取所有环境变量
    local env_vars
    if [ -n "$prefix" ]; then
        env_vars=$(env | grep "^${prefix}")
    else
        env_vars=$(env)
    fi
    
    # 替换变量
    while IFS='=' read -r key value; do
        if [ -n "$prefix" ]; then
            # 移除前缀
            key=${key#$prefix}
        fi
        # 替换模板中的变量
        sed -i "s|{{[[:space:]]*${key}[[:space:]]*}}|${value}|g" "$temp_file"
    done <<< "$env_vars"
    
    # 检查是否还有未替换的变量
    if grep -q '{{[^}]*}}' "$temp_file"; then
        log_warn "以下变量未被替换:"
        grep -o '{{[^}]*}}' "$temp_file" | sort -u
    fi
    
    # 移动到目标文件
    mv "$temp_file" "$output_file"
    
    log_info "模板处理完成: $output_file"
    return 0
}

# 验证模板文件
# 用法: validate_template "模板文件"
validate_template() {
    local template_file="$1"
    
    if [ ! -f "$template_file" ]; then
        log_error "模板文件不存在: $template_file"
        return 1
    fi
    
    # 检查模板语法
    local variables
    variables=$(grep -o '{{[^}]*}}' "$template_file" | sort -u)
    
    if [ -z "$variables" ]; then
        log_warn "模板中没有找到变量: $template_file"
        return 0
    fi
    
    log_info "模板变量列表:"
    while read -r var; do
        # 清理变量名
        var=${var#"{{"}
        var=${var%"}}"}
        var=$(echo "$var" | tr -d '[:space:]')
        echo "  - $var"
    done <<< "$variables"
    
    return 0
}

# 生成变量文件模板
# 用法: generate_vars_template "模板文件" "输出变量文件"
generate_vars_template() {
    local template_file="$1"
    local vars_file="$2"
    
    if [ ! -f "$template_file" ]; then
        log_error "模板文件不存在: $template_file"
        return 1
    fi
    
    # 提取所有变量并生成变量文件
    {
        echo "# 自动生成的变量文件"
        echo "# 基于模板: $template_file"
        echo "# 生成时间: $(date)"
        echo
        
        grep -o '{{[^}]*}}' "$template_file" | sort -u | while read -r var; do
            # 清理变量名
            var=${var#"{{"}
            var=${var%"}}"}
            var=$(echo "$var" | tr -d '[:space:]')
            echo "${var}="
        done
    } > "$vars_file"
    
    log_info "变量文件模板已生成: $vars_file"
    return 0
}

# 合并多个模板
# 用法: merge_templates "输出文件" "模板文件1" "模板文件2" ...
merge_templates() {
    local output_file="$1"
    shift
    local template_files=("$@")
    
    # 创建临时文件
    local temp_file
    temp_file=$(mktemp)
    
    # 合并所有模板
    for template in "${template_files[@]}"; do
        if [ ! -f "$template" ]; then
            log_error "模板文件不存在: $template"
            rm -f "$temp_file"
            return 1
        fi
        
        cat "$template" >> "$temp_file"
        echo >> "$temp_file"  # 添加空行分隔
    done
    
    # 移动到目标文件
    mv "$temp_file" "$output_file"
    
    log_info "模板合并完成: $output_file"
    return 0
}
