#!/bin/bash
# MySQL 状态检查功能
# 包含服务状态和复制状态相关功能

# 引入依赖
source "$(dirname "${BASH_SOURCE[0]}")/utils.sh"

# 检查服务状态
check_status() {
  local product="$1"
  log_info "检查 $product MySQL 服务状态..."
  
  # 检查当前环境中存在的配置文件
  local master1_compose_file=$(get_master1_compose_file "$product")
  local master2_compose_file=$(get_master2_compose_file "$product")
  local is_master1_local=false
  local is_master2_local=false
  
  if [ -f "$master1_compose_file" ]; then
    is_master1_local=true
  fi
  
  if [ -f "$master2_compose_file" ]; then
    is_master2_local=true
  fi
  
  if [ "$is_master1_local" = "false" ] && [ "$is_master2_local" = "false" ]; then
    log_error "在当前主机上未找到任何MySQL节点的配置文件"
    return 1
  fi
  
  local master1=$(get_master1_name "$product")
  local master2=$(get_master2_name "$product")
  
  log_info "本地节点状态:"
  
  if [ "$is_master1_local" = "true" ]; then
    log_info "主节点1状态:"
    (cd "$COMPOSE_DIR" && docker-compose -f $(basename "$master1_compose_file") ps)
    local master1_running=$(is_container_running "$master1" && echo "yes" || echo "no")
    
    if [ "$master1_running" = "no" ]; then
      log_warning "本地主节点1 ($master1) 未运行"
    else
      log_success "本地主节点1 ($master1) 正常运行"
    fi
  fi
  
  if [ "$is_master2_local" = "true" ]; then
    log_info "主节点2状态:"
    (cd "$COMPOSE_DIR" && docker-compose -f $(basename "$master2_compose_file") ps)
    local master2_running=$(is_container_running "$master2" && echo "yes" || echo "no")
    
    if [ "$master2_running" = "no" ]; then
      log_warning "本地主节点2 ($master2) 未运行"
    else
      log_success "本地主节点2 ($master2) 正常运行"
    fi
  fi
  
  # 提示检查远程节点
  if [ "$is_master1_local" = "true" ] && [ "$is_master2_local" = "false" ]; then
    log_info "远程主节点2状态需要在远程主机上检查"
  elif [ "$is_master1_local" = "false" ] && [ "$is_master2_local" = "true" ]; then
    log_info "远程主节点1状态需要在远程主机上检查"
  fi
  
  return 0
}

# 检查复制状态
check_replication() {
  local product="$1"
  log_info "检查 $product MySQL 主主复制状态..."
  
  # 检查当前环境中存在的配置文件和容器
  local master1=$(get_master1_name "$product")
  local master2=$(get_master2_name "$product")
  local running=0
  local password=$(get_mysql_root_password)
  
  # 检查主节点1
  if is_container_running "$master1"; then
    log_info "检查 $master1 的复制状态..."
    echo "-------- $master1 复制状态 --------"
    docker exec $master1 mysql -uroot -p"$password" -e "SHOW SLAVE STATUS\G" | grep -E "Slave_IO_Running|Slave_SQL_Running|Seconds_Behind_Master|Last_IO_Error|Last_SQL_Error"
    echo "------------------------------------"
    ((running++))
  else
    log_warning "$master1 未运行，无法检查复制状态"
  fi
  
  # 检查主节点2
  if is_container_running "$master2"; then
    log_info "检查 $master2 的复制状态..."
    echo "-------- $master2 复制状态 --------"
    docker exec $master2 mysql -uroot -p"$password" -e "SHOW SLAVE STATUS\G" | grep -E "Slave_IO_Running|Slave_SQL_Running|Seconds_Behind_Master|Last_IO_Error|Last_SQL_Error"
    echo "------------------------------------"
    ((running++))
  else
    log_warning "$master2 未运行，无法检查复制状态"
  fi
  
  if [ "$running" -eq 0 ]; then
    log_error "本地没有运行的MySQL节点，无法检查复制状态"
    log_info "提示：要检查完整的主主复制状态，需要在两台主机上分别运行此脚本"
    return 1
  else
    log_info "提示：主主复制需要检查两个节点。如果只看到一个节点状态，请在另一台主机上运行此脚本检查另一个节点"
  fi
  
  return 0
}

# 修复复制错误
fix_replication() {
  local product="$1"
  log_info "修复复制错误..."
  
  # 检查哪些节点在本地可用
  local master1=$(get_master1_name "$product")
  local master2=$(get_master2_name "$product")
  local available_nodes=()
  
  if is_container_running "$master1"; then
    available_nodes+=("$master1")
  fi
  
  if is_container_running "$master2"; then
    available_nodes+=("$master2")
  fi
  
  if [ ${#available_nodes[@]} -eq 0 ]; then
    log_error "没有运行的MySQL节点可供操作"
    return 1
  fi
  
  # 如果只有一个节点，直接使用它
  local node=""
  if [ ${#available_nodes[@]} -eq 1 ]; then
    node=${available_nodes[0]}
    log_info "将对节点 $node 进行操作"
  else
    # 如果有多个节点，让用户选择
    node=$(select_node "$product")
    if [ $? -ne 0 ]; then
      return 1
    fi
  fi
  
  # 检查复制状态
  log_info "检查节点 $node 的复制状态..."
  local password=$(get_mysql_root_password)
  local slave_status=$(docker exec $node mysql -uroot -p"$password" -e "SHOW SLAVE STATUS\G")
  echo "$slave_status" | grep -E "Slave_IO_Running|Slave_SQL_Running|Seconds_Behind_Master|Last_IO_Error|Last_SQL_Error"
  
  # 选择修复方法
  echo "选择修复方法:"
  echo "1) 跳过一个错误 (SQL_SLAVE_SKIP_COUNTER=1)"
  echo "2) 重置复制关系"
  echo "3) 查看详细复制状态"
  echo -n "请选择 [1-3]: "
  local fix_choice
  read fix_choice
  
  case $fix_choice in
    1)
      log_info "尝试跳过错误..."
      docker exec $node mysql -uroot -p"$password" -e "STOP SLAVE; SET GLOBAL SQL_SLAVE_SKIP_COUNTER = 1; START SLAVE;"
      if [ $? -eq 0 ]; then
        log_success "已跳过错误并重启复制"
        sleep 2
        docker exec $node mysql -uroot -p"$password" -e "SHOW SLAVE STATUS\G" | grep -E "Slave_IO_Running|Slave_SQL_Running|Seconds_Behind_Master|Last_IO_Error|Last_SQL_Error"
      else
        log_error "跳过错误失败"
      fi
      ;;
    2)
      log_warning "重置复制关系需要重新配置主主复制。此操作复杂且风险高，建议联系专业DBA。"
      if ! confirm_action "是否强制执行此操作?"; then
        log_info "操作已取消"
        return 0
      fi
      
      log_info "重置复制关系..."
      docker exec $node mysql -uroot -p"$password" -e "STOP SLAVE; RESET SLAVE ALL;"
      log_warning "已重置复制关系，需要手动重新配置复制关系。请使用 setup-master-master.sh 脚本重新配置。"
      ;;
    3)
      log_info "显示详细复制状态..."
      docker exec $node mysql -uroot -p"$password" -e "SHOW SLAVE STATUS\G"
      ;;
    *)
      log_error "无效的选择"
      return 1
      ;;
  esac
  
  return 0
} 