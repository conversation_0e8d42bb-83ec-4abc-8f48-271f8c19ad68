# MySQL 部署说明

## 目录结构

MySQL 部署目录包含以下结构：

```
infrastructure/
└── database/
    └── mysql/
        ├── config/              # 配置文件目录
        │   ├── my.cnf           # 通用配置
        │   ├── master1/         # 主节点1配置
        │   └── master2/         # 主节点2配置
        ├── data/                # 数据存储目录
        │   └── <产品名>/        # 按产品隔离
        │       ├── master1/     # 主节点1数据
        │       └── master2/     # 主节点2数据
        ├── init/                # 初始化脚本
        │   └── <产品名>/        # 按产品隔离
        │       ├── 0_initDB.sql     # 数据库初始化
        │       ├── 1_initUser.sql   # 用户初始化
        │       └── 2_addAdmin.sql   # 管理员账户添加
        ├── scripts/             # 操作脚本
        │   ├── deploy.sh        # 部署脚本
        │   ├── maintenance.sh   # 维护脚本
        │   ├── monitor.sh       # 监控脚本
        │   ├── backup.sh        # 备份脚本
        │   ├── restore.sh       # 恢复脚本
        │   └── setup-master-master.sh  # 主主复制配置
        ├── backups/             # 备份存储
        │   └── <产品名>/        # 按产品隔离
        ├── logs/                # 日志目录
        │   └── <产品名>/        # 按产品隔离
        │       ├── master1/     # 主节点1日志
        │       └── master2/     # 主节点2日志
        ├── secrets/             # 敏感信息
        │   ├── mysql_root_password.txt      # root密码
        │   └── replication_password.txt     # 复制用户密码
        └── compose/             # Docker Compose配置
            └── <产品名>-mysql.yml  # 产品对应的编排文件
```

## 目录结构设计说明

本项目采用"服务模块化"原则组织目录结构，主要体现在以下几个方面：

1. **服务独立性**
   - `infrastructure/database/mysql/` 包含所有 MySQL 相关文件，包括配置、数据、脚本和编排文件
   - 形成完整的独立服务单元，便于单独部署和维护
   
2. **目录结构设计优势**
   - **独立性增强**：所有MySQL相关文件集中在单一目录树下，无需跨目录操作
   - **路径引用简化**：相对路径更加简单直观，减少出错可能性
   - **部署便利性**：支持将整个MySQL目录作为独立单元部署
   - **维护性提升**：配置变更和编排调整可以同步进行

3. **与旧结构的兼容**
   - 部署脚本会检查并处理可能的兼容性问题
   - 保持主要操作接口不变，确保平滑过渡

## 部署架构

本MySQL部署采用主主复制（Master-Master Replication）架构，具有以下特点：

1. **主主复制结构**：两个MySQL实例互为主从，实现数据的双向同步。
2. **不同物理宿主机部署**：两个主节点分别部署在不同的物理服务器上，提高可用性。
3. **统一端口映射**：每个产品在所有宿主机上使用相同的端口，简化应用配置。
4. **自动解决冲突**：通过auto_increment_increment和auto_increment_offset设置避免主键冲突。
5. **产品隔离**：不同产品使用独立的数据库实例，确保安全隔离。
6. **独立配置文件**：每个节点使用独立的Docker Compose配置文件，更符合分布式部署场景。

### 配置文件设计

为适应分布式部署场景，MySQL服务采用独立的Docker Compose文件进行管理：

1. **独立节点配置文件**：
   - `<产品名>-mysql-master1.yml`：专门用于配置和管理主节点1
   - `<产品名>-mysql-master2.yml`：专门用于配置和管理主节点2
   - `setup-replication.yml`：用于配置主主复制关系

2. **好处**：
   - 精确对应宿主机：每台宿主机只需要关心自己的配置文件
   - 避免容器冲突：不再创建不必要的容器
   - 配置独立性：各节点配置完全隔离，更贴合实际部署
   - 维护简化：修改一个节点不会影响另一个节点的配置

### 端口分配方案

每个产品使用预定义的端口号，在所有宿主机上保持一致：

| 产品名称 | MySQL端口 | 说明 |
|---------|----------|------|
| cloudkey | 13306 | 云密钥系统 |
| eseal | 13316 | 电子签章系统 |
| quicksign | 13326 | 速签通系统 |

这种设计使得应用程序可以通过访问任一宿主机的相同端口来实现高可用，同时避免了不同产品之间的端口冲突。

## 部署步骤

### 1. 环境准备

1. 安装Docker和Docker Compose
2. 确保宿主机具有足够的存储空间
3. 确保宿主机间网络互通，并开放MySQL端口

### 2. 配置文件准备

1. 准备主节点的配置文件
   ```bash
   mkdir -p infrastructure/database/mysql/config/{master1,master2}
   # 复制配置文件模板到相应目录
   ```

2. 准备初始化脚本
   ```bash
   mkdir -p infrastructure/database/mysql/init/<产品名>
   # 添加数据库初始化SQL脚本
   ```

3. 设置密码文件
   ```bash
   mkdir -p infrastructure/database/mysql/secrets
   echo "your_strong_password" > infrastructure/database/mysql/secrets/mysql_root_password.txt
   echo "replication_password" > infrastructure/database/mysql/secrets/replication_password.txt
   chmod 600 infrastructure/database/mysql/secrets/*.txt
   ```

### 3. 端口开放

部署脚本会自动检测操作系统类型并帮助开放MySQL端口：

- **OpenEuler 22.03**：使用firewalld开放端口
  ```bash
  # 手动执行以下命令
  sudo firewall-cmd --zone=public --add-port=<MYSQL_PORT>/tcp --permanent
  sudo firewall-cmd --reload
  ```

- **Ubuntu 22.04**：使用ufw开放端口
  ```bash
  # 手动执行以下命令
  sudo ufw allow <MYSQL_PORT>/tcp
  ```

不同产品对应的默认端口：
- cloudkey: 13306
- eseal: 13316
- quicksign: 13326

### 4. 部署服务

使用部署脚本自动完成部署：

```bash
# 进入脚本目录
cd infrastructure/database/mysql/scripts

# 设置脚本执行权限
chmod +x *.sh

# 部署指定产品的MySQL服务
./deploy.sh <产品名>  # 例如: ./deploy.sh cloudkey 或 ./deploy.sh quicksign
```

部署脚本将自动：
- 创建必要的目录结构
- 检查配置文件和环境
- 创建独立的节点配置文件
- 启动MySQL服务
- 配置主主复制

#### 4.1 主主复制部署说明

由于主主复制架构要求两个MySQL节点部署在不同的宿主机上，部署时需要在每台宿主机上分别执行部署命令，指定对应的节点角色：

**在第一台宿主机上部署master1节点：**

```bash
cd infrastructure/database/mysql/scripts
./deploy.sh cloudkey master1  # 第二个参数指定仅部署master1节点
```

**在第二台宿主机上部署master2节点：**

```bash
cd infrastructure/database/mysql/scripts
./deploy.sh cloudkey master2  # 第二个参数指定仅部署master2节点
```

#### 4.2 MySQL复制配置说明

在完成基本部署后，需要配置MySQL节点之间的复制关系。系统提供了`setup-mysql-replication.sh`脚本用于配置不同的复制模式：

##### 主主复制（Master-Master Replication）配置

主主复制模式下，两个MySQL节点互为主从关系，任一节点的数据变更都会同步到另一个节点。**这种模式下，两个MySQL节点必须部署在不同的物理宿主机上**。

在其中一台宿主机（如master节点所在服务器）上执行以下命令：

```bash
cd infrastructure/database/mysql/scripts

# 语法: ./setup-mysql-replication.sh [产品名] [主节点IP] [从节点IP] [当前节点] [复制模式]
./setup-mysql-replication.sh cloudkey *************** *************** master master-master
```

参数说明：
- 产品名：要配置的产品名称（如cloudkey、eseal等）
- 主节点IP：主节点所在服务器IP地址
- 从节点IP：从节点所在服务器IP地址
- 当前节点：当前执行脚本的节点角色（master或slave）
- 复制模式：指定为`master-master`表示配置主主复制

**主主复制注意事项：**

1. 主主复制需要在两台不同的物理服务器上部署MySQL节点
2. 脚本只需要在其中一台服务器上执行一次
3. 两台服务器之间必须能够通过网络互相访问对方的MySQL端口
4. 自增ID已配置为避免冲突（主节点1使用奇数ID，主节点2使用偶数ID）
5. 应避免在两个节点上同时修改同一条数据，以防止复制冲突

##### 主从复制（Master-Slave Replication）配置

主从复制模式下，一个节点作为主节点（处理所有写操作），另一个节点作为从节点（处理读操作或作为备份）。

在主节点所在服务器上执行以下命令：

```bash
cd infrastructure/database/mysql/scripts

# 语法: ./setup-mysql-replication.sh [产品名] [主节点IP] [从节点IP] [当前节点] [复制模式]
./setup-mysql-replication.sh cloudkey *************** *************** master master-slave
```

参数说明与主主复制相同，但复制模式指定为`master-slave`。

**主从复制注意事项：**

1. 主从复制模式下，所有写操作必须在主节点上执行
2. 从节点适合用于读操作分担、数据备份或故障转移准备
3. 如需将从节点转换为主节点（故障转移），请使用维护脚本中的对应功能

##### 复制状态验证

配置完成后，可以通过以下命令验证复制状态：

```bash
# 检查复制状态
cd infrastructure/database/mysql/scripts
./maintenance.sh cloudkey  # 选择"检查复制状态"选项

# 或直接使用MySQL命令检查
docker exec -it cloudkey-mysql-master1 mysql -uroot -p$(cat ../secrets/mysql_root_password.txt) -e "SHOW SLAVE STATUS\G"
```

正常状态下，应看到`Slave_IO_Running: Yes`和`Slave_SQL_Running: Yes`。

### 5. 验证部署

1. 检查服务状态：
   ```bash
   cd infrastructure/database/mysql/scripts
   ./maintenance.sh cloudkey  # 进入维护菜单并选择"检查服务状态"选项
   ```
   
   或直接使用Docker Compose命令：
   ```bash
   # 主节点1状态
   cd infrastructure/database/mysql/compose
   docker-compose -f cloudkey-mysql-master1.yml ps
   
   # 主节点2状态
   docker-compose -f cloudkey-mysql-master2.yml ps
   ```

2. 检查主主复制状态：
   ```bash
   # 在主节点1上查看复制状态
   docker exec -it cloudkey-mysql-master1 mysql -uroot -p$(cat ../../database/mysql/secrets/mysql_root_password.txt) -e "SHOW SLAVE STATUS\G"
   
   # 在主节点2上查看复制状态
   docker exec -it cloudkey-mysql-master2 mysql -uroot -p$(cat ../../database/mysql/secrets/mysql_root_password.txt) -e "SHOW SLAVE STATUS\G"
   ```

## 运维操作

### 数据库备份与恢复

#### 自动备份

系统配置了每日凌晨2点的自动备份任务，备份文件保存在 `backups/<产品名>/` 目录下。

#### 手动备份

```bash
# 使用维护脚本执行手动备份
cd infrastructure/database/mysql/scripts
./maintenance.sh cloudkey  # 替换为目标产品
# 在交互菜单中选择"执行手动备份"选项
```

#### 数据恢复

```bash
# 使用维护脚本执行数据恢复
cd infrastructure/database/mysql/scripts
./maintenance.sh cloudkey  # 替换为目标产品
# 在交互菜单中选择"恢复数据库"选项
```

### 检查复制状态

```bash
# 使用维护脚本检查复制状态
cd infrastructure/database/mysql/scripts
./maintenance.sh cloudkey  # 替换为目标产品
# 在交互菜单中选择"检查主主复制状态"选项
```

### 系统监控

```bash
# 使用监控脚本生成系统健康报告
cd infrastructure/database/mysql/scripts
./monitor.sh cloudkey  # 替换为目标产品
```

## 多产品支持

本部署方案支持多个产品同时使用，各产品之间完全隔离：

1. 不同产品使用不同的Docker Compose配置文件
2. 不同产品使用不同的数据目录和备份目录
3. 不同产品使用不同的端口避免冲突

添加新产品步骤：

1. 复制现有产品的配置文件和初始化脚本
   ```bash
   cp -r infrastructure/database/mysql/init/cloudkey infrastructure/database/mysql/init/new_product
   ```

2. 修改新产品的初始化脚本
   ```bash
   vi infrastructure/database/mysql/init/new_product/0_initDB.sql
   vi infrastructure/database/mysql/init/new_product/1_initUser.sql
   vi infrastructure/database/mysql/init/new_product/2_addAdmin.sql
   ```

3. 使用部署脚本部署新产品（脚本会自动生成配置文件）
   ```bash
   ./deploy.sh new_product
   ```

4. 部署脚本将自动创建以下配置文件：
   - `new_product-mysql-master1.yml` - 主节点1配置
   - `new_product-mysql-master2.yml` - 主节点2配置
   
   可以根据需要修改这些配置文件以自定义产品特定设置。

## 高可用配置

### 主主部署架构

当前部署采用主主复制架构，两个主节点位于不同的物理服务器上，通过相同的端口映射实现：

1. 宿主机A上的master1节点和宿主机B上的master2节点互为主从
2. 两台宿主机都将MySQL端口映射为相同的端口号（根据产品不同而不同）
3. 应用程序可配置多个数据库连接地址，实现高可用

### 负载均衡与故障转移

推荐使用以下方案实现透明的高可用：

1. **Keepalived方案（推荐）**：
   - 在两台宿主机上安装Keepalived
   - 配置虚拟IP(VIP)随主节点浮动
   - 应用程序仅需连接VIP地址

2. **应用程序多连接池**：
   - 应用程序配置连接到两台宿主机的连接池
   - 设置失败检测和自动切换

## 安全措施

当前部署已实施以下安全措施：

1. 密码通过Docker Secrets管理，避免明文存储
2. 数据库文件权限控制，仅允许必要的访问
3. 仅开放必要的网络端口
4. 不同产品使用独立的数据库实例，避免交叉访问

### 密码管理建议

1. 定期更换root密码和复制用户密码
2. 对密码文件设置严格的访问控制
3. 考虑使用密码管理系统或密钥保管库

## 故障排查

常见问题的排查和解决方法可参考故障排除指南：

```bash
cat infrastructure/database/mysql/scripts/troubleshooting.md
```

主要包括以下内容：
- 服务启动问题
- 主主复制问题
- 多宿主机部署问题
- 性能问题
- 连接问题
- 数据问题

## 维护计划

建议定期执行以下维护活动：

1. **每日检查**：
   - 复制状态检查
   - 备份完成情况
   - 磁盘空间使用情况

2. **每周检查**：
   - 数据库性能分析
   - 慢查询分析
   - 数据文件碎片整理

3. **每月检查**：
   - 全面系统健康检查
   - 备份恢复测试
   - 安全配置审计

## 技术支持

如遇到无法解决的问题，请联系系统管理员。 

## 脚本说明

本部署方案包含多个脚本文件，用于自动化MySQL部署和维护操作。以下是对各个脚本的详细说明及执行顺序。

### 脚本文件列表及用途

| 脚本文件 | 用途 | 描述 |
|---------|------|------|
| `deploy.sh` | 初始部署 | 完成MySQL服务的初始部署，包括创建目录、检查配置、开放端口和启动服务 |
| `setup-master-master.sh` | 主主复制配置 | 配置两个MySQL节点之间的主主复制关系，通常由`deploy.sh`自动调用 |
| `maintenance.sh` | 日常维护 | 提供交互式菜单进行日常维护操作，包括检查复制状态、修复复制错误等 |
| `monitor.sh` | 状态监控 | 监控MySQL服务状态并生成健康报告，可配置为定时任务定期执行 |
| `backup.sh` | 数据备份 | 执行数据库备份操作，支持全量备份和增量备份，通常由定时任务触发 |
| `restore.sh` | 数据恢复 | 从备份文件恢复数据库，支持选择特定时间点的备份进行恢复 |

### 脚本执行顺序

在典型的MySQL部署和维护流程中，脚本的执行顺序如下：

1. **初始部署阶段**：
   ```bash
   # 1. 首先执行部署脚本
   cd infrastructure/database/mysql/scripts
   ./deploy.sh <产品名>  # 例如: ./deploy.sh cloudkey
   
   # 部署脚本会自动执行以下步骤:
   # - 检查环境前提条件
   # - 创建必要的目录结构
   # - 设置脚本执行权限
   # - 检查配置文件
   # - 询问是否开放端口（根据操作系统自动选择方法）
   # - 启动MySQL服务
   # - 等待服务启动完成
   # - 检查服务状态
   # - 显示部署信息
   
   # 2. deploy.sh 会在适当的时候自动调用 setup-master-master.sh 配置主主复制
   # 通常无需手动执行此脚本，但如需单独配置复制关系，可执行:
   ./setup-master-master.sh <产品名>
   ```

2. **日常运维阶段**：
   ```bash
   # 1. 定期执行监控脚本检查系统状态
   ./monitor.sh <产品名>  # 例如: ./monitor.sh cloudkey
   
   # 2. 使用维护脚本执行维护操作
   ./maintenance.sh <产品名>
   
   # 3. 备份脚本通常配置为定时任务自动执行
   # 但也可以手动触发:
   ./backup.sh <产品名>
   
   # 4. 需要恢复数据时执行:
   ./restore.sh <产品名> [备份文件路径]
   ```

### 脚本详细用法

#### 部署脚本 (deploy.sh)

```bash
./deploy.sh <产品名>
```

参数说明:
- `<产品名>`: 要部署的产品名称，如 cloudkey、eseal 或 quicksign。不指定时默认为 cloudkey。

功能:
- 检测操作系统类型（支持OpenEuler 22.03和Ubuntu 22.04）
- 根据产品名称分配对应端口
- 创建目录结构
- 检查并开放端口（根据操作系统类型）
- 启动MySQL服务
- 配置主主复制关系

#### 维护脚本 (maintenance.sh)

```bash
./maintenance.sh <产品名>
```

功能:
- 提供交互式菜单，包含以下选项:
  - 检查主主复制状态
  - 修复复制错误
  - 执行手动备份
  - 恢复数据库
  - 重置复制关系
  - 查看服务状态
  - 查看日志信息

#### 监控脚本 (monitor.sh)

```bash
./monitor.sh <产品名> [报告类型]
```

参数说明:
- `<产品名>`: 要监控的产品名称
- `[报告类型]`: 可选参数，指定生成的报告类型（简报、详细或完整）

功能:
- 检查MySQL服务状态
- 监控主主复制状态
- 检查资源使用情况
- 生成健康报告
- 检查备份状态

#### 备份脚本 (backup.sh)

```bash
./backup.sh <产品名> [备份类型]
```

参数说明:
- `<产品名>`: 要备份的产品名称
- `[备份类型]`: 可选参数，指定备份类型（full: 全量备份，incremental: 增量备份）。不指定时默认为全量备份。

功能:
- 执行全量或增量备份
- 管理备份文件存储
- 删除过期备份
- 记录备份日志

#### 恢复脚本 (restore.sh)

```bash
./restore.sh <产品名> [备份文件路径]
```

参数说明:
- `<产品名>`: 要恢复的产品名称
- `[备份文件路径]`: 可选参数，指定要恢复的备份文件路径。不指定时会列出可用备份供选择。

功能:
- 从指定备份文件恢复数据库
- 恢复完成后自动重建主主复制关系
- 提供恢复前的数据校验

#### 主主复制配置脚本 (setup-master-master.sh)

```bash
./setup-master-master.sh <产品名>
```

功能:
- 配置两个MySQL节点间的主主复制关系
- 创建复制用户
- 设置复制参数
- 初始化复制过程
- 验证复制状态

### 日常维护建议

1. **日常检查**:
   ```bash
   # 每日执行监控脚本检查系统健康状态
   ./monitor.sh <产品名>
   ```

2. **定期备份**:
   ```bash
   # 配置cron任务自动执行备份
   0 2 * * * cd /path/to/scripts && ./backup.sh <产品名> > /dev/null 2>&1
   ```

3. **复制状态检查**:
   ```bash
   # 定期检查复制状态
   ./maintenance.sh <产品名>
   # 在菜单中选择"检查主主复制状态"选项
   ```

通过合理使用这些脚本，可以实现MySQL服务的高效部署和维护，保障系统的稳定性和可靠性。 

## 脚本使用指南

MySQL部署包含一系列脚本，用于简化各种操作和维护任务。所有脚本位于`infrastructure/database/mysql/scripts/`目录下。

### 脚本概述

| 脚本名称 | 主要功能 | 适用场景 |
|---------|---------|---------|
| deploy.sh | 部署和初始化MySQL服务 | 首次部署或重建MySQL环境 |
| maintenance.sh | 日常维护操作（启动/停止/重启等） | 日常运维和服务管理 |
| uninstall.sh | 卸载MySQL服务及清理资源 | 需要完全移除MySQL服务时 |
| monitor.sh | 监控MySQL服务状态和性能 | 性能分析和健康检查 |
| backup.sh | 执行数据库备份 | 数据安全和定期备份 |
| restore.sh | 从备份恢复数据库 | 数据恢复和灾难恢复 |
| setup-master-master.sh | 配置主主复制 | 跨服务器部署主主复制架构 |
| common.sh | 共享函数和变量库 | 被其他脚本引用，不直接执行 |

### 脚本详细说明

#### 1. deploy.sh - 部署脚本

**用途**: 部署和初始化MySQL服务，包括创建目录结构、配置文件和启动容器。

**用法**:
```bash
./deploy.sh [产品名] [节点角色]
```

**参数**:
- `产品名`: 要部署的产品名称，可选值：cloudkey, eseal, quicksign（默认：cloudkey）
- `节点角色`: 要部署的节点角色，可选值：master1, master2, both（默认：both）

**示例**:
```bash
./deploy.sh cloudkey both      # 部署云密钥系统的两个主节点（适用于单机测试环境）
./deploy.sh eseal master1      # 仅部署电子签章系统的主节点1
./deploy.sh quicksign master2  # 仅部署快捷签系统的主节点2
```

**特点**:
- 自动检测环境并验证依赖项
- 创建必要的目录结构和配置
- 根据需要开放防火墙端口
- 自动启动指定的MySQL服务
- 部署完成后显示操作指南

#### 2. maintenance.sh - 维护脚本

**用途**: 提供日常维护操作的交互式菜单，简化MySQL服务的维护任务。

**用法**:
```bash
./maintenance.sh [产品名] [选项]
```

**参数**:
- `产品名`: 要维护的产品名称（默认：cloudkey）

**选项**:
- `-h, --help`: 显示帮助信息

**功能菜单**:
- 启动服务
- 停止服务
- 重启服务
- 检查服务状态
- 检查主主复制状态
- 执行手动备份
- 恢复数据库
- 查看系统资源使用
- 修复复制错误
- 更新配置

**示例**:
```bash
./maintenance.sh cloudkey  # 维护云密钥系统的MySQL服务
./maintenance.sh eseal     # 维护电子签章系统的MySQL服务
```

#### 3. uninstall.sh - 卸载脚本

**用途**: 卸载MySQL服务，删除容器并可选择性地清理数据目录。

**用法**:
```bash
./uninstall.sh <产品名称> [master1|master2] [--remove-data|--keep-data]
```

**参数**:
- `产品名称`: 必须，要卸载的产品名称
- `master1|master2`: 可选，指定要卸载的节点，不指定则卸载所有节点
- `--remove-data`: 可选，卸载时删除所有数据目录
- `--keep-data`: 可选，卸载时保留数据目录（默认）

**选项**:
- `-h, --help`: 显示帮助信息

**示例**:
```bash
./uninstall.sh cloudkey            # 卸载cloudkey产品的所有MySQL节点，保留数据
./uninstall.sh cloudkey master1    # 只卸载cloudkey产品的master1节点，保留数据
./uninstall.sh cloudkey --remove-data # 卸载cloudkey产品的所有MySQL节点并删除数据
```

**注意事项**:
- 使用`--remove-data`选项会永久删除所有数据，请谨慎使用
- 建议卸载前先执行备份操作

#### 4. monitor.sh - 监控脚本

**用途**: 监控MySQL服务的状态、性能和资源使用情况。

**用法**:
```bash
./monitor.sh [产品名] [选项]
```

**功能**:
- 生成MySQL健康状况报告
- 检查连接情况和慢查询
- 监控资源使用（CPU、内存、磁盘）
- 分析性能指标

#### 5. backup.sh - 备份脚本

**用途**: 执行数据库备份操作，支持全量备份和增量备份。

**用法**:
```bash
./backup.sh [产品名] [备份类型]
```

**备份类型**:
- `full`: 全量备份（默认）
- `incremental`: 增量备份

#### 6. restore.sh - 恢复脚本

**用途**: 从现有备份中恢复数据库。

**用法**:
```bash
./restore.sh [产品名] [备份文件路径]
```

#### 7. setup-master-master.sh - 主主复制配置脚本

**用途**: 在两个MySQL节点之间配置主主复制关系。

**用法**:
```bash
./setup-master-master.sh <产品名>
```

功能:
- 配置两个MySQL节点间的主主复制关系
- 创建复制用户
- 设置复制参数
- 初始化复制过程
- 验证复制状态

#### 8. common.sh - 通用函数库

**用途**: 提供所有脚本共享的函数和变量定义。

**主要内容**:
- 颜色定义（用于终端输出）
- 目录路径定义
- 日志函数
- 通用工具函数
- 错误处理函数

**注意**: 此脚本不直接执行，而是被其他脚本通过source命令引入。

### 日常运维最佳实践

1. **首次部署**:
   ```bash
   ./deploy.sh <产品名> <节点角色>
   ```

2. **日常维护操作**:
   ```bash
   ./maintenance.sh <产品名>
   ```
   - 使用交互式菜单选择所需的维护操作
   - 避免直接使用Docker命令操作容器

3. **定期备份**:
   - 使用维护脚本中的备份选项，或直接执行备份脚本
   - 验证备份文件的完整性
   - 定期将备份文件转移到离线存储

4. **监控健康状况**:
   ```bash
   ./monitor.sh <产品名>
   ```
   - 定期检查并分析监控报告
   - 对潜在问题及早干预

5. **配置更新**:
   - 使用维护脚本中的"更新配置"选项
   - 确保配置更改后测试服务状态

6. **完全卸载**:
   ```bash
   ./uninstall.sh <产品名> [--remove-data]
   ```
   - 仅在确认不再需要服务时使用
   - 执行卸载前先进行备份

通过合理使用这些脚本，可以实现MySQL服务的高效部署和维护，保障系统的稳定性和可靠性。 

### 新部署模式 - 独立配置文件架构

#### 部署模式更新说明

本次更新将MySQL部署模式从单一Compose文件改为独立的配置文件架构，适应分布式环境下的部署需求：

1. **配置文件拆分**：
   - 原先：所有节点配置在单一的`<产品名>-mysql.yml`文件中
   - 现在：每个节点使用独立的配置文件
      - `<产品名>-mysql-master1.yml` - 专用于主节点1
      - `<产品名>-mysql-master2.yml` - 专用于主节点2
      - `setup-replication.yml` - 用于配置主主复制

2. **部署脚本更新**：
   - `deploy.sh`脚本更新为动态生成节点配置文件
   - 根据节点角色（master1/master2/both）选择部署对应的配置文件
   - 支持跨服务器的分布式部署模式

3. **维护脚本更新**：
   - `maintenance.sh`脚本支持独立操作各个节点
   - 启动、停止和重启操作可以选择特定节点或所有节点
   - 各节点状态展示更加清晰

#### 使用示例

**1. 单机测试环境部署（两个节点在一台机器上）**：
```bash
cd infrastructure/database/mysql/scripts
./deploy.sh cloudkey both
```

**2. 分布式生产环境部署（两个节点在不同机器上）**：
```bash
# 在第一台服务器上:
cd infrastructure/database/mysql/scripts
./deploy.sh cloudkey master1

# 在第二台服务器上:
cd infrastructure/database/mysql/scripts
./deploy.sh cloudkey master2

# 配置主主复制（在其中一台服务器上执行）:
MASTER1_HOST=************* MASTER2_HOST=************* docker-compose -f ../compose/setup-replication.yml up --abort-on-container-exit
```

**3. 维护操作**：
```bash
cd infrastructure/database/mysql/scripts
./maintenance.sh cloudkey
# 根据菜单选择需要的操作和目标节点
```

这种部署架构更加符合实际生产环境中的分布式部署需求，使MySQL主主复制的管理更加清晰和高效。 