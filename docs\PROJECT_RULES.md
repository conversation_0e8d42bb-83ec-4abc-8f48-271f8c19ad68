# 项目规则

## 版本控制规则

### Git 提交规范
1. 每次完整的功能变动都必须进行一次 git commit
2. commit message 必须清晰描述变更内容
3. commit message 格式：
   ```
   <type>(<scope>): <subject>

   <body>
   ```
   - type: feat|fix|docs|style|refactor|test|chore
   - scope: 变更范围
   - subject: 变更描述
   - body: 详细说明（可选）

### 目录结构管理
1. 任何涉及目录结构的变更，必须同步更新 `dir-structure` 文件
2. 新增目录时必须添加 README.md 说明文件
3. 目录命名规则：
   - 全小写
   - 多词使用连字符(-)分隔
   - 保持简洁明确

### 配置文件管理
1. 所有配置文件必须放在对应服务的 config 目录下
2. 配置文件必须包含必要的注释说明
3. 敏感配置信息必须使用环境变量或配置中心管理

### 日志管理
1. 所有日志必须按服务分类存储
2. 日志文件必须包含日期信息
3. 必须配置日志轮转策略

### 镜像管理
1. 镜像必须按架构分类存储（x86_64/aarch64）
2. 镜像文件必须包含版本信息
3. 必须维护镜像的 SHA256 校验值 