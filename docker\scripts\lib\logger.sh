#!/bin/bash
# ===============================================================
# 日志处理库
# 提供统一的日志记录功能
# ===============================================================

# 确保只加载一次
[[ -n "${_LOGGER_SH_LOADED:-}" ]] && return 0
readonly _LOGGER_SH_LOADED=1

# 日志级别定义
declare -r LOG_LEVEL_DEBUG=0
declare -r LOG_LEVEL_INFO=1
declare -r LOG_LEVEL_WARN=2
declare -r LOG_LEVEL_ERROR=3

# 默认日志级别
CURRENT_LOG_LEVEL=${LOG_LEVEL_INFO}

# 日志颜色定义
declare -r COLOR_DEBUG='\033[0;36m'    # Cyan
declare -r COLOR_INFO='\033[0;32m'     # Green
declare -r COLOR_WARN='\033[0;33m'     # Yellow
declare -r COLOR_ERROR='\033[0;31m'    # Red
declare -r COLOR_RESET='\033[0m'       # Reset

# 初始化日志系统
init_logger() {
    local log_level="${1:-INFO}"
    local log_file="${2:-}"
    
    # 设置日志级别
    case "${log_level^^}" in
        "DEBUG") CURRENT_LOG_LEVEL=$LOG_LEVEL_DEBUG ;;
        "INFO")  CURRENT_LOG_LEVEL=$LOG_LEVEL_INFO ;;
        "WARN")  CURRENT_LOG_LEVEL=$LOG_LEVEL_WARN ;;
        "ERROR") CURRENT_LOG_LEVEL=$LOG_LEVEL_ERROR ;;
        *) echo "无效的日志级别: $log_level，使用默认级别 INFO" >&2
           CURRENT_LOG_LEVEL=$LOG_LEVEL_INFO ;;
    esac
    
    # 如果指定了日志文件，确保目录存在
    if [[ -n "${log_file}" ]]; then
        local log_dir
        log_dir=$(dirname "${log_file}")
        [[ -d "${log_dir}" ]] || mkdir -p "${log_dir}"
    fi
    
    export LOG_FILE="${log_file}"
}

# 内部日志记录函数
_log() {
    local level=$1
    local color=$2
    local msg=$3
    local log_level_name
    
    case $level in
        $LOG_LEVEL_DEBUG) log_level_name="DEBUG" ;;
        $LOG_LEVEL_INFO)  log_level_name="INFO" ;;
        $LOG_LEVEL_WARN)  log_level_name="WARN" ;;
        $LOG_LEVEL_ERROR) log_level_name="ERROR" ;;
    esac
    
    # 检查是否应该记录此级别的日志
    [[ $level -ge $CURRENT_LOG_LEVEL ]] || return 0
    
    # 格式化日志消息
    local timestamp
    timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local formatted_msg="${timestamp} [${log_level_name}] ${msg}"
    
    # 输出到控制台
    echo -e "${color}${formatted_msg}${COLOR_RESET}"
    
    # 如果配置了日志文件，同时写入文件
    if [[ -n "${LOG_FILE}" ]]; then
        echo "${formatted_msg}" >> "${LOG_FILE}"
    fi
}

# 公共日志接口
log_debug() { _log $LOG_LEVEL_DEBUG "${COLOR_DEBUG}" "$*"; }
log_info()  { _log $LOG_LEVEL_INFO  "${COLOR_INFO}"  "$*"; }
log_warn()  { _log $LOG_LEVEL_WARN  "${COLOR_WARN}"  "$*"; }
log_error() { _log $LOG_LEVEL_ERROR "${COLOR_ERROR}" "$*"; }

# 错误处理函数
log_error_and_exit() {
    log_error "$*"
    exit 1
}

# 导出公共函数
export -f init_logger
export -f log_debug
export -f log_info
export -f log_warn
export -f log_error
export -f log_error_and_exit 