#!/bin/bash
# OpenResty 服务控制脚本
# 作用：提供 OpenResty 服务的启动、停止、重启、状态查询、配置重载和日志查看功能
# 使用方法：./control.sh [命令]
#   服务级命令: start|stop|restart|reload|test|showconf
#   容器级命令: container_start|container_stop|container_restart|status|logs
#   端口命令: check_ports|open_ports

# 引入公共库函数
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${SCRIPT_DIR}/common_lib.sh"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # 无颜色

# OpenResty 服务目录
OPENRESTY_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
COMPOSE_FILE="${OPENRESTY_DIR}/docker-compose.yml"

# 要检查的端口列表
REQUIRED_PORTS=(6443 8080)

# Docker Compose 命令变量
DOCKER_COMPOSE_CMD=""

# 初始化Docker Compose命令
if ! DOCKER_COMPOSE_CMD=$(get_docker_compose_cmd); then
    echo -e "${RED}错误: 无法获取Docker Compose命令${NC}"
    exit 1
fi

# 显示使用方法
show_usage() {
    echo -e "${YELLOW}OpenResty 服务控制脚本${NC}"
    echo "使用方法: $0 [命令]"
    echo ""
    echo "服务级命令 (更快，推荐日常使用):"
    echo "  start    - 启动 OpenResty 服务进程"
    echo "  stop     - 停止 OpenResty 服务进程"
    echo "  restart  - 重启 OpenResty 服务进程"
    echo "  reload   - 重新加载 OpenResty 配置"
    echo "  test     - 测试配置文件语法"
    echo "  showconf - 显示完整配置"
    echo ""
    echo "容器级命令 (更全面，适用于初始部署或环境重建):"
    echo "  container_start   - 启动 OpenResty 容器"
    echo "  container_stop    - 停止 OpenResty 容器"
    echo "  container_restart - 重启 OpenResty 容器"
    echo "  status            - 查看容器状态"
    echo "  logs              - 查看容器日志"
    echo ""
    echo "端口命令:"
    echo "  check_ports       - 检查端口状态(6443和8080)"
    echo "  open_ports        - 在防火墙中开放所需端口"
    echo ""
}

# 检查容器是否运行
check_container_running() {
    local container_id=$(cd "$OPENRESTY_DIR" && $DOCKER_COMPOSE_CMD ps -q openresty)
    if [ -z "$container_id" ]; then
        return 1
    fi
    
    local container_status=$(docker inspect -f '{{.State.Running}}' "$container_id" 2>/dev/null)
    if [ "$container_status" == "true" ]; then
        return 0
    else
        return 1
    fi
}

# 在防火墙中开放端口
open_ports_in_firewall() {
    echo -e "${YELLOW}正在检查防火墙并开放所需端口...${NC}"
    
    # 使用common_lib中的detect_os_type函数检测系统类型
    detect_os_type
    
    local all_ports_opened=true
    
    for port in "${REQUIRED_PORTS[@]}"; do
        echo -e "${YELLOW}尝试在防火墙中开放端口 $port...${NC}"
        # 使用common_lib中的open_firewall_port函数
        if ! open_firewall_port "$port"; then
            all_ports_opened=false
        fi
    done
    
    if $all_ports_opened; then
        echo -e "${GREEN}✓ 所有必需端口已在防火墙中开放${NC}"
    else
        echo -e "${RED}✗ 部分端口开放失败，请手动检查防火墙配置${NC}"
        echo -e "${YELLOW}提示: 您可能需要以root权限运行此脚本以配置防火墙${NC}"
    fi
}

# 检查端口状态
check_ports_status() {
    echo -e "${YELLOW}检查 OpenResty 所需端口状态...${NC}"
    
    local all_ports_ok=true
    
    for port in "${REQUIRED_PORTS[@]}"; do
        echo -e "${YELLOW}检查端口 $port...${NC}"
        
        # 使用ss命令检查端口监听状态
        if command -v ss >/dev/null 2>&1; then
            if ss -tlnp | grep -q ":$port "; then
                echo -e "${GREEN}✓ 端口 $port 正在监听${NC}"
                # 显示监听该端口的进程
                echo -e "  监听进程: $(ss -tlnp | grep ":$port " | grep -o 'pid=[0-9]*' | cut -d= -f2)"
            else
                echo -e "${RED}✗ 端口 $port 未监听${NC}"
                all_ports_ok=false
            fi
        # 备用方案使用netstat
        elif command -v netstat >/dev/null 2>&1; then
            if netstat -tlnp | grep -q ":$port "; then
                echo -e "${GREEN}✓ 端口 $port 正在监听${NC}"
                # 显示监听该端口的进程
                echo -e "  监听进程: $(netstat -tlnp | grep ":$port " | awk '{print $7}')"
            else
                echo -e "${RED}✗ 端口 $port 未监听${NC}"
                all_ports_ok=false
            fi
        else
            echo -e "${RED}✗ 无法检查端口 $port 状态: ss和netstat命令都不可用${NC}"
            all_ports_ok=false
        fi
    done
    
    if $all_ports_ok; then
        echo -e "${GREEN}所有必需端口都已正常监听!${NC}"
        return 0
    else
        echo -e "${RED}部分必需端口未正常监听，服务可能不完全可用!${NC}"
        echo -e "${YELLOW}提示: 如果刚刚启动服务，请稍等片刻再检查${NC}"
        echo -e "${YELLOW}如果问题持续存在，请检查是否有其他服务占用了这些端口${NC}"
        return 1
    fi
}

# 检查端口占用情况
check_port_availability() {
    local port=$1
    
    # 使用ss命令检查端口占用
    if command -v ss >/dev/null 2>&1; then
        if ss -tlnp | grep -q ":$port "; then
            return 1
        fi
    # 备用方案使用netstat
    elif command -v netstat >/dev/null 2>&1; then
        if netstat -tlnp | grep -q ":$port "; then
            return 1
        fi
    fi
    
    return 0
}

# 终止所有OpenResty进程
kill_openresty_processes() {
    echo -e "${YELLOW}正在强制终止所有OpenResty进程...${NC}"
    
    if check_container_running; then
        # 使用docker exec执行pkill命令
        docker exec $(cd "$OPENRESTY_DIR" && $DOCKER_COMPOSE_CMD ps -q openresty) sh -c "pkill -9 nginx || true"
        sleep 2
    fi
}

# 强制释放端口
force_release_port() {
    local port=$1
    echo -e "${YELLOW}尝试强制释放端口 $port...${NC}"
    
    if check_container_running; then
        # 在容器内执行命令释放端口
        docker exec $(cd "$OPENRESTY_DIR" && $DOCKER_COMPOSE_CMD ps -q openresty) sh -c "
            # 尝试关闭TIME_WAIT状态的连接
            if command -v ss >/dev/null 2>&1; then
                ss -tuna | grep $port | grep TIME-WAIT | awk '{print \$4}' | while read conn; do
                    echo \"尝试关闭连接 \$conn\"
                done
            fi
            
            # 设置更快的TIME_WAIT回收
            if [ -f /proc/sys/net/ipv4/tcp_tw_recycle ]; then
                echo 1 > /proc/sys/net/ipv4/tcp_tw_recycle
            fi
            
            # 启用TIME_WAIT套接字重用
            if [ -f /proc/sys/net/ipv4/tcp_tw_reuse ]; then
                echo 1 > /proc/sys/net/ipv4/tcp_tw_reuse
            fi
            
            # 使用重新启动容器内网络的方式来释放所有套接字
            if command -v ip >/dev/null 2>&1; then
                ip addr flush dev eth0 scope global 2>/dev/null || true
            fi
        " || true
        
        sleep 3
    fi
}

# 检查所需端口是否可用
check_ports_availability() {
    echo -e "${YELLOW}检查所需端口是否可用...${NC}"
    
    local all_ports_available=true
    
    for port in "${REQUIRED_PORTS[@]}"; do
        if ! check_port_availability $port; then
            echo -e "${RED}端口 $port 已被占用!${NC}"
            all_ports_available=false
            
            # 显示占用端口的进程信息
            if command -v ss >/dev/null 2>&1; then
                echo -e "${YELLOW}查看占用该端口的进程:${NC}"
                ss -tlnp | grep ":$port "
            elif command -v netstat >/dev/null 2>&1; then
                echo -e "${YELLOW}查看占用该端口的进程:${NC}"
                netstat -tlnp | grep ":$port "
            fi
        else
            echo -e "${GREEN}端口 $port 可用${NC}"
        fi
    done
    
    if ! $all_ports_available; then
        echo -e "${RED}部分必需端口被占用，可能影响服务正常启动!${NC}"
        echo -e "${YELLOW}是否尝试强制释放这些端口? [y/N]${NC}"
        read -r response
        if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
            for port in "${REQUIRED_PORTS[@]}"; do
                if ! check_port_availability $port; then
                    force_release_port $port
                fi
            done
        else
            echo -e "${YELLOW}未释放被占用的端口，服务可能无法正常启动${NC}"
        fi
    fi
}

# 启动容器
container_start_service() {
    echo -e "${YELLOW}正在启动 OpenResty 容器...${NC}"
    
    # 检查Docker环境
    check_docker_environment || exit 1
    
    # 在防火墙中开放所需端口
    open_ports_in_firewall
    
    # 检查端口是否可用
    check_ports_availability
    
    if [ -f "$COMPOSE_FILE" ]; then
        cd "$OPENRESTY_DIR" && $DOCKER_COMPOSE_CMD up -d
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}OpenResty 容器已成功启动!${NC}"
            # 等待服务完全启动
            echo -e "${YELLOW}等待5秒让服务完全启动...${NC}"
            sleep 5
            # 检查端口状态
            check_ports_status
        else
            echo -e "${RED}启动 OpenResty 容器失败!${NC}"
            exit 1
        fi
    else
        echo -e "${RED}错误: docker-compose.yml 文件不存在于 $OPENRESTY_DIR${NC}"
        exit 1
    fi
}

# 停止容器
container_stop_service() {
    echo -e "${YELLOW}正在停止 OpenResty 容器...${NC}"
    if [ -f "$COMPOSE_FILE" ]; then
        cd "$OPENRESTY_DIR" && $DOCKER_COMPOSE_CMD down
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}OpenResty 容器已成功停止!${NC}"
        else
            echo -e "${RED}停止 OpenResty 容器失败!${NC}"
            exit 1
        fi
    else
        echo -e "${RED}错误: docker-compose.yml 文件不存在于 $OPENRESTY_DIR${NC}"
        exit 1
    fi
}

# 重启容器
container_restart_service() {
    echo -e "${YELLOW}正在重启 OpenResty 容器...${NC}"
    container_stop_service
    sleep 5
    container_start_service
}

# 启动服务
start_service() {
    echo -e "${YELLOW}正在启动 OpenResty 服务...${NC}"
    
    # 检查容器是否运行
    if ! check_container_running; then
        echo -e "${YELLOW}OpenResty 容器未运行，先启动容器...${NC}"
        container_start_service
        return
    fi
    
    # 使用OpenResty原生命令启动服务
    cd "$OPENRESTY_DIR" && $DOCKER_COMPOSE_CMD exec openresty /usr/local/openresty/bin/openresty
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}OpenResty 服务已成功启动!${NC}"
        # 等待服务完全启动
        echo -e "${YELLOW}等待3秒让服务完全启动...${NC}"
        sleep 3
        # 检查端口状态
        check_ports_status
    else
        echo -e "${RED}启动 OpenResty 服务失败!${NC}"
        exit 1
    fi
}

# 停止服务
stop_service() {
    echo -e "${YELLOW}正在停止 OpenResty 服务...${NC}"
    
    # 检查容器是否运行
    if ! check_container_running; then
        echo -e "${YELLOW}OpenResty 容器未运行，无需停止服务。${NC}"
        return 0
    fi
    
    # 使用OpenResty原生命令停止服务
    cd "$OPENRESTY_DIR" && $DOCKER_COMPOSE_CMD exec openresty /usr/local/openresty/bin/openresty -s quit
    local stop_status=$?
    
    if [ $stop_status -eq 0 ]; then
        echo -e "${GREEN}OpenResty 服务已成功停止!${NC}"
        # 等待服务完全停止
        sleep 3
    else
        echo -e "${RED}停止 OpenResty 服务失败!${NC}"
        exit 1
    fi
}

# 重启服务
restart_service() {
    echo -e "${YELLOW}正在重启 OpenResty 服务...${NC}"
    
    # 检查容器是否运行
    if ! check_container_running; then
        echo -e "${YELLOW}OpenResty 容器未运行，先启动容器...${NC}"
        container_start_service
        return
    fi
    
    # 使用OpenResty原生命令重启服务
    stop_service
    sleep 2
    start_service
}

# 检查服务状态
check_status() {
    echo -e "${YELLOW}OpenResty 容器状态:${NC}"
    if [ -f "$COMPOSE_FILE" ]; then
        cd "$OPENRESTY_DIR" && $DOCKER_COMPOSE_CMD ps
        
        # 如果容器在运行，检查端口状态
        if check_container_running; then
            echo ""
            check_ports_status
        fi
    else
        echo -e "${RED}错误: docker-compose.yml 文件不存在于 $OPENRESTY_DIR${NC}"
        exit 1
    fi
}

# 重载配置
reload_config() {
    echo -e "${YELLOW}正在重载 OpenResty 配置...${NC}"
    
    # 检查容器是否运行
    if ! check_container_running; then
        echo -e "${RED}错误: OpenResty 容器未运行，无法重载配置。${NC}"
        exit 1
    fi
    
    # 先测试配置
    test_config
    
    # 使用OpenResty原生命令重载配置
    cd "$OPENRESTY_DIR" && $DOCKER_COMPOSE_CMD exec openresty /usr/local/openresty/bin/openresty -s reload
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}OpenResty 配置已成功重载!${NC}"
        # 等待重载完成
        sleep 2
        # 检查端口状态
        check_ports_status
    else
        echo -e "${RED}重载 OpenResty 配置失败!${NC}"
        exit 1
    fi
}

# 测试配置
test_config() {
    echo -e "${YELLOW}正在测试 OpenResty 配置文件语法...${NC}"
    
    # 检查容器是否运行
    if ! check_container_running; then
        echo -e "${RED}错误: OpenResty 容器未运行，无法测试配置。${NC}"
        exit 1
    fi
    
    # 使用OpenResty原生命令测试配置
    cd "$OPENRESTY_DIR" && $DOCKER_COMPOSE_CMD exec openresty /usr/local/openresty/bin/openresty -t
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}OpenResty 配置语法正确!${NC}"
    else
        echo -e "${RED}OpenResty 配置语法错误!${NC}"
        exit 1
    fi
}

# 显示完整配置
show_config() {
    echo -e "${YELLOW}显示 OpenResty 完整配置:${NC}"
    
    # 检查容器是否运行
    if ! check_container_running; then
        echo -e "${RED}错误: OpenResty 容器未运行，无法显示配置。${NC}"
        exit 1
    fi
    
    # 使用OpenResty原生命令显示配置
    cd "$OPENRESTY_DIR" && $DOCKER_COMPOSE_CMD exec openresty /usr/local/openresty/bin/openresty -T
}

# 查看日志
view_logs() {
    echo -e "${YELLOW}OpenResty 容器日志:${NC}"
    if [ -f "$COMPOSE_FILE" ]; then
        cd "$OPENRESTY_DIR" && $DOCKER_COMPOSE_CMD logs --tail=100 openresty
    else
        echo -e "${RED}错误: docker-compose.yml 文件不存在于 $OPENRESTY_DIR${NC}"
        exit 1
    fi
}

# 主函数
main() {
    case "$1" in
        start)
            start_service
            ;;
        stop)
            stop_service
            ;;
        restart)
            restart_service
            ;;
        container_start)
            container_start_service
            ;;
        container_stop)
            container_stop_service
            ;;
        container_restart)
            container_restart_service
            ;;
        status)
            check_status
            ;;
        reload)
            reload_config
            ;;
        test)
            test_config
            ;;
        showconf)
            show_config
            ;;
        logs)
            view_logs
            ;;
        check_ports)
            check_ports_status
            ;;
        open_ports)
            open_ports_in_firewall
            ;;
        *)
            show_usage
            exit 1
            ;;
    esac
}

# 执行主函数
if [ $# -eq 0 ]; then
    show_usage
    exit 1
else
    main "$1"
fi 