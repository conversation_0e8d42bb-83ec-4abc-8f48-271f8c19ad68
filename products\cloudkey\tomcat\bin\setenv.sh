#!/bin/bash
# CloudKey应用的Tomcat环境变量配置

# =====================================================
# JVM 内存设置
# =====================================================
JAVA_OPTS="-server -Xms3G -Xmx3G"
JAVA_OPTS="$JAVA_OPTS -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=256m"
JAVA_OPTS="$JAVA_OPTS -XX:MaxDirectMemorySize=256m"

# =====================================================
# 字符编码和时区设置
# =====================================================
JAVA_OPTS="$JAVA_OPTS -Dfile.encoding=UTF-8 -Duser.timezone=GMT+8"

# =====================================================
# GC 配置
# =====================================================
# GC 算法选择
JAVA_OPTS="$JAVA_OPTS -XX:+UseConcMarkSweepGC -XX:+CMSParallelRemarkEnabled"
JAVA_OPTS="$JAVA_OPTS -XX:+UseCMSInitiatingOccupancyOnly -XX:CMSInitiatingOccupancyFraction=70"
JAVA_OPTS="$JAVA_OPTS -XX:+ScavengeBeforeFullGC -XX:+CMSScavengeBeforeRemark"
JAVA_OPTS="$JAVA_OPTS -XX:+CMSClassUnloadingEnabled"

# GC 日志配置
JAVA_OPTS="$JAVA_OPTS -XX:+PrintGCDetails -XX:+PrintGCDateStamps"
JAVA_OPTS="$JAVA_OPTS -XX:+PrintGCTimeStamps -XX:+PrintHeapAtGC"
JAVA_OPTS="$JAVA_OPTS -Xloggc:$CATALINA_HOME/logs/gc.log"
JAVA_OPTS="$JAVA_OPTS -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=5 -XX:GCLogFileSize=10M"

# =====================================================
# 性能优化参数
# =====================================================
JAVA_OPTS="$JAVA_OPTS -XX:+DisableExplicitGC"
JAVA_OPTS="$JAVA_OPTS -XX:+ScavengeBeforeFullGC"
JAVA_OPTS="$JAVA_OPTS -XX:+AlwaysPreTouch"
JAVA_OPTS="$JAVA_OPTS -XX:+ExplicitGCInvokesConcurrent"

# =====================================================
# OOM处理
# =====================================================
JAVA_OPTS="$JAVA_OPTS -XX:+HeapDumpOnOutOfMemoryError"
JAVA_OPTS="$JAVA_OPTS -XX:HeapDumpPath=$CATALINA_HOME/logs/heapdump.hprof"
JAVA_OPTS="$JAVA_OPTS -XX:OnOutOfMemoryError=\"kill -9 %p\""

# =====================================================
# Catalina特定参数
# =====================================================
CATALINA_OPTS="-Djava.security.egd=file:/dev/./urandom"
CATALINA_OPTS="$CATALINA_OPTS -Djava.awt.headless=true"


# 导出环境变量
export JAVA_OPTS CATALINA_OPTS 
export CATALINA_PID="$CATALINA_HOME/tomcat.pid"
export LD_LIBRARY_PATH=/usr/lib64:/usr/lib/x86_64-linux-gnu:/usr/local/lib64
JAVA_OPTS="$JAVA_OPTS -Dlog4j2.formatMsgNoLookups=true -Djava.security.egd=file:/dev/./urandom"
export JAVA_OPTS

# 开启加密卡日志
#export NETCA_CARD_MNGR_TRACE=/var/log/NetcaMngr.log
#export NETCA_SWSDS_CARD_TRACE=/var/log/NetcaSwsds.log
