#!/bin/bash
# Keepalived配置生成脚本
# 根据模板和环境变量配置生成产品特定的Keepalived配置

set -e

# 脚本所在目录
SCRIPT_DIR=$(dirname "$(readlink -f "$0")")
# 项目根目录
BASE_DIR=$(dirname "${SCRIPT_DIR}")
# 配置目录 - 修正路径，移除多余的gateway前缀
CONFIG_DIR="${BASE_DIR}/config"
# 公共配置目录
COMMON_DIR="${CONFIG_DIR}/common"
# 脚本模板目录
TEMPLATE_DIR="${COMMON_DIR}/scripts"
# 环境变量目录
ENV_DIR="${COMMON_DIR}/env"

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo "选项:"
    echo "  -p, --product PRODUCT   指定产品 (cloudkey, eseal, quicksign)"
    echo "  -a, --all               处理所有产品"
    echo "  -h, --help              显示帮助信息"
    echo
    echo "示例:"
    echo "  $0 -p cloudkey          为云密钥系统生成配置"
    echo "  $0 -a                   为所有产品生成配置"
}

# 替换配置文件中的变量
replace_variables() {
    local template_file="$1"
    local output_file="$2"
    local env_file="$3"
    
    # 确保输出目录存在
    mkdir -p "$(dirname "${output_file}")"
    
    # 复制模板文件
    cp "${template_file}" "${output_file}"
    
    # 读取环境变量文件并应用替换
    while IFS='=' read -r key value; do
        # 忽略注释行和空行
        if [[ ${key:0:1} != '#' && -n "$key" && -n "$value" ]]; then
            # 去除引号
            value="${value%\"}"
            value="${value#\"}"
            value="${value%\'}"
            value="${value#\'}"
            
            # 替换模板中的变量
            sed -i "s|{{$key}}|$value|g" "${output_file}"
        fi
    done < "${env_file}"
    
    # 设置执行权限
    chmod +x "${output_file}"
    
    echo "已生成配置文件: ${output_file}"
}

# 处理单个产品配置
process_product() {
    local product="$1"
    local env_file="${ENV_DIR}/${product}.env"
    local product_dir="${CONFIG_DIR}/${product}"
    local scripts_dir="${product_dir}/scripts"
    
    echo "正在处理 ${product} 产品配置..."
    
    # 检查环境变量文件是否存在
    if [ ! -f "${env_file}" ]; then
        echo "错误: 找不到 ${product} 的环境变量配置文件: ${env_file}"
        return 1
    fi
    
    # 创建产品配置目录
    mkdir -p "${product_dir}"
    mkdir -p "${scripts_dir}"
    
    # 生成脚本文件
    replace_variables "${TEMPLATE_DIR}/check_redis.sh.tpl" "${scripts_dir}/check_redis.sh" "${env_file}"
    replace_variables "${TEMPLATE_DIR}/check_mysql.sh.tpl" "${scripts_dir}/check_mysql.sh" "${env_file}"
    replace_variables "${TEMPLATE_DIR}/check_nginx.sh.tpl" "${scripts_dir}/check_nginx.sh" "${env_file}"
    # 将notify.sh也放入scripts目录下，便于统一管理
    replace_variables "${TEMPLATE_DIR}/notify.sh.tpl" "${scripts_dir}/notify.sh" "${env_file}"
    
    # 更新配置文件中的notify脚本路径
    for config_type in "master" "backup"; do
        local config_file="${product_dir}/keepalived_${config_type}.conf"
        if [ -f "${config_file}" ]; then
            echo "更新 ${config_file} 中的通知脚本路径..."
            # 替换notify脚本路径
            sed -i "s|/etc/keepalived/${product}/notify.sh|/etc/keepalived/scripts/${product}/notify.sh|g" "${config_file}"
        fi
    done
    
    echo "${product} 产品配置文件生成完成!"
    echo
}

# 主处理逻辑
main() {
    local process_all=false
    local product=""
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case "$1" in
            -p|--product)
                product="$2"
                shift 2
                ;;
            -a|--all)
                process_all=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                echo "错误: 未知选项 $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查参数
    if [ "$process_all" = false ] && [ -z "$product" ]; then
        echo "错误: 请指定要处理的产品或使用 -a 选项处理所有产品"
        show_help
        exit 1
    fi
    
    # 处理指定产品或所有产品
    if [ "$process_all" = true ]; then
        # 获取所有环境变量文件对应的产品
        for env_file in "${ENV_DIR}"/*.env; do
            if [ -f "${env_file}" ]; then
                product_name=$(basename "${env_file}" .env)
                process_product "${product_name}"
            fi
        done
    else
        process_product "${product}"
    fi
    
    echo "配置文件生成完成!"
}

# 执行主函数
main "$@" 