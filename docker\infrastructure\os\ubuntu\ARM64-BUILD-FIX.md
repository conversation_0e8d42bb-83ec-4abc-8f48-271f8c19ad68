# Ubuntu ARM64 构建问题解决方案

## 问题描述

在ARM64架构上构建Ubuntu Docker镜像时遇到以下问题：

1. **404错误**: 阿里云镜像源不完全支持ARM64架构的包
2. **GPG密钥问题**: 缺少公钥 `NO_PUBKEY 871920D1991BC93C`
3. **权限问题**: 密钥文件不可读
4. **apt-key弃用**: 使用了过时的GPG密钥管理方式

## 解决方案

### 1. 立即修复方案

已修复的 `Dockerfile.template` 包含以下改进：

- **智能架构检测**: 自动检测架构并使用相应的镜像源
- **ARM64优化源**: 使用 `ports.ubuntu.com` 替代阿里云镜像源
- **现代GPG管理**: 使用新的GPG密钥管理方式，避免 `apt-key` 弃用警告
- **备用镜像源**: 添加清华大学镜像源作为备用

### 2. 统一多架构支持

优化了 `Dockerfile.template` 以智能支持多架构：

- 自动检测架构并配置相应的镜像源（ARM64使用Ubuntu Ports，AMD64使用阿里云镜像）
- 简化的库文件管理，让系统自动处理架构特定路径
- 使用通配符和自动查找来创建必要的库文件链接

### 3. 自动化修复工具

#### 修复脚本: `docker/scripts/fix-arm64-build.sh`

```bash
# 运行修复脚本
./docker/scripts/fix-arm64-build.sh

# 修复特定Dockerfile
./docker/scripts/fix-arm64-build.sh docker/infrastructure/os/ubuntu/Dockerfile.template
```

功能：
- 检查Docker Buildx支持
- 设置多架构构建器
- 清理Docker缓存
- 测试ARM64构建
- 提供构建建议

#### ARM64专用构建脚本: `docker/scripts/build/build-os-arm64.sh`

```bash
# 基本构建
./docker/scripts/build/build-os-arm64.sh ubuntu 22.04

# 包含密码卡支持
./docker/scripts/build/build-os-arm64.sh ubuntu 22.04 --install-sc34 --push

# 干运行（仅显示命令）
./docker/scripts/build/build-os-arm64.sh ubuntu 22.04 --dry-run
```

## 使用方法

### 方法1: 使用优化后的统一模板

```bash
# 使用Docker Buildx构建ARM64镜像
docker buildx build --platform linux/arm64 \
  --build-arg CRYPTO_VERSION=1.0.0 \
  --build-arg ARCH=arm64 \
  -t your-registry/ubuntu:22.04-arm64 \
  -f docker/infrastructure/os/ubuntu/Dockerfile.template \
  .
```

### 方法2: 使用专用构建脚本

```bash
# 运行ARM64构建脚本
./docker/scripts/build/build-os-arm64.sh ubuntu 22.04 --arch arm64
```

## 关键修复点

### 1. 镜像源配置

**修复前**:
```dockerfile
# 阿里云镜像源（ARM64支持不完整）
echo "deb http://mirrors.aliyun.com/ubuntu/ jammy main restricted universe multiverse" > /etc/apt/sources.list
```

**修复后**:
```dockerfile
# 智能架构检测
ARCH=$(dpkg --print-architecture)
if [ "$ARCH" = "arm64" ] || [ "$ARCH" = "aarch64" ]; then
  # ARM64使用Ubuntu Ports源
  echo "deb http://ports.ubuntu.com/ubuntu-ports jammy main restricted universe multiverse" > /etc/apt/sources.list
else
  # AMD64使用阿里云镜像源
  echo "deb http://mirrors.aliyun.com/ubuntu/ jammy main restricted universe multiverse" > /etc/apt/sources.list
fi
```

### 2. GPG密钥管理

**修复前**:
```dockerfile
# 过时的apt-key方式
apt-key adv --keyserver keyserver.ubuntu.com --recv-keys 871920D1991BC93C
```

**修复后**:
```dockerfile
# 现代GPG密钥管理
mkdir -p /etc/apt/keyrings
gpg --no-default-keyring --keyring /etc/apt/keyrings/ubuntu-archive-keyring.gpg \
    --keyserver keyserver.ubuntu.com --recv-keys 871920D1991BC93C
chmod 644 /etc/apt/keyrings/ubuntu-archive-keyring.gpg
```

### 3. 简化的库文件管理

**自动库文件查找**:
```dockerfile
# 简化的LD_LIBRARY_PATH配置（让系统处理架构特定路径）
echo "export LD_LIBRARY_PATH=/usr/lib64:/usr/local/lib" >> /etc/profile

# 自动查找和链接系统库
for lib in libsqlite3.so.0 libssl.so.3 libcrypto.so.3; do
  if [ ! -e "/usr/lib64/$lib" ]; then
    lib_path=$(find /usr/lib -name "$lib" 2>/dev/null | head -1)
    [ -n "$lib_path" ] && ln -sf "$lib_path" "/usr/lib64/$lib"
  fi
done
```

## 故障排除

### 1. 如果仍然遇到404错误

```bash
# 尝试使用不同的镜像源
docker buildx build --platform linux/arm64 \
  --build-arg UBUNTU_MIRROR=http://archive.ubuntu.com/ubuntu/ \
  -t your-image .
```

### 2. 如果GPG密钥问题持续

```bash
# 手动添加密钥
docker run --rm -it ubuntu:22.04 bash -c "
  apt-get update
  apt-get install -y gnupg
  mkdir -p /etc/apt/keyrings
  gpg --no-default-keyring --keyring /etc/apt/keyrings/ubuntu-archive-keyring.gpg \
      --keyserver keyserver.ubuntu.com --recv-keys 871920D1991BC93C
"
```

### 3. 网络问题

```bash
# 使用主机网络
docker buildx build --platform linux/arm64 --network=host -t your-image .
```

## 验证构建

```bash
# 构建测试镜像
docker buildx build --platform linux/arm64 \
  -t test-ubuntu-arm64:latest \
  -f docker/infrastructure/os/ubuntu/Dockerfile.template \
  . --load

# 运行测试
docker run --rm test-ubuntu-arm64:latest uname -m
# 应该输出: aarch64

# 测试包管理
docker run --rm test-ubuntu-arm64:latest apt list --installed | head -5
```

## 注意事项

1. **Docker版本**: 确保使用Docker 19.03+版本，支持Buildx
2. **网络连接**: ARM64构建可能需要更长时间，确保网络稳定
3. **资源要求**: ARM64构建可能需要更多内存和存储空间
4. **交叉编译**: 在非ARM64主机上构建可能较慢，建议在ARM64主机上构建

## 相关文件

- `Dockerfile.template` - 修复后的通用模板
- `Dockerfile.arm64.template` - ARM64专用模板
- `docker/scripts/fix-arm64-build.sh` - 自动修复脚本
- `docker/scripts/build/build-os-arm64.sh` - ARM64构建脚本
- `versions.json` - 版本配置文件
