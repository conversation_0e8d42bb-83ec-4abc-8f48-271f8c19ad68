#!/bin/bash
# ===============================================================
# 脚本名称: fix-line-endings.sh
# 描述: 修复项目中所有脚本和配置文件的行结束符问题
# 作用: 将 Windows 风格的行结束符 (CRLF) 转换为 Unix 风格 (LF)
# ===============================================================

set -euo pipefail

# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../../.." && pwd)"

# 定义颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # 无颜色

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo -e "${GREEN}行结束符修复工具${NC}"
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示帮助信息"
    echo "  --check-only        仅检查文件，不进行修复"
    echo "  --include-all       包含所有文件类型（默认只处理脚本和配置文件）"
    echo "  --dry-run           模拟运行，显示将要处理的文件但不实际修改"
    echo "  --verbose           显示详细信息"
    echo ""
    echo "描述:"
    echo "  此脚本会扫描项目中的脚本文件和配置文件，检查并修复"
    echo "  Windows 风格的行结束符 (CRLF) 问题，转换为 Unix 风格 (LF)。"
    echo ""
    echo "支持的文件类型:"
    echo "  脚本文件: *.sh"
    echo "  配置文件: *.conf, *.config, *.env, *.properties, *.ini"
    echo ""
    echo "示例:"
    echo "  $0                    # 修复所有支持的文件"
    echo "  $0 --check-only       # 仅检查，不修复"
    echo "  $0 --dry-run          # 模拟运行"
    echo "  $0 --verbose          # 显示详细信息"
    exit 0
}

# 检查文件是否包含 CRLF
has_crlf() {
    local file="$1"
    if [ -f "$file" ] && [ -r "$file" ]; then
        # 使用 grep 检查是否包含 \r 字符
        if grep -q $'\r' "$file" 2>/dev/null; then
            return 0
        fi
    fi
    return 1
}

# 转换文件的行结束符
convert_file() {
    local file="$1"
    local dry_run="$2"
    
    if [ "$dry_run" = "true" ]; then
        log_info "将转换: $file"
        return 0
    fi
    
    # 创建临时文件
    local temp_file=$(mktemp)
    
    # 转换行结束符
    if tr -d '\r' < "$file" > "$temp_file"; then
        # 替换原文件
        if mv "$temp_file" "$file"; then
            log_info "已转换: $file"
            return 0
        else
            log_error "无法替换文件: $file"
            rm -f "$temp_file"
            return 1
        fi
    else
        log_error "转换失败: $file"
        rm -f "$temp_file"
        return 1
    fi
}

# 处理文件
process_files() {
    local check_only="$1"
    local include_all="$2"
    local dry_run="$3"
    local verbose="$4"
    
    # 定义要处理的文件扩展名
    local extensions=("*.sh" "*.conf" "*.config" "*.env" "*.properties" "*.ini")
    
    if [ "$include_all" = "true" ]; then
        extensions+=("*.txt" "*.md" "*.yml" "*.yaml" "*.json" "*.xml")
    fi
    
    local total_files=0
    local crlf_files=0
    local converted_files=0
    local failed_files=0
    
    log_info "开始扫描项目文件..."
    
    # 遍历所有匹配的文件
    for ext in "${extensions[@]}"; do
        while IFS= read -r -d '' file; do
            total_files=$((total_files + 1))
            
            # 获取相对路径
            local rel_path="${file#$PROJECT_ROOT/}"
            
            if has_crlf "$file"; then
                crlf_files=$((crlf_files + 1))
                
                if [ "$verbose" = "true" ] || [ "$check_only" = "true" ] || [ "$dry_run" = "true" ]; then
                    log_warn "发现 CRLF: $rel_path"
                fi
                
                if [ "$check_only" = "false" ]; then
                    if convert_file "$file" "$dry_run"; then
                        converted_files=$((converted_files + 1))
                    else
                        failed_files=$((failed_files + 1))
                    fi
                fi
            else
                if [ "$verbose" = "true" ]; then
                    log_debug "正常: $rel_path"
                fi
            fi
        done < <(find "$PROJECT_ROOT" -name "$ext" -type f -print0 2>/dev/null)
    done
    
    # 显示统计信息
    echo ""
    log_info "扫描完成！"
    echo -e "${BLUE}统计信息:${NC}"
    echo "  总文件数: $total_files"
    echo "  包含 CRLF 的文件: $crlf_files"
    
    if [ "$check_only" = "false" ] && [ "$dry_run" = "false" ]; then
        echo "  成功转换: $converted_files"
        if [ $failed_files -gt 0 ]; then
            echo -e "  ${RED}转换失败: $failed_files${NC}"
        fi
    fi
    
    # 返回状态
    if [ $crlf_files -gt 0 ]; then
        if [ "$check_only" = "true" ]; then
            log_warn "发现 $crlf_files 个文件包含 CRLF 行结束符"
            return 1
        elif [ "$dry_run" = "true" ]; then
            log_info "模拟运行完成，将转换 $crlf_files 个文件"
            return 0
        elif [ $failed_files -gt 0 ]; then
            log_error "有 $failed_files 个文件转换失败"
            return 1
        else
            log_info "所有文件转换成功"
            return 0
        fi
    else
        log_info "所有文件都使用正确的行结束符"
        return 0
    fi
}

# 主函数
main() {
    local check_only="false"
    local include_all="false"
    local dry_run="false"
    local verbose="false"
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                ;;
            --check-only)
                check_only="true"
                shift
                ;;
            --include-all)
                include_all="true"
                shift
                ;;
            --dry-run)
                dry_run="true"
                shift
                ;;
            --verbose)
                verbose="true"
                shift
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                ;;
        esac
    done
    
    # 切换到项目根目录
    cd "$PROJECT_ROOT"
    
    log_info "项目根目录: $PROJECT_ROOT"
    
    # 处理文件
    process_files "$check_only" "$include_all" "$dry_run" "$verbose"
}

# 执行主函数
main "$@"
