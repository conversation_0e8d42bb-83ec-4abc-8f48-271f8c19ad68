#!/bin/bash

# 设置错误时退出
set -e

# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
# 获取项目根目录
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../../.." && pwd)"

# 定义目录路径
LIB_DIR="${PROJECT_ROOT}/docker/scripts/lib"
BUILD_LIB_DIR="${PROJECT_ROOT}/docker/scripts/lib"
BASE_DIR="${PROJECT_ROOT}/docker/infrastructure/jdk"
CONFIG_FILE="${PROJECT_ROOT}/docker/scripts/build/config/harbor.conf"
VERSION_FILE="${SCRIPT_DIR}/versions/jdk_versions.json"

# 设置Harbor仓库地址（优先级：命令行参数 > 环境变量 > 配置文件 > 默认值）
# 首先检查是否存在配置文件
if [ -f "$CONFIG_FILE" ]; then
    # 从配置文件读取
    source "$CONFIG_FILE"
fi

# 然后检查环境变量（覆盖配置文件）
HARBOR_REGISTRY=${HARBOR_REGISTRY:-${HARBOR_REGISTRY_DEFAULT:-""}}
HARBOR_USER=${HARBOR_USER:-${HARBOR_USER_DEFAULT:-""}}
HARBOR_PASSWORD=${HARBOR_PASSWORD:-${HARBOR_PASSWORD_DEFAULT:-""}}

# 显示Harbor配置信息函数
show_harbor_config() {
    if [ -z "$HARBOR_REGISTRY" ]; then
        echo "错误: Harbor仓库地址未配置"
        echo "请通过以下方式之一设置Harbor仓库地址:"
        echo "  1. 设置环境变量: export HARBOR_REGISTRY=your-harbor-registry:port"
        echo "  2. 创建配置文件: ${CONFIG_FILE}"
        echo "     配置文件内容示例: HARBOR_REGISTRY_DEFAULT=\"your-harbor-registry:port\""
        echo "  3. 使用命令行参数: --harbor-registry=your-harbor-registry:port"
        return 1
    else
        echo "Harbor仓库地址: $HARBOR_REGISTRY"
        if [ -n "$HARBOR_USER" ] && [ -n "$HARBOR_PASSWORD" ]; then
            echo "Harbor凭证: 已配置"
        else
            echo "Harbor凭证: 未配置（可能无法推送镜像）"
        fi
        return 0
    fi
}

# 验证目录结构和配置文件
validate_environment() {
    # 检查必要的目录
    local required_dirs=(
        "$LIB_DIR"
        "$BASE_DIR"
        "${BASE_DIR}/shared/jdk"
        "$(dirname $VERSION_FILE)"
    )
    
    for dir in "${required_dirs[@]}"
    do
        if [ ! -d "$dir" ]; then
            echo "错误: 目录不存在: $dir"
            echo "请确保目录结构完整"
            exit 1
        fi
    done
    
    # 检查必要的文件
    local required_files=(
        "$VERSION_FILE"
        "${LIB_DIR}/common.sh"
        "${BASE_DIR}/Dockerfile.template"
    )
    
    for file in "${required_files[@]}"
    do
        if [ ! -f "$file" ]; then
            echo "错误: 文件不存在: $file"
            echo "请确保所需文件已正确部署"
            exit 1
        fi
    done
    
    # 验证版本配置文件格式
    if ! jq '.' "$VERSION_FILE" > /dev/null 2>&1; then
        echo "错误: 版本配置文件格式无效: $VERSION_FILE"
        echo "请确保文件为有效的JSON格式"
        exit 1
    fi
    
    # 检查JDK安装包
    local jdk_version=$(jq -r '.versions."8".jdk_version' "$VERSION_FILE")
    if [ ! -f "${BASE_DIR}/shared/jdk/8/jdk${jdk_version}.tar.gz" ]; then
        echo "警告: JDK安装包不存在: ${BASE_DIR}/shared/jdk/8/jdk${jdk_version}.tar.gz"
        echo "请将JDK安装包放置在上述目录中再继续"
        exit 1
    fi
    
    # 检查Docker是否已安装
    if ! command -v docker >/dev/null 2>&1; then
        echo "错误: 未检测到Docker，请先安装Docker"
        exit 1
    fi
    
    # 检查jq是否安装
    if ! command -v jq >/dev/null 2>&1; then
        echo "错误: 未检测到jq命令，请先安装jq"
        echo "可以使用以下命令安装jq:"
        echo "  - Ubuntu/Debian: apt-get install -y jq"
        echo "  - RHEL/CentOS: yum install -y jq"
        exit 1
    fi
}

# 加载版本配置
load_versions() {
    # 获取支持的版本列表
    local versions=($(jq -r '.versions | keys[]' "$VERSION_FILE"))
    if [ ${#versions[@]} -eq 0 ]; then
        echo "错误: 未在配置文件中找到支持的JDK版本"
        exit 1
    fi
    
    # 获取默认变体和架构
    local default_variant=$(jq -r '.variants | keys | .[0]' "$VERSION_FILE")
    local default_arch=$(jq -r '.versions."8".supported_architectures[0]' "$VERSION_FILE")
    
    # 设置全局变量
    VERSIONS=("${versions[@]}")
    DEFAULT_VARIANT=${default_variant:-"jammy"}
    DEFAULT_ARCH=${default_arch:-"amd64"}
    
    echo "已加载版本配置:"
    echo "  支持的JDK版本: ${VERSIONS[*]}"
    echo "  默认变体: $DEFAULT_VARIANT"
    echo "  默认架构: $DEFAULT_ARCH"
}

# 检查docker.sh是否存在，存在则加载
if [ -f "${LIB_DIR}/docker.sh" ]; then
    source "${LIB_DIR}/docker.sh"  # Docker相关函数
fi

# 检查build-utils.sh是否存在，存在则加载，不存在则创建空函数
if [ -f "${BUILD_LIB_DIR}/build-utils.sh" ]; then
    source "${BUILD_LIB_DIR}/build-utils.sh"  # 构建工具函数
else
    echo "注意: build-utils.sh不存在，将使用默认空函数"
    # 定义空的create_manifest函数，防止调用时报错
    create_manifest() {
        echo "警告: create_manifest函数未实现"
        return 0
    }
fi

# 定义配置文件路径
VERSIONS_FILE="${VERSION_FILE}"

# 检查必要文件
check_required_files() {
    # 检查配置文件
    local config_files=(
        "$VERSIONS_FILE"
        "$BASE_DIR/Dockerfile.template"
    )
    
    for file in "${config_files[@]}"
    do
        if [ ! -f "$file" ]; then
            error "配置文件不存在: $file"
            exit 1
        fi
    done

    # 检查公共库文件
    if [ ! -f "${LIB_DIR}/common.sh" ]; then
        error "公共库文件不存在: ${LIB_DIR}/common.sh"
        exit 1
    fi
}

# 登录Harbor仓库
login_registry() {
    if [ -z "$HARBOR_REGISTRY" ]; then
        echo "错误: Harbor仓库地址未配置"
        return 1
    fi
    
    if [ -z "${HARBOR_USER}" ] || [ -z "${HARBOR_PASSWORD}" ]; then
        echo "警告: Harbor用户名或密码未配置，镜像可能无法推送"
        echo "请通过以下方式之一设置Harbor凭证:"
        echo "  1. 设置环境变量: export HARBOR_USER=your-username HARBOR_PASSWORD=your-password"
        echo "  2. 在配置文件中设置: ${CONFIG_FILE}"
        echo "     配置文件内容: HARBOR_USER_DEFAULT=\"your-username\" HARBOR_PASSWORD_DEFAULT=\"your-password\""
        return 1
    fi

    echo "正在登录Harbor仓库: ${HARBOR_REGISTRY}"
    printf "%s" "${HARBOR_PASSWORD}" | docker login "${HARBOR_REGISTRY}" -u "${HARBOR_USER}" --password-stdin
    return $?
}

# 构建指定版本的镜像
build_version() {
    local version=$1
    local variant=$2
    local arch=$3

    echo "开始构建 JDK $version ($variant) for $arch"

    # 检查当前系统架构是否支持目标架构
    local current_arch=$(uname -m)
    local docker_arch=$arch
    
    # 转换架构名称格式
    if [ "$current_arch" = "x86_64" ]; then
        current_arch="amd64"
    elif [ "$current_arch" = "aarch64" ]; then
        current_arch="arm64"
    fi
    
    if [ "$current_arch" != "$docker_arch" ]; then
        echo "警告: 当前系统架构 ($current_arch) 与目标架构 ($docker_arch) 不匹配，跳过构建"
        return
    fi

    # 获取Ubuntu版本
    local ubuntu_version=$(jq -r ".variants.\"$variant\".ubuntu_version // \"22.04\"" "$VERSION_FILE")
    echo "使用Ubuntu版本: $ubuntu_version"
    
    # 获取OS后缀（如 crypto-sc62）
    local os_suffix=$(jq -r ".variants.\"$variant\".os_suffix // \"\"" "$VERSION_FILE")
    
    # 获取JDK具体版本号
    local jdk_full_version=$(jq -r ".versions.\"$version\".jdk_version // \"1.8.0_172\"" "$VERSION_FILE")
    
    # 从jdk_full_version中提取更新号(例如从1.8.0_172提取172)
    local update_number=$(echo "$jdk_full_version" | grep -oP '(?<=_)\d+' || echo "0")
    
    # 从versions.json获取build号(如果有)
    local build_number=$(jq -r ".versions.\"$version\".build_number // \"\"" "$VERSION_FILE")
    local build_part=""
    if [ -n "$build_number" ]; then
        build_part="-b${build_number}"
    fi
    
    # 获取基础镜像 - 优先使用完整路径（向后兼容），如果没有则组装
    local base_image=$(jq -r ".variants.\"$variant\".base_image // empty" "$VERSION_FILE")
    if [[ -z "$base_image" ]]; then
        # 默认基础镜像
        local os_type="ubuntu"
        base_image="$HARBOR_REGISTRY/btit/infra/os/$os_type:$ubuntu_version"
        
        # 添加后缀（如果有）
        if [[ -n "$os_suffix" ]]; then
            base_image="${base_image}-${os_suffix}"
        fi
    fi
    echo "使用基础镜像: $base_image"
    
    # 提取后缀用于标签命名
    local base_image_suffix=""
    if [[ -n "$os_suffix" ]]; then
        base_image_suffix="-${os_suffix}"
    else
        # 如果没有直接配置后缀，尝试从基础镜像中提取
        if [[ "$base_image" =~ :([^:]+)$ ]]; then
            local image_tag="${BASH_REMATCH[1]}"
            # 检查标签中是否有 crypto 相关后缀
            if [[ "$image_tag" =~ -(crypto[-a-z0-9]*)$ ]]; then
                base_image_suffix="-${BASH_REMATCH[1]}"
            fi
        fi
    fi
    echo "标签后缀: $base_image_suffix"
    
    # 目标镜像使用Harbor仓库
    local registry="$HARBOR_REGISTRY"
    # 从配置文件中获取命名空间和仓库
    local namespace=$(jq -r '.build_defaults.namespace // "btit"' "$VERSION_FILE")
    local repository=$(jq -r '.build_defaults.repository // "infra/jdk"' "$VERSION_FILE")
    
    # 确保仓库路径正确
    # 如果repository已经包含完整路径（包含namespace），则直接使用
    if [[ "$repository" == "$namespace/"* ]]; then
        # repository已经包含namespace，直接使用
        :
    else
        # 将namespace添加到repository前面
        repository="$namespace/$repository"
    fi
    
    # 新的标签格式：8u172-b01-jdk-jammy-crypto-sc62
    local tag="$registry/$repository:${version}u${update_number}${build_part}-jdk-$variant$base_image_suffix"
    
    # 创建临时构建目录
    local build_dir=$(mktemp -d)
    cp "$BASE_DIR/Dockerfile.template" "$build_dir/Dockerfile"
    
    # 创建JDK目录并复制安装包
    mkdir -p "$build_dir/shared/jdk/$version"
    cp "${BASE_DIR}/shared/jdk/$version/jdk${jdk_full_version}.tar.gz" "$build_dir/shared/jdk/$version/"
    
    # 复制证书资源文件
    if [ -d "$BASE_DIR/shared/certs" ]; then
        mkdir -p "$build_dir/shared/certs"
        cp -r "$BASE_DIR/shared/certs" "$build_dir/shared/"
    fi
    
    # 替换变量
    sed -i "s|\${JDK_VERSION}|$version|g" "$build_dir/Dockerfile"
    sed -i "s|\${HARBOR_REGISTRY}|$HARBOR_REGISTRY|g" "$build_dir/Dockerfile"
    sed -i "s|\${UBUNTU_VERSION}|$ubuntu_version|g" "$build_dir/Dockerfile"
    sed -i "s|\${ARCH}|$arch|g" "$build_dir/Dockerfile"
    sed -i "s|\${BASE_IMAGE}|$base_image|g" "$build_dir/Dockerfile"

    # 构建镜像
    (
        cd "$build_dir"
        docker build --platform "linux/$arch" \
                    --build-arg "BASE_IMAGE=$base_image" \
                    -t "$tag" .
    )

    # 构建结果
    local build_result=$?

    # 如果指定了输出目录，保存生成的Dockerfile
    if [ -n "$OUTPUT_DIR" ]; then
        local target_dir="${OUTPUT_DIR}/jdk/${version}/${variant}"
        mkdir -p "$target_dir"
        cp "$build_dir/Dockerfile" "$target_dir/Dockerfile"
        echo "已保存Dockerfile到: $target_dir/Dockerfile"
    fi

    # 推送镜像
    if [ $build_result -eq 0 ]; then
        echo "推送镜像: $tag"
        docker push "$tag"
        echo "JDK ${version}u${update_number}${build_part} ($variant) for $arch 构建完成"
    else
        echo "错误: JDK ${version}u${update_number}${build_part} ($variant) for $arch 构建失败"
    fi

    # 清理临时文件
    if [ "$DEBUG" != "true" ]; then
        rm -rf "$build_dir"
    else
        echo "调试模式: 保留临时构建目录 $build_dir"
    fi

    return $build_result
}

# 解析命令行参数
parse_args() {
    VERSIONS=()
    VARIANTS=("jammy")  # 改为使用jammy变体名称
    ARCHITECTURES=("amd64")
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            --harbor-registry=*)
                HARBOR_REGISTRY="${1#*=}"
                shift
                ;;
            --harbor-registry)
                HARBOR_REGISTRY="$2"
                shift 2
                ;;
            --harbor-user=*)
                HARBOR_USER="${1#*=}"
                shift
                ;;
            --harbor-user)
                HARBOR_USER="$2"
                shift 2
                ;;
            --harbor-password=*)
                HARBOR_PASSWORD="${1#*=}"
                shift
                ;;
            --harbor-password)
                HARBOR_PASSWORD="$2"
                shift 2
                ;;
            --debug)
                DEBUG=true
                shift
                ;;
            --output-dir=*)
                OUTPUT_DIR="${1#*=}"
                shift
                ;;
            --output-dir)
                OUTPUT_DIR="$2"
                shift 2
                ;;
            *)
                VERSIONS+=("$1")
                shift
                ;;
        esac
    done
}

# 显示帮助信息
show_help() {
    echo "JDK镜像构建脚本"
    echo "用法: $0 [选项] [jdk版本...]"
    echo ""
    echo "选项:"
    echo "  -h, --help                显示帮助信息"
    echo "  --harbor-registry=<地址>  指定Harbor仓库地址 (如: registry.example.com:1443)"
    echo "  --harbor-user=<用户名>    指定Harbor用户名"
    echo "  --harbor-password=<密码>  指定Harbor密码"
    echo "  --debug                   启用调试模式，保留临时文件"
    echo "  --output-dir=<目录>       指定生成的Dockerfile输出目录"
    echo ""
    echo "示例:"
    echo "  $0                                             # 构建所有支持的JDK镜像"
    echo "  $0 8                                           # 构建JDK 8镜像"
    echo "  $0 --harbor-registry=192.168.1.100:1443 8      # 使用指定的Harbor仓库构建JDK 8镜像"
    echo ""
    echo "环境变量和配置文件:"
    echo "  HARBOR_REGISTRY=<地址>    # 环境变量设置Harbor仓库地址"
    echo "  HARBOR_USER=<用户名>      # 环境变量设置Harbor用户名"
    echo "  HARBOR_PASSWORD=<密码>    # 环境变量设置Harbor密码"
    echo "  ${CONFIG_FILE}  # 配置文件，可设置HARBOR_REGISTRY_DEFAULT等"
    echo "  DEBUG=true                # 启用调试模式，保留临时文件"
    echo "  OUTPUT_DIR=<目录>         # 指定生成的Dockerfile输出目录"
    echo ""
    echo "Harbor仓库配置优先级: 命令行参数 > 环境变量 > 配置文件 > 默认值"
    echo ""
}

# 构建所有版本镜像
build_all() {
    # 获取要构建的版本列表
    local versions
    if [ $# -gt 0 ]; then
        versions=("$@")
        echo "准备构建指定的JDK版本: ${versions[*]}"
    else
        versions=($(jq -r '.versions | keys[]' "$VERSION_FILE"))
        echo "准备构建配置文件中所有JDK版本: ${versions[*]}"
    fi

    # 遍历版本构建
    for version in "${versions[@]}"
    do
        echo "处理JDK版本: $version"
        # 检查JDK安装包是否存在
        local jdk_full_version=$(jq -r ".versions.\"$version\".jdk_version // \"1.8.0_172\"" "$VERSION_FILE")
        if [ ! -f "${BASE_DIR}/shared/jdk/$version/jdk${jdk_full_version}.tar.gz" ]; then
            echo "错误: JDK安装包不存在: ${BASE_DIR}/shared/jdk/$version/jdk${jdk_full_version}.tar.gz"
            echo "请将JDK安装包放置在正确的目录中"
            continue
        fi
        
        # 获取该版本支持的变体
        if ! jq -e ".versions.\"$version\"" "$VERSION_FILE" > /dev/null 2>&1; then
            echo "警告: 版本配置文件中未找到JDK $version 的配置，使用默认配置"
            # 默认配置
            local variants=("jammy")  # 改为使用jammy变体名称
            local architectures=("amd64")
        else
            # 从配置文件获取
            local variants=($(jq -r ".versions.\"$version\".variants[]" "$VERSION_FILE"))
            local architectures=($(jq -r ".versions.\"$version\".supported_architectures[]" "$VERSION_FILE"))
        fi
        
        echo "JDK $version 变体: ${variants[*]}"
        echo "JDK $version 架构: ${architectures[*]}"

        # 构建每个变体的每个架构
        for variant in "${variants[@]}"
        do
            for arch in "${architectures[@]}"
            do
                build_version "$version" "$variant" "$arch"
            done
        done
    done

    echo "===== JDK镜像构建完成 ====="
}

# 获取支持的JDK版本
get_supported_versions() {
    if [ -f "$VERSION_FILE" ]; then
        VERSIONS=($(jq -r '.versions | keys[]' "$VERSION_FILE"))
        if [ ${#VERSIONS[@]} -eq 0 ]; then
            echo "错误: 未在配置文件中找到支持的JDK版本"
            exit 1
        fi
    else
        # 默认支持的版本
        VERSIONS=("8")
    fi
}

# 检查命令行工具
check_commands() {
    local cmds=("docker" "jq" "sed")
    for cmd in "${cmds[@]}"
    do
        if ! command -v $cmd >/dev/null 2>&1; then
            echo "错误: 未安装必要命令: $cmd"
            exit 1
        fi
    done
}

# 主函数
main() {
    # 解析命令行参数
    parse_args "$@"
    
    # 验证环境和配置
    validate_environment
    
    # 加载版本配置
    load_versions
    
    # 显示并验证Harbor配置
    if ! show_harbor_config; then
        exit 1
    fi
    
    # 如果需要，尝试登录Harbor仓库
    if [ -n "$HARBOR_REGISTRY" ]; then
        login_registry || echo "警告: Harbor登录失败，但仍将继续构建"
    fi
    
    # 如果没有指定版本，使用所有支持的版本
    if [ ${#VERSIONS[@]} -eq 0 ]; then
        get_supported_versions
    fi
    
    # 显示构建信息
    echo "构建信息:"
    echo "  基础镜像: 来自OS构建的Ubuntu镜像"
    echo "  目标Harbor仓库: $HARBOR_REGISTRY"
    
    # 从配置文件中获取命名空间和仓库路径
    local namespace=$(jq -r '.build_defaults.namespace // "btit"' "$VERSION_FILE")
    local repository=$(jq -r '.build_defaults.repository // "infra/jdk"' "$VERSION_FILE")
    
    # 组合完整的仓库路径
    if [[ "$repository" != */* ]]; then
        repository="$namespace/$repository"
    fi
    
    echo "  镜像路径: $HARBOR_REGISTRY/$repository"
    echo "  JDK版本: ${VERSIONS[*]}"
    
    # 显示详细版本信息
    for version in "${VERSIONS[@]}"
    do
        local jdk_detailed=$(jq -r ".versions.\"$version\".jdk_version // \"未知\"" "$VERSION_FILE")
        echo "    - JDK $version: 详细版本 $jdk_detailed"
    done
    
    # 构建镜像
    echo "开始构建JDK镜像..."
    build_all "${VERSIONS[@]}"
    echo "JDK镜像构建完成"
}

# 执行主函数
main "$@"
