handlers = 1catalina.org.apache.juli.FileHandler, 2localhost.org.apache.juli.FileHandler, 3manager.org.apache.juli.File<PERSON><PERSON>ler, 4host-manager.org.apache.juli.FileHandler, java.util.logging.ConsoleHandler

.handlers = 1catalina.org.apache.juli.FileHandler, java.util.logging.ConsoleHandler

############################################################
# 处理器配置
############################################################

# 1catalina 处理器 - 主日志文件
1catalina.org.apache.juli.FileHandler.level = FINE
1catalina.org.apache.juli.FileHandler.directory = ${catalina.base}/logs/tomcat
1catalina.org.apache.juli.FileHandler.prefix = catalina.
# 启用日志轮转
1catalina.org.apache.juli.FileHandler.rotatable = true
# 最大单个文件大小为10MB
1catalina.org.apache.juli.FileHandler.maxFileSize = 10485760
# 保留10个日志文件
1catalina.org.apache.juli.FileHandler.maxFiles = 10

# 2localhost 处理器
2localhost.org.apache.juli.FileHandler.level = FINE
2localhost.org.apache.juli.FileHandler.directory = ${catalina.base}/logs/tomcat
2localhost.org.apache.juli.FileHandler.prefix = localhost.
2localhost.org.apache.juli.FileHandler.rotatable = true
2localhost.org.apache.juli.FileHandler.maxFileSize = 10485760
2localhost.org.apache.juli.FileHandler.maxFiles = 10

# 3manager 处理器
3manager.org.apache.juli.FileHandler.level = FINE
3manager.org.apache.juli.FileHandler.directory = ${catalina.base}/logs/tomcat
3manager.org.apache.juli.FileHandler.prefix = manager.
3manager.org.apache.juli.FileHandler.rotatable = true
3manager.org.apache.juli.FileHandler.maxFileSize = 10485760
3manager.org.apache.juli.FileHandler.maxFiles = 5

# 4host-manager 处理器
4host-manager.org.apache.juli.FileHandler.level = FINE
4host-manager.org.apache.juli.FileHandler.directory = ${catalina.base}/logs/tomcat
4host-manager.org.apache.juli.FileHandler.prefix = host-manager.
4host-manager.org.apache.juli.FileHandler.rotatable = true
4host-manager.org.apache.juli.FileHandler.maxFileSize = 10485760
4host-manager.org.apache.juli.FileHandler.maxFiles = 5

# 控制台处理器
java.util.logging.ConsoleHandler.level = FINE
java.util.logging.ConsoleHandler.formatter = org.apache.juli.OneLineFormatter

############################################################
# 日志等级配置
############################################################

org.apache.catalina.core.ContainerBase.[Catalina].[localhost].level = INFO
org.apache.catalina.core.ContainerBase.[Catalina].[localhost].handlers = 2localhost.org.apache.juli.FileHandler

org.apache.catalina.core.ContainerBase.[Catalina].[localhost].[/manager].level = INFO
org.apache.catalina.core.ContainerBase.[Catalina].[localhost].[/manager].handlers = 3manager.org.apache.juli.FileHandler

org.apache.catalina.core.ContainerBase.[Catalina].[localhost].[/host-manager].level = INFO
org.apache.catalina.core.ContainerBase.[Catalina].[localhost].[/host-manager].handlers = 4host-manager.org.apache.juli.FileHandler 