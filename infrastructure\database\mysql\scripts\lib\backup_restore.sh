#!/bin/bash
# MySQL 备份和恢复功能
# 包含数据库备份和恢复相关功能

# 引入依赖
source "$(dirname "${BASH_SOURCE[0]}")/utils.sh"

# 执行手动备份
do_backup() {
  local product="$1"
  log_info "正在为 $product 执行手动备份..."
  
  # 检查至少一个主节点在运行
  local master1=$(get_master1_name "$product")
  local master2=$(get_master2_name "$product")
  local master1_running=$(is_container_running "$master1" && echo "yes" || echo "no")
  local master2_running=$(is_container_running "$master2" && echo "yes" || echo "no")
  
  local node=""
  if [ "$master1_running" = "yes" ]; then
    node=$master1
  elif [ "$master2_running" = "yes" ]; then
    node=$master2
  else
    log_error "MySQL服务未运行，无法执行备份"
    return 1
  fi
  
  log_info "使用节点 $node 执行备份..."
  
  # 创建备份日志目录
  local base_dir="$(dirname "$(dirname "$(dirname "${BASH_SOURCE[0]}")")")"
  mkdir -p "$base_dir/logs/$product/backup"
  local log_file="$base_dir/logs/$product/backup/manual_backup_$(date +%Y%m%d_%H%M%S).log"
  
  # 直接执行备份脚本
  docker exec -e PRODUCT=$product $node bash /scripts/backup.sh | tee "$log_file"
  
  if [ $? -eq 0 ]; then
    log_success "备份完成"
    log_info "备份日志已保存到: $log_file"
    return 0
  else
    log_error "备份失败"
    log_info "查看日志了解详情: $log_file"
    return 1
  fi
}

# 恢复数据库
do_restore() {
  local product="$1"
  log_info "准备恢复 $product 数据库..."
  
  # 检查至少一个主节点在运行
  local master1=$(get_master1_name "$product")
  local master2=$(get_master2_name "$product")
  local master1_running=$(is_container_running "$master1" && echo "yes" || echo "no")
  local master2_running=$(is_container_running "$master2" && echo "yes" || echo "no")
  
  local node=""
  if [ "$master1_running" = "yes" ]; then
    node=$master1
  elif [ "$master2_running" = "yes" ]; then
    node=$master2
  else
    log_error "MySQL服务未运行，无法执行恢复"
    return 1
  fi
  
  log_warning "警告: 此操作将覆盖现有数据。建议在操作前停止应用服务。"
  if ! confirm_action "是否继续?"; then
    log_info "操作已取消"
    return 0
  fi
  
  log_info "使用节点 $node 执行恢复..."
  
  # 启动交互式恢复
  docker exec -it -e PRODUCT=$product $node bash /scripts/restore.sh
  
  if [ $? -eq 0 ]; then
    log_success "恢复操作完成"
    return 0
  else
    log_error "恢复操作失败"
    return 1
  fi
} 