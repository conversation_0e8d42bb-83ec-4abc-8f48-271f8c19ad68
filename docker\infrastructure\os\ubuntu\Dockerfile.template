FROM docker.zysproxy.online/ubuntu:{{VERSION}}

# 接收构建参数
ARG CRYPTO_VERSION
ARG ARCH
ARG INSTALL_CARD_MNGR=false
ARG INSTALL_SC62=false
ARG INSTALL_SC34=false

# 设置环境变量
ENV LANG=C.UTF-8 \
    LC_ALL=C.UTF-8 \
    TERM=xterm-256color \
    DEBIAN_FRONTEND=noninteractive \
    TZ=Asia/Shanghai \
    LD_LIBRARY_PATH=/usr/lib64:/usr/lib/x86_64-linux-gnu

# 添加元数据
LABEL maintainer="维护团队 <<EMAIL>>" \
      description="基础Ubuntu {{VERSION}} 镜像，含NETCA_CRYPTO支持" \
      version="{{VERSION}}" \
      codename="{{CODENAME}}" \
      crypto_version="${CRYPTO_VERSION}" \
      architecture="${ARCH}" \
      card_manager="${INSTALL_CARD_MNGR}" \
      sansec_sc62="${INSTALL_SC62}" \
      sansec_sc34="${INSTALL_SC34}"

# 配置apt源并安装必要软件包
RUN set -ex \
    && cp /etc/apt/sources.list /etc/apt/sources.list.bak \
    && echo "deb http://mirrors.aliyun.com/ubuntu/ {{CODENAME}} main restricted universe multiverse\ndeb http://mirrors.aliyun.com/ubuntu/ {{CODENAME}}-updates main restricted universe multiverse\ndeb http://mirrors.aliyun.com/ubuntu/ {{CODENAME}}-backports main restricted universe multiverse\ndeb http://mirrors.aliyun.com/ubuntu/ {{CODENAME}}-security main restricted universe multiverse\n\ndeb http://archive.ubuntu.com/ubuntu/ {{CODENAME}} main restricted universe multiverse\ndeb http://archive.ubuntu.com/ubuntu/ {{CODENAME}}-updates main restricted universe multiverse\ndeb http://archive.ubuntu.com/ubuntu/ {{CODENAME}}-backports main restricted universe multiverse\ndeb http://security.ubuntu.com/ubuntu/ {{CODENAME}}-security main restricted universe multiverse" > /etc/apt/sources.list \
    # 第一步：绕过GPG验证，先安装gnupg
    && apt-get update --allow-insecure-repositories \
    && DEBIAN_FRONTEND=noninteractive apt-get install -y --allow-unauthenticated gnupg ca-certificates \
    # 第二步：添加GPG密钥
    && apt-key adv --keyserver keyserver.ubuntu.com --recv-keys 871920D1991BC93C \
    && chmod 644 /etc/apt/trusted.gpg.d/*.gpg \
    # 第三步：正常更新并安装软件包
    && apt-get update \
    && DEBIAN_FRONTEND=noninteractive apt-get install -y \
       apt-utils \
       {{PACKAGES}} \
       dialog \
       libterm-readline-perl-perl \
       sqlite3 \
       libsqlite3-dev \
       libgmp10 \
       libgmp3-dev \
       libldap-2.5-0 \
       libssl3 \
       libssl-dev \
       openssl \
       vim-tiny \
       libzip4 \
       file \
       libxml2 \
       unzip \
       build-essential \
       pciutils \
       dnsutils \
       curl \
       ca-certificates \
       apt-transport-https \
       kmod \
       tzdata \
    && ln -snf /usr/share/zoneinfo/$TZ /etc/localtime \
    && echo $TZ > /etc/timezone \
    && dpkg-reconfigure -f noninteractive tzdata \
    # 创建必要的符号链接
    && ln -sf /usr/lib/x86_64-linux-gnu/libgmp.so.10 /usr/lib/x86_64-linux-gnu/libgmp.so.3 \
    && if [ ! -e /usr/lib/x86_64-linux-gnu/libzip.so.1 ]; then \
         ln -sf /usr/lib/x86_64-linux-gnu/libzip.so.4 /usr/lib/x86_64-linux-gnu/libzip.so.1; \
       fi \
    # 创建arch/x64符号链接
    && KERNEL_SRC="/lib/modules/${KERNEL_VERSION}/build" \
    && if [ -d "${KERNEL_SRC}/arch/x86" ] && [ ! -d "${KERNEL_SRC}/arch/x64" ]; then \
         echo "创建arch/x64到arch/x86的符号链接" \
         && ln -sf "${KERNEL_SRC}/arch/x86" "${KERNEL_SRC}/arch/x64"; \
       fi \
    # 清理
    && apt-get clean \
    && apt-get autoremove -y \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /tmp/* /var/tmp/*

# 配置LD_LIBRARY_PATH
RUN echo "export LD_LIBRARY_PATH=${LD_LIBRARY_PATH}" >> /etc/profile

# Copy NETCA_CRYPTO files
COPY shared/crypto-files/${CRYPTO_VERSION}/${ARCH}/NETCA_CRYPTO_linux32_64 /tmp/NETCA_CRYPTO_linux32_64/

# Install NETCA_CRYPTO and related components
RUN set -ex \
    && mkdir -p /usr/lib64 /var/log \
    && cd /tmp/NETCA_CRYPTO_linux32_64 \
    && chmod +x setup.sh \
    # 安装NETCA CRYPTO
    && echo "开始安装NETCA CRYPTO组件..." \
    && bash -x ./setup.sh /usr/lib64 CRYPTO move \
    # 根据条件安装密码卡管理JNI库
    && if [ "$INSTALL_CARD_MNGR" = "true" ]; then \
         echo "开始安装NetcaCardMngr组件..." \
         && bash -x ./setup.sh /usr/lib64 NetcaCardMngr move; \
       fi \
    # 根据条件安装SansecCard_SC62组件
    && if [ "$INSTALL_SC62" = "true" ]; then \
         echo "开始安装SansecCard_SC62组件..." \
         && bash -x ./setup.sh /usr/lib64 SansecCard_SC62 Library ; \
       fi \
    # 根据条件安装SansecCard_SC34组件
    && if [ "$INSTALL_SC34" = "true" ]; then \
         echo "开始安装SansecCard_SC34组件..." \
         && bash -x ./setup.sh /usr/lib64 SansecCard Library ; \
       fi \
    # 创建必要的库文件链接
    && for lib in libsqlite3.so.0 libssl.so.3 libcrypto.so.3; do \
         if [ ! -e "/usr/lib64/$lib" ] && [ -e "/usr/lib/x86_64-linux-gnu/$lib" ]; then \
           ln -sf "/usr/lib/x86_64-linux-gnu/$lib" "/usr/lib64/$lib"; \
         fi \
       done \
    # 清理
    && rm -rf /tmp/NETCA_CRYPTO_linux32_64

WORKDIR /app

CMD ["/bin/bash"] 


