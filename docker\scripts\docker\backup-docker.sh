#!/bin/bash
# ===============================================================
# 脚本名称: backup-docker.sh
# 描述: Docker安装备份工具
# ===============================================================

set -e

# 全局常量
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly BASE_DIR="$(cd "${SCRIPT_DIR}/../../.." && pwd)"
readonly LIB_DIR="${BASE_DIR}/docker/scripts/lib"

# 导入依赖库
source "${LIB_DIR}/common.sh"
source "${LIB_DIR}/logger.sh"

# 默认日志配置
LOG_LEVEL="INFO"
LOG_FILE="logs/backup-docker.log"

# 初始化日志系统
init_logger "$LOG_LEVEL" "$LOG_FILE"

# 检测文件格式并修复（如果需要）
if [ -f "$0" ] && grep -q $'\r' "$0"; then
    log_info "检测到 Windows 换行符，正在修复..."
    TMP_FILE=$(mktemp)
    tr -d '\r' < "$0" > "$TMP_FILE"
    cat "$TMP_FILE" > "$0"
    rm -f "$TMP_FILE"
    log_info "文件格式已修复，重新执行脚本..."
    exec bash "$0" "$@"
fi

# 显示帮助信息
show_help() {
    cat << EOF
用法: $(basename "$0") [选项]

描述:
  备份当前Docker安装的二进制文件、配置和服务文件。
  备份将保存到指定目录，并提供恢复脚本。

选项:
  -h, --help             显示帮助信息
  -d, --backup-dir DIR   指定备份目录 (默认: /var/backup/docker/<时间戳>)
  --log-level LEVEL      指定日志级别 (DEBUG, INFO, WARN, ERROR)

示例:
  # 使用默认配置备份
  $(basename "$0")
  
  # 指定备份目录
  $(basename "$0") -d /path/to/backup
EOF
}

# 检测Docker版本
detect_docker_version() {
    if command -v docker >/dev/null 2>&1; then
        local version=$(docker --version | grep -oP "Docker version \K[0-9]+\.[0-9]+\.[0-9]+" 2>/dev/null || echo "")
        if [ -n "$version" ]; then
            echo "$version"
        else
            echo "未知"
        fi
    else
        echo "未安装"
    fi
}

# 备份Docker
backup_docker() {
    local backup_dir="$1"
    
    if [ -z "$backup_dir" ]; then
        backup_dir="/var/backup/docker/$(date +%Y%m%d_%H%M%S)"
    fi
    
    log_info "开始备份Docker到: $backup_dir"
    
    # 创建备份目录
    mkdir -p "$backup_dir"
    
    # 检测Docker版本
    local docker_version=$(detect_docker_version)
    
    if [ "$docker_version" = "未安装" ]; then
        log_warn "未检测到Docker安装，备份可能不完整"
    else
        log_info "检测到Docker版本: $docker_version"
        echo "$docker_version" > "$backup_dir/VERSION"
    fi
    
    # 检测Docker服务状态
    if systemctl is-active docker >/dev/null 2>&1; then
        log_info "Docker服务状态: 运行中"
        systemctl status docker --no-pager > "$backup_dir/docker.status" 2>/dev/null || true
    else
        log_warn "Docker服务状态: 未运行"
    fi
    
    # 备份二进制文件
    log_info "备份Docker二进制文件..."
    local bins_backed_up=()
    
    for bin in docker containerd containerd-shim containerd-shim-runc-v2 ctr runc docker-compose; do
        if command -v $bin >/dev/null 2>&1; then
            log_info "备份二进制文件: $bin"
            cp $(which $bin) "$backup_dir/" 2>/dev/null && bins_backed_up+=("$bin") || log_warn "无法备份 $bin"
        fi
    done
    
    # 备份配置文件
    log_info "备份Docker配置文件..."
    if [ -d "/etc/docker" ]; then
        log_info "备份 /etc/docker 目录"
        cp -r /etc/docker "$backup_dir/" 2>/dev/null || log_warn "无法备份 /etc/docker 目录"
    else
        log_warn "Docker配置目录不存在: /etc/docker"
    fi
    
    # 备份服务文件
    log_info "备份Docker服务文件..."
    local service_files_backed_up=()
    
    for service_file in /usr/lib/systemd/system/docker.service /usr/lib/systemd/system/docker.socket /etc/systemd/system/containerd.service; do
        if [ -f "$service_file" ]; then
            log_info "备份服务文件: $service_file"
            cp "$service_file" "$backup_dir/$(basename $service_file)" 2>/dev/null && service_files_backed_up+=("$(basename $service_file)") || log_warn "无法备份 $service_file"
        fi
    done
    
    # 如果存在，备份docker-compose.yml文件
    if [ -f "/docker-compose.yml" ]; then
        log_info "备份 /docker-compose.yml"
        cp "/docker-compose.yml" "$backup_dir/" 2>/dev/null || log_warn "无法备份 /docker-compose.yml"
    fi
    
    # 记录Docker信息
    log_info "收集Docker信息..."
    
    if command -v docker >/dev/null 2>&1; then
        log_info "收集Docker详细信息..."
        mkdir -p "$backup_dir/info"
        
        # 收集docker info
        docker info > "$backup_dir/info/docker_info.txt" 2>/dev/null || log_warn "无法收集 docker info"
        
        # 收集docker容器列表
        docker ps -a > "$backup_dir/info/docker_containers.txt" 2>/dev/null || log_warn "无法收集容器列表"
        
        # 收集docker镜像列表
        docker images > "$backup_dir/info/docker_images.txt" 2>/dev/null || log_warn "无法收集镜像列表"
        
        # 收集docker网络列表
        docker network ls > "$backup_dir/info/docker_networks.txt" 2>/dev/null || log_warn "无法收集网络列表"
        
        # 收集docker卷列表
        docker volume ls > "$backup_dir/info/docker_volumes.txt" 2>/dev/null || log_warn "无法收集卷列表"
    fi
    
    # 创建备份摘要
    log_info "创建备份摘要..."
    cat > "$backup_dir/BACKUP_SUMMARY.txt" << EOF
========================================
Docker 备份摘要
========================================
备份时间: $(date)
Docker版本: $docker_version
主机名: $(hostname)
操作系统: $(cat /etc/os-release | grep PRETTY_NAME | cut -d'"' -f2)

备份的二进制文件:
$(for bin in "${bins_backed_up[@]}"; do echo "- $bin"; done)

备份的服务文件:
$(for service in "${service_files_backed_up[@]}"; do echo "- $service"; done)

备份的配置目录:
$(if [ -d "$backup_dir/docker" ]; then echo "- /etc/docker"; else echo "- 无"; fi)

备份完成时间: $(date)
========================================
EOF
    
    # 创建恢复脚本
    log_info "创建恢复脚本..."
    cat > "$backup_dir/restore.sh" << 'EOF'
#!/bin/bash
# Docker恢复脚本
set -e

BACKUP_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
echo "正在从备份目录恢复Docker: $BACKUP_DIR"

# 停止现有服务
systemctl stop docker containerd 2>/dev/null || true

# 恢复二进制文件
for bin in docker containerd containerd-shim containerd-shim-runc-v2 ctr runc docker-compose; do
    if [ -f "$BACKUP_DIR/$bin" ]; then
        echo "恢复 $bin 二进制文件"
        cp "$BACKUP_DIR/$bin" "/usr/bin/$bin"
        chmod +x "/usr/bin/$bin"
    fi
done

# 恢复配置目录
if [ -d "$BACKUP_DIR/docker" ]; then
    echo "恢复 Docker 配置目录"
    cp -r "$BACKUP_DIR/docker" /etc/
fi

# 恢复服务文件
for service in docker.service docker.socket containerd.service; do
    if [ -f "$BACKUP_DIR/$service" ]; then
        echo "恢复 $service 服务文件"
        cp "$BACKUP_DIR/$service" "/usr/lib/systemd/system/$service"
    fi
done

# 重新加载systemd
systemctl daemon-reload

# 启动服务
systemctl start containerd docker

echo "Docker恢复完成，版本信息:"
docker --version

exit 0
EOF
    
    chmod +x "$backup_dir/restore.sh"
    
    log_info "Docker备份完成"
    log_info "备份目录: $backup_dir"
    log_info "恢复命令: $backup_dir/restore.sh"
}

# 解析命令行参数
parse_args() {
    BACKUP_DIR=""
    
    while [[ $# -gt 0 ]]; do
        case "$1" in
            -h|--help)
                show_help
                exit 0
                ;;
            -d|--backup-dir)
                BACKUP_DIR="$2"
                shift 2
                ;;
            --log-level)
                LOG_LEVEL="$2"
                init_logger "$LOG_LEVEL" "$LOG_FILE"
                shift 2
                ;;
            *)
                echo "未知选项: $1" >&2
                show_help
                exit 1
                ;;
        esac
    done
}

# 主函数
main() {
    log_info "开始Docker备份流程..."

    check_root
    parse_args "$@"
    backup_docker "$BACKUP_DIR"

    log_info "Docker备份流程完成"
}

# 执行主函数
main "$@" 