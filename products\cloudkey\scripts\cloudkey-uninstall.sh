#!/bin/bash
#
# CloudKey 卸载脚本
# 用于完全卸载 CloudKey 服务（停止并移除容器、网络）
#

# 引入公共库函数
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${SCRIPT_DIR}/common_lib.sh"

# 显示帮助信息
show_help() {
  cat << EOF
用法: $(basename "$0") [选项]

CloudKey 卸载脚本，用于完全卸载 CloudKey 服务（停止并移除容器、网络）。

选项:
  -h, --help    显示此帮助信息并退出
  -v, --volumes 同时移除数据卷（将删除所有数据）

功能:
  - 停止并移除所有服务容器
  - 移除网络连接
  - 可选择性地移除数据卷
  
示例:
  $(basename "$0")             卸载服务（保留数据）
  $(basename "$0") --volumes   卸载服务并删除所有数据
  $(basename "$0") --help      显示帮助信息

相关脚本:
  cloudkey-start.sh     启动服务
  cloudkey-stop.sh      停止服务（不移除容器）
  cloudkey-restart.sh   重启服务
  cloudkey-status.sh    查看服务状态
EOF
}

# 默认不删除数据卷
REMOVE_VOLUMES=false

# 处理命令行参数
for arg in "$@"; do
  case $arg in
    -h|--help)
      show_help
      exit 0
      ;;
    -v|--volumes)
      REMOVE_VOLUMES=true
      shift
      ;;
    *)
      echo -e "${RED}未知参数: $arg${NC}"
      echo -e "${YELLOW}使用 -h 或 --help 查看帮助信息${NC}"
      exit 1
      ;;
  esac
done

set -e

# 检查Docker环境
check_docker_environment || exit 1

# 获取Docker Compose命令
DOCKER_COMPOSE_CMD=$(get_docker_compose_cmd)

# 项目根目录（上级目录）
PROJECT_DIR="$(cd "${SCRIPT_DIR}/.." && pwd)"

# 卸载服务
echo -e "${BLUE}正在卸载 CloudKey 服务...${NC}"
cd "${PROJECT_DIR}"

# 检查服务是否存在
CONTAINER_ID=$($DOCKER_COMPOSE_CMD ps -q cloudkey 2>/dev/null)
if [ -z "${CONTAINER_ID}" ]; then
  echo -e "${YELLOW}未发现 CloudKey 服务容器，可能已经被卸载。${NC}"
fi

# 使用 docker-compose down 停止并移除容器和网络
if [ "$REMOVE_VOLUMES" = true ]; then
  echo -e "${RED}警告: 将移除所有容器、网络和数据卷！${NC}"
  echo -e "${RED}所有数据将被删除且无法恢复！${NC}"
  
  read -p "确定要继续吗？(y/n): " confirm
  if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ]; then
    echo -e "${YELLOW}正在移除所有容器、网络和数据卷...${NC}"
    $DOCKER_COMPOSE_CMD down -v
    echo -e "${GREEN}CloudKey 服务已被完全卸载，包括所有数据。${NC}"
  else
    echo -e "${YELLOW}操作已取消。${NC}"
    exit 0
  fi
else
  echo -e "${YELLOW}正在移除所有容器和网络（保留数据卷）...${NC}"
  $DOCKER_COMPOSE_CMD down
  echo -e "${GREEN}CloudKey 服务容器和网络已被移除，数据卷已保留。${NC}"
fi

echo -e "${BLUE}卸载完成。如需重新安装，请运行 cloudkey-start.sh 脚本。${NC}" 