#!/bin/bash
# MySQL 性能监控功能
# 包含性能监控和诊断相关功能

# 引入依赖
source "$(dirname "${BASH_SOURCE[0]}")/utils.sh"

# 性能检查菜单
select_performance_check() {
  echo "选择性能检查项目:"
  echo "1) 连接状态"
  echo "2) 查询缓存状态"
  echo "3) 表状态"
  echo "4) InnoDB 状态"
  echo "5) 慢查询日志"
  echo "6) 所有性能检查"
  echo -n "请选择 [1-6]: "
  local perf_choice
  read perf_choice
  
  echo "$perf_choice"
}

# 检查连接状态
check_connections() {
  local node="$1"
  local password="$2"
  
  log_info "检查连接状态..."
  docker exec $node mysql -uroot -p"$password" -e "SHOW STATUS LIKE 'Threads_%';"
  docker exec $node mysql -uroot -p"$password" -e "SHOW VARIABLES LIKE 'max_connections';"
  docker exec $node mysql -uroot -p"$password" -e "SHOW STATUS LIKE 'Max_used_connections';"
}

# 检查查询缓存
check_query_cache() {
  local node="$1"
  local password="$2"
  
  log_info "检查查询缓存状态..."
  docker exec $node mysql -uroot -p"$password" -e "SHOW VARIABLES LIKE '%query_cache%';"
  docker exec $node mysql -uroot -p"$password" -e "SHOW STATUS LIKE 'Qcache_%';"
}

# 检查表状态
check_tables() {
  local node="$1"
  local password="$2"
  local product="$3"
  
  log_info "检查表状态..."
  docker exec $node mysql -uroot -p"$password" -e "USE $product; SHOW TABLE STATUS;"
}

# 检查InnoDB状态
check_innodb() {
  local node="$1"
  local password="$2"
  
  log_info "检查 InnoDB 状态..."
  docker exec $node mysql -uroot -p"$password" -e "SHOW ENGINE INNODB STATUS\G"
}

# 检查慢查询日志
check_slow_queries() {
  local node="$1"
  
  log_info "检查慢查询日志..."
  docker exec $node ls -la /var/log/mysql/mysql-slow.log 2>/dev/null || echo "慢查询日志文件不存在"
  echo "显示最新的10条慢查询记录:"
  docker exec $node bash -c "if [ -f /var/log/mysql/mysql-slow.log ]; then tail -n 50 /var/log/mysql/mysql-slow.log | grep -A 10 'Query_time' | head -n 50; else echo '无慢查询记录'; fi"
}

# 检查数据库性能
check_performance() {
  local product="$1"
  log_info "检查 $product MySQL 性能..."
  
  local node=$(select_node "$product")
  if [ $? -ne 0 ]; then
    return 1
  fi
  
  # 检查节点是否运行
  if ! is_container_running "$node"; then
    log_error "节点 $node 未运行"
    return 1
  fi
  
  local password=$(get_mysql_root_password)
  local perf_choice=$(select_performance_check)
  
  case $perf_choice in
    1)
      check_connections "$node" "$password"
      ;;
    2)
      check_query_cache "$node" "$password"
      ;;
    3)
      check_tables "$node" "$password" "$product"
      ;;
    4)
      check_innodb "$node" "$password"
      ;;
    5)
      check_slow_queries "$node"
      ;;
    6)
      log_info "执行所有性能检查..."
      check_connections "$node" "$password"
      
      check_query_cache "$node" "$password"
      
      log_info "检查InnoDB状态摘要..."
      docker exec $node mysql -uroot -p"$password" -e "SHOW ENGINE INNODB STATUS\G" | grep -E "Buffer pool|Log sequence|MySQL thread|transactions|queries"
      
      log_info "检查慢查询概况..."
      docker exec $node bash -c "if [ -f /var/log/mysql/mysql-slow.log ]; then echo '慢查询日志存在'; else echo '慢查询日志不存在'; fi"
      ;;
    *)
      log_error "无效的选择"
      return 1
      ;;
  esac
  
  return 0
} 