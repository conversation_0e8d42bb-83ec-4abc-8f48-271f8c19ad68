#!/bin/bash
#
# Redis 主从切换脚本
# 用于动态切换Redis主从关系
#

# 设置错误时退出
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # 无颜色

# 脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
BASE_DIR="$(dirname "$SCRIPT_DIR")"

# 参数解析
PRODUCT_NAME=${1:-"cloudkey"}
NEW_MASTER=${2}

# 显示帮助信息
if [[ "$PRODUCT_NAME" == "--help" || "$PRODUCT_NAME" == "-h" || -z "$NEW_MASTER" ]]; then
  echo "用法: $0 <产品名称> <新主节点类型>"
  echo "产品名称: cloudkey, eseal, quicksign (必须指定)"
  echo "新主节点类型: master 或 slave (必须指定)"
  echo "示例:"
  echo "  $0 cloudkey slave  # 将当前的从节点提升为主节点，原主节点变为从节点"
  echo "  $0 cloudkey master # 将当前的主节点确认为主节点（从节点仍为从节点）"
  exit 1
fi

# 检查新主节点参数
if [[ "$NEW_MASTER" != "master" && "$NEW_MASTER" != "slave" ]]; then
  echo -e "${RED}错误: 新主节点类型必须是 'master' 或 'slave'${NC}"
  exit 1
fi

# 根据产品名称设置容器名称
MASTER_CONTAINER="${PRODUCT_NAME}-redis-master"
SLAVE_CONTAINER="${PRODUCT_NAME}-redis-slave"

# 获取Redis密码
REDIS_PASSWORD=""
if [ -f "$BASE_DIR/secrets/redis_password.txt" ]; then
  REDIS_PASSWORD=$(cat "$BASE_DIR/secrets/redis_password.txt")
  
  # 检查容器是否存在
  if ! docker ps | grep -q "$MASTER_CONTAINER"; then
    echo -e "${RED}错误: 主节点容器 $MASTER_CONTAINER 未运行${NC}"
    exit 1
  fi
  
  # 验证密码有效性
  echo -e "${YELLOW}验证密码有效性...${NC}"
  
  # 直接测试密码
  RESULT=$(docker exec -i "$MASTER_CONTAINER" redis-cli -a "$REDIS_PASSWORD" ping 2>&1)
  
  if echo "$RESULT" | grep -q "PONG"; then
    echo -e "${GREEN}Redis密码验证通过${NC}"
  else
    echo -e "${YELLOW}Redis密码验证失败，尝试无密码连接...${NC}"
    
    NOPW_RESULT=$(docker exec -i "$MASTER_CONTAINER" redis-cli ping 2>&1)
    
    if echo "$NOPW_RESULT" | grep -q "PONG"; then
      echo -e "${GREEN}无密码连接成功${NC}"
      REDIS_PASSWORD=""
    else
      echo -e "${RED}无法连接到Redis服务器${NC}"
      exit 1
    fi
  fi
else
  echo -e "${YELLOW}警告: 找不到密码文件 $BASE_DIR/secrets/redis_password.txt${NC}"
  echo -e "${YELLOW}尝试无密码连接...${NC}"
  if docker ps | grep -q "$MASTER_CONTAINER" && \
     docker exec -i "$MASTER_CONTAINER" redis-cli ping | grep -q "PONG"; then
    echo -e "${GREEN}无密码连接成功${NC}"
  else
    echo -e "${RED}无法连接到Redis服务器${NC}"
    exit 1
  fi
fi

# 获取当前主从状态
echo -e "${BLUE}[1/3] 检查当前主从状态...${NC}"

# 检查主节点是否运行
if ! docker ps | grep -q "$MASTER_CONTAINER"; then
  echo -e "${RED}错误: 主节点容器 $MASTER_CONTAINER 未运行${NC}"
  exit 1
fi

# 检查从节点是否运行
if ! docker ps | grep -q "$SLAVE_CONTAINER"; then
  echo -e "${RED}错误: 从节点容器 $SLAVE_CONTAINER 未运行${NC}"
  exit 1
fi

# 获取主节点IP
MASTER_IP=$(docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' $MASTER_CONTAINER)
echo -e "${YELLOW}当前主节点IP: ${GREEN}$MASTER_IP${NC}"

# 获取从节点IP
SLAVE_IP=$(docker inspect -f '{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' $SLAVE_CONTAINER)
echo -e "${YELLOW}当前从节点IP: ${GREEN}$SLAVE_IP${NC}"

# 执行切换
echo -e "${BLUE}[2/3] 执行主从切换...${NC}"

# 定义执行Redis命令的函数
exec_redis_cmd() {
  local container=$1
  local cmd=$2
  
  local result
  
  if [ -z "$REDIS_PASSWORD" ]; then
    # 无密码模式
    result=$(docker exec -i $container redis-cli $cmd 2>&1)
  else
    # 带密码模式
    result=$(docker exec -i $container redis-cli -a "$REDIS_PASSWORD" $cmd 2>&1)
  fi
  
  # 返回结果
  echo "$result"
}

if [[ "$NEW_MASTER" == "slave" ]]; then
  echo -e "${YELLOW}将从节点 $SLAVE_CONTAINER 提升为主节点...${NC}"
  
  # 先将从节点提升为主节点
  exec_redis_cmd $SLAVE_CONTAINER "REPLICAOF NO ONE"
  sleep 2
  
  # 再将原主节点降级为从节点
  # 先设置masterauth，再设置replicaof
  exec_redis_cmd $MASTER_CONTAINER "CONFIG SET masterauth $REDIS_PASSWORD"
  exec_redis_cmd $MASTER_CONTAINER "REPLICAOF $SLAVE_IP 6379"
  
  echo -e "${GREEN}主从切换完成! 新的主节点是: $SLAVE_CONTAINER (IP: $SLAVE_IP)${NC}"
elif [[ "$NEW_MASTER" == "master" ]]; then
  echo -e "${YELLOW}确认 $MASTER_CONTAINER 为主节点，确保从节点正确复制...${NC}"
  
  # 确保从节点正确配置为当前主节点的从节点
  # 先设置masterauth，再设置replicaof
  exec_redis_cmd $SLAVE_CONTAINER "CONFIG SET masterauth $REDIS_PASSWORD"
  exec_redis_cmd $SLAVE_CONTAINER "REPLICAOF $MASTER_IP 6379"
  
  echo -e "${GREEN}主从关系已确认! 主节点: $MASTER_CONTAINER (IP: $MASTER_IP)${NC}"
fi

# 验证新的主从状态
echo -e "${BLUE}[3/3] 验证新的主从状态...${NC}"

sleep 3  # 等待复制建立

# 验证主节点角色
MASTER_ROLE=$(exec_redis_cmd $MASTER_CONTAINER "INFO REPLICATION" | grep role | cut -d: -f2 | tr -d '[:space:]')
echo -e "${YELLOW}$MASTER_CONTAINER 当前角色: ${GREEN}$MASTER_ROLE${NC}"

# 验证从节点角色
SLAVE_ROLE=$(exec_redis_cmd $SLAVE_CONTAINER "INFO REPLICATION" | grep role | cut -d: -f2 | tr -d '[:space:]')
echo -e "${YELLOW}$SLAVE_CONTAINER 当前角色: ${GREEN}$SLAVE_ROLE${NC}"

# 检查主从复制状态
if [[ "$NEW_MASTER" == "slave" ]]; then
  # 新的主节点是原来的从节点，检查原主节点的复制状态
  REPL_STATE=$(exec_redis_cmd $MASTER_CONTAINER "INFO REPLICATION" | grep master_link_status | cut -d: -f2 | tr -d '[:space:]')
  if [[ "$REPL_STATE" == "up" ]]; then
    echo -e "${GREEN}复制状态正常! $MASTER_CONTAINER 成功复制 $SLAVE_CONTAINER 的数据${NC}"
  else
    echo -e "${RED}警告: 复制状态异常! $MASTER_CONTAINER 无法复制 $SLAVE_CONTAINER 的数据${NC}"
  fi
else
  # 主节点不变，检查从节点的复制状态
  REPL_STATE=$(exec_redis_cmd $SLAVE_CONTAINER "INFO REPLICATION" | grep master_link_status | cut -d: -f2 | tr -d '[:space:]')
  if [[ "$REPL_STATE" == "up" ]]; then
    echo -e "${GREEN}复制状态正常! $SLAVE_CONTAINER 成功复制 $MASTER_CONTAINER 的数据${NC}"
  else
    echo -e "${RED}警告: 复制状态异常! $SLAVE_CONTAINER 无法复制 $MASTER_CONTAINER 的数据${NC}"
  fi
fi

echo -e "${BLUE}====================================${NC}"
if [[ "$NEW_MASTER" == "slave" ]]; then
  echo -e "${GREEN}成功将 $SLAVE_CONTAINER 提升为主节点!${NC}"
  echo -e "${GREEN}$MASTER_CONTAINER 现在是从节点${NC}"
else
  echo -e "${GREEN}成功确认 $MASTER_CONTAINER 为主节点!${NC}"
  echo -e "${GREEN}$SLAVE_CONTAINER 继续作为从节点${NC}"
fi
echo -e "${BLUE}====================================${NC}" 