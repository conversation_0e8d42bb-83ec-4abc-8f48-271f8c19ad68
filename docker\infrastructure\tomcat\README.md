# Tomcat 容器镜像构建

本目录包含用于构建Tomcat容器镜像的模板和配置文件。这些镜像基于JDK基础镜像，专为医疗产品设计优化。

## 目录结构

```
tomcat/
├── Dockerfile.template      # Dockerfile模板文件
├── shared/                  # 共享资源目录
│   ├── conf/                # Tomcat配置文件
│   ├── scripts/             # Tomcat启动脚本
│   └── crypto-files/        # 加密组件
└── README.md                # 本文档
```

## 重要说明

**注意：本项目不应在代码仓库中保存生成的Dockerfile文件。所有Dockerfile均通过构建脚本动态生成。**

## 构建流程

Tomcat镜像使用模板化方式构建，具体流程如下：

1. 构建脚本读取`Dockerfile.template`模板文件
2. 根据指定的Tomcat版本和JDK版本，动态替换模板中的变量
3. 在临时目录中生成实际的Dockerfile
4. 使用生成的Dockerfile构建镜像
5. 构建完成后清理临时文件（除非启用调试模式）

## 构建命令

使用`build-tomcat.sh`脚本构建Tomcat镜像：

```bash
# 构建所有版本
bash docker/scripts/build/build-tomcat.sh

# 构建特定版本
bash docker/scripts/build/build-tomcat.sh 9.0 8    # Tomcat 9.0 + JDK 8
bash docker/scripts/build/build-tomcat.sh 9.0 17   # Tomcat 9.0 + JDK 17

# 调试模式（保留临时文件）
DEBUG=true bash docker/scripts/build/build-tomcat.sh 9.0 8

# 保存生成的Dockerfile
OUTPUT_DIR=./output bash docker/scripts/build/build-tomcat.sh 9.0 8
```

## 镜像版本管理

当前支持的版本组合：

| Tomcat版本 | JDK版本 | 镜像标签                    |
|-----------|---------|---------------------------|
| 9.0.100   | 8       | btit/tomcat:9.0-jdk8-temurin-jammy |
| 9.0.100   | 17      | btit/tomcat:9.0-jdk17-temurin-jammy |

## 自定义配置

1. **服务器配置**：修改`shared/conf/server.xml`
2. **JVM设置**：修改`shared/scripts/setenv.sh`
3. **加密组件**：放置在`shared/crypto-files/`目录下

## 最佳实践

1. **不要直接编辑生成的Dockerfile**：所有修改应在模板文件中进行
2. **遵循Docker构建规范**：使用特定版本的基础镜像，避免使用`latest`标签
3. **维护模板变量**：在`build-tomcat.sh`脚本中维护版本号和校验和信息

## 注意事项

1. 模板文件中的Linter警告是预期的，因为它包含替换变量
2. 实际构建时这些变量会被正确替换，生成有效的Dockerfile
3. 校验和信息存储在`registry/images/infrastructure/tomcat/`目录下
