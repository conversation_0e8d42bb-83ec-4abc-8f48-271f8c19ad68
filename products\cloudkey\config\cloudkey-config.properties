#####################数据库连接配置######################
shardingsphere.datasource.business.url=****************************************************************************
shardingsphere.datasource.business.username=cloudkey
shardingsphere.datasource.business.password=Netca@2006
shardingsphere.datasource.archive.url=************************************************************************************
shardingsphere.datasource.archive.username=cloudkey
shardingsphere.datasource.archive.password=Netca@2006
#####################数据库连接配置######################
#####################redis连接配置######################
#redis所在机器的ip
spring.redis.host=localhost
spring.redis.port=6379
#redis访问密码，其他配置可以不修改，直接复制
spring.redis.password=CK_Redis@2006
# 一般来说是不用配置的，Spring Cache 会根据依赖的包自行装配
spring.cache.type=redis
# 连接超时时间（毫秒）
spring.redis.timeout=10000
# Redis默认情况下有16个分片，这里配置具体使用的分片
spring.redis.database=0
# 连接池最大连接数（使用负值表示没有限制） 默认 8
spring.redis.lettuce.pool.max-active=8
# 连接池最大阻塞等待时间（使用负值表示没有限制） 默认 -1
spring.redis.lettuce.pool.max-wait=-1
# 连接池中的最大空闲连接 默认 8
spring.redis.lettuce.pool.max-idle=8
# 连接池中的最小空闲连接 默认 0
spring.redis.lettuce.pool.min-idle=0
#连接间隔 心跳检测
spring.redis.pingConnectionInterval=1000
# redisson 连接池的大小
spring.redisson.connection.pool.size=100
#####################redis连接配置######################

########与业务平台通讯软证书信息--配置#########
cloudkey.system.certkeystorepath=${catalina.home}/conf/bpms.jks
cloudkey.system.certstorepwd=123456
cloudkey.system.certstoretype=JKS
cloudkey.system.cert_entry=test
cloudkey.system.cert_pwd=123456
########与业务平台通讯软证书信息--配置#########

#######使用JKS############
cloudkey.system.enc_keystorepath=${catalina.home}/conf/enc.jks
cloudkey.system.enc_storepwd=123456
cloudkey.system.enc_storetype=JKS
cloudkey.system.enc_cert_entry=test
cloudkey.system.enc_cert_pwd=123456
cloudkey.system.devicetype=1
cloudkey.system.decryptcert=MIICVzCCAcCgAwIBAgIEV6LRLjANBgkqhkiG9w0BAQUFADBvMQswCQYDVQQGEwJDTjESMBAGA1UECBMJR3Vhbmdkb25nMRIwEAYDVQQHEwlHdWFuZ3pob3UxDjAMBgNVBAoTBU5FVENBMQ4wDAYDVQQLEwVORVRDQTEYMBYGA1UEAwwP55yB6LSo55uR5rWL6K+VMCAXDTE2MDgwNDA1MjI1NFoYDzIxMTYwNzExMDUyMjU0WjBvMQswCQYDVQQGEwJDTjESMBAGA1UECBMJR3Vhbmdkb25nMRIwEAYDVQQHEwlHdWFuZ3pob3UxDjAMBgNVBAoTBU5FVENBMQ4wDAYDVQQLEwVORVRDQTEYMBYGA1UEAwwP55yB6LSo55uR5rWL6K+VMIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDMx4UDmOgeV5ZUcSNf6qHiIdaqGqFMMpZ2vk36RM/KIJXUSZWDwu52iPf0ETTu7BQjVFUrGHQOofnJQx5nWDh76gMrPFbPfxemTW2qElxgNes3QQxYluBF/n0tatz8WhvCrQApbDOtAAtJUqKG2Ay9hWM/4E7I+M7jHoagCuhoIwIDAQABMA0GCSqGSIb3DQEBBQUAA4GBAJcWmiSLr/jLLyuKSW9eyjpHeAvo/8+haKDaGr09ebb1jUE5sJOwuKRpbV0boEnwltaPBAEFcPeKytMhtPj1Aq2gjqiersX4HATJRdQ2rmVvDqPXKZIZFyfT7zmaVEFIgmyoVRxuHWSvu2v3f1fGi1f2zwrTMfwOBQQe3GgECZIa
#######使用JKS############

#由该上述加密机签发的证书，用于加密管理员PIN
#业务平台59新接口SM2加密证书
cloudkey.system.bpms_enc_admin_pin_cert=MIIDDzCCArSgAwIBAgILEJpiE+R21X51/aEwCgYIKoEcz1UBg3UwYjELMAkGA1UEBhMCQ04xJDAiBgNVBAoMG05FVENBIENlcnRpZmljYXRlIEF1dGhvcml0eTEtMCsGA1UEAwwkTkVUQ0EgU00yIFRFU1QwMSBhbmQgRXZhbHVhdGlvbiBDQTAxMB4XDTIwMDQzMDA4MDgyOFoXDTI1MDQzMDA4MDgyOFowZjELMAkGA1UEBhMCQ04xEjAQBgNVBAgMCUd1YW5nZG9uZzEPMA0GA1UEBwwG5bm/5beeMRswGQYDVQQKDBJQSU7noIHliqDlr4bor4HkuaYxFTATBgNVBAMMDDE5Mi4xNjguMC41OTBZMBMGByqGSM49AgEGCCqBHM9VAYItA0IABF9bVKx6lYKtZqDxPmDGnsBZsSppoY+Y9nUidcJYLmyaUXtxK1rjVFdPHgSv2iu41nXzTJs/QlH7FhNIhrKT1gKjggFLMIIBRzAfBgNVHSMEGDAWgBQMe+ticwN1+oxKJAz2jzshZX4X6TAdBgNVHQ4EFgQUJGjIsmt4iWKB1Zz0Ft39axhMWFYwawYDVR0gBGQwYjBgBgorBgEEAYGSSA0KMFIwUAYIKwYBBQUHAgEWRGh0dHA6Ly93d3cuY25jYS5uZXQvY3Mva25vd2xlZGdlL3doaXRlcGFwZXIvY3BzL25ldENBdGVzdGNlcnRjcHMucGRmMDMGA1UdHwQsMCowKKAmoCSGImh0dHA6Ly90ZXN0LmNuY2EubmV0L2NybC9TTTJDQS5jcmwwDAYDVR0TAQH/BAIwADAOBgNVHQ8BAf8EBAMCBLAwDwYDVR0RBAgwBocEwKgAOzA0BgorBgEEAYGSSAEOBCYMJGRlNzcyOGRiZTFjNmE0MDAxMTQ3YjdkYjM3ZmEzMDYzQFMwMjAKBggqgRzPVQGDdQNJADBGAiEA2SyfnDPuUjGmNlSC1bMip4QTK+xyXwlPfw9ubZdlURACIQCMqR5yV6TpPK5xzkJKqjXud0DEruUOvKbVNV/rLz2zIA==
########加密机--配置#########


######################人脸识别###########################
face_landmarks_model_path=${catalina.home}/conf/landmarks_model.dat
face_dnn_model_path=${catalina.home}/conf/dnn_model.dat
face_similarity_limit=0.55
######################人脸识别###########################

################短信实现类#########################
#cloudkey.system.smstype=net.netca.common.notice.sender.sms.open189.Open189Sender

################ 正则表达式配置 #########################
cloudkey.regexp.idcard_all=(^\\d{15}$)|(^\\d{17}(\\d|X|x)$)
cloudkey.regexp.idcard_15=^(\\d{6})(\\d{2})(\\d{2})(\\d{2})(\\d{3})$
cloudkey.regexp.idcard_18=^(\\d{6})(\\d{4})(\\d{2})(\\d{2})(\\d{3})([0-9]|X|x)$
cloudkey.regexp.hkIdCard=^[A-Z]{1,2}[0-9]{6}\\(?[0-9A]\\)?$
cloudkey.regexp.social_credit=^[0-9A-Z]+$
cloudkey.regexp.taxid=^\\**[A-Za-z0-9]+$
cloudkey.regexp.telphone=^[0-9]{1,4}-?[0-9]{5,10}$
cloudkey.regexp.mobilephone=^(13|14|15|16|17|18|19)\\d{9}$
cloudkey.regexp.email=^(([^<>()\\[\\]\\.,;:\\s@\"]+(\\.[^<>()\\[\\]\\.,;:\\s@\"]+)*)|(\".+\"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\])|(([a-zA-Z\\-0-9]+\\.)+[a-zA-Z]{2,}))$
################ 正则表达式配置 #########################

cloudkey.system.contextPath=/cloudkeyserver

#################事件证书pin码配置#######################
cloudkey.event.userPin=Netca@2006
cloudkey.event.adminPin=Netca@2006
#################事件证书pin码配置#######################
#################腾讯云刷脸认证#######################
cloudkey.tencent.face.url= https://bpms.cnca.net/ia/v1/api/face/compare
cloudkey.tencent.face.appId=cloudkey_app
cloudkey.tencent.face.appKey=6640193361039919
#################腾讯云刷脸认证#######################
#################电子数据统一存储#######################
#cloudkey.edss.url=https://192.168.201.115:7777/edssweb
#cloudkey.edss.appId=cloudkey_gyfy
#cloudkey.edss.appKey=01D4001F43FE5BED1A0E23544F122995
#################电子数据统一存储#######################
#################定时任务表达式#######################
# 定时检查进行证书密钥更新的定时任务，默认每天0点执行.
cloudkey.cron.scheduledUpdateCert=0 0 0 * * ?
#################定时任务表达式#######################
#################密码卡调用方式 (enableTransfer=false && enableExternalDevice=false 表示本地密码卡，enableTransfer=true && enableExternalDevice=true 表示更换密码卡，enableTransfer=false && enableExternalDevice=true 表示网络密码卡)#######################
# 是否开启更换密码卡，true为开 false为关
cloudkey.card.enableTransfer=false
#是否开启外部服务密码设备，true为开 false为关
cloudkey.card.enableExternalDevice=false
#是否开启网络调用密码卡管理的地址
cloudkey.card.externalDeviceBaseUrl=http://127.0.0.1:7777/cloudkeycardmngr/
#是否开启网络调用密码卡管理的日志，true为开 false为关
cloudkey.card.enableNetcardmngrLog=false
#################密码卡调用方式#######################
#预约续期定时任务表达式
cloudkey.cron.scheduledRenewCert:0 30 3 * * ?