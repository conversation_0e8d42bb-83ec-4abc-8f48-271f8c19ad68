# 云密钥系统Keepalived环境变量配置

# 产品标识
PRODUCT_NAME="cloudkey"
PRODUCT_DISPLAY_NAME="云密钥系统"

# 注意：Keepalived配置文件中的IP地址、网络接口和路由ID等参数是直接设置在配置文件中的，
# 不是通过环境变量设置。请直接修改对应产品目录下的keepalived_master.conf和keepalived_backup.conf文件。

# MySQL配置
MYSQL_HOST=127.0.0.1
MYSQL_PORT=13306
MYSQL_USER=cloudkey
MYSQL_PASSWORD=Netca@2006
MYSQL_DATABASE=cloudkey

# Redis配置
REDIS_HOST=127.0.0.1
REDIS_PORT=16379
REDIS_PASSWORD=CK_Redis@2006
REDIS_DB=0

# Nginx配置
NGINX_HOST=127.0.0.1
NGINX_PORT=80
NGINX_STATUS_PATH=/nginx_status
