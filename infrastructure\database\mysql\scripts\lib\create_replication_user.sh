#!/bin/bash
# MySQL 复制用户创建库
# 用于在MySQL节点上创建用于主主复制的用户
# 版本: 1.0.0

# 检查是否已经加载此库
if [ "${MYSQL_REPLICATION_USER_LIB_LOADED:-}" = "true" ]; then
  return 0
fi

# 防止该库被多次加载
MYSQL_REPLICATION_USER_LIB_LOADED=true

# 确保日志函数可用
if [ "${MYSQL_LOGS_LIB_LOADED:-}" != "true" ]; then
  if [ -f "$(dirname "${BASH_SOURCE[0]}")/logs.sh" ]; then
    source "$(dirname "${BASH_SOURCE[0]}")/logs.sh"
  else
    # 定义简单的日志函数，以防日志库不可用
    log_info() { echo "[INFO] $1"; }
    log_error() { echo "[ERROR] $1"; }
    log_success() { echo "[SUCCESS] $1"; }
    log_warning() { echo "[WARNING] $1"; }
    log_debug() { echo "[DEBUG] $1"; }  # 添加debug级别日志函数
  fi
fi

# 创建复制用户函数
create_replication_user() {
  local product="${1:-cloudkey}"
  local node_role="${2:-master1}"
  local base_dir="${3:-}"
  
  if [ -z "$base_dir" ]; then
    # 如果没有提供base_dir，尝试确定它
    local script_dir="$(dirname "$(dirname "${BASH_SOURCE[0]}")")"
    base_dir="$(dirname "$script_dir")"
  fi
  
  log_info "创建MySQL复制用户..."
  log_info "参数: 产品=$product, 节点=$node_role, 基础目录=$base_dir"
  
  # 获取复制用户密码
  local repl_password_file="${base_dir}/secrets/replication_password.txt"
  if [ ! -f "$repl_password_file" ]; then
    log_error "复制用户密码文件不存在: $repl_password_file"
    return 1
  fi
  
  # 获取root密码
  local root_password_file="${base_dir}/secrets/mysql_root_password.txt"
  if [ ! -f "$root_password_file" ]; then
    log_error "MySQL root密码文件不存在: $root_password_file"
    return 1
  fi
  
  local repl_password=$(cat "$repl_password_file")
  local root_password=$(cat "$root_password_file")
  
  # 创建复制用户
  case "$node_role" in
    "master1")
      local container="${product}-mysql-master1"
      if ! docker ps | grep -q "$container"; then
        log_error "容器 $container 不存在或未运行"
        log_info "当前运行的容器列表:"
        docker ps
        return 1
      fi
      
      log_info "在 $container 上创建复制用户..."
      log_info "执行命令: docker exec -i $container mysql -uroot -p**** [SQL命令]"
      
      # 使用set -x在执行前打印命令，set +x在执行后关闭
      (
        set -x
        # 临时禁用错误返回检查，捕获错误输出
        (docker exec -i "$container" mysql -uroot -p${root_password} <<EOF
CREATE USER IF NOT EXISTS 'replication_user'@'%' IDENTIFIED WITH mysql_native_password BY '$repl_password';
GRANT REPLICATION SLAVE ON *.* TO 'replication_user'@'%';
FLUSH PRIVILEGES;
EOF
        ) 2> /tmp/mysql_error.log
      )
      local exit_code=$?
      set +x
      
      # 检查是否有错误
      if [ $exit_code -ne 0 ]; then
        log_error "MySQL命令执行失败，退出码: $exit_code"
        log_error "错误信息: $(cat /tmp/mysql_error.log 2>/dev/null || echo '无法获取错误日志')"
        
        # 测试连接
        log_info "尝试简单连接测试..."
        docker exec -it "$container" mysql -uroot -p${root_password} -e "SELECT 1" || \
          log_error "基本连接测试失败，请检查MySQL服务状态和网络配置"
        
        # 检查容器内的MySQL状态
        log_info "检查容器内MySQL进程状态..."
        docker exec "$container" ps aux | grep mysql || true
        
        # 检查端口监听
        log_info "检查MySQL端口监听状态..."
        docker exec "$container" netstat -tulpn | grep mysql || true
        
        return 1
      fi
      ;;
      
    "master2")
      local container="${product}-mysql-master2"
      if ! docker ps | grep -q "$container"; then
        log_error "容器 $container 不存在或未运行"
        log_info "当前运行的容器列表:"
        docker ps
        return 1
      fi
      
      log_info "在 $container 上创建复制用户..."
      log_info "执行命令: docker exec -i $container mysql -uroot -p**** [SQL命令]"
      
      # 使用set -x在执行前打印命令，set +x在执行后关闭
      (
        set -x
        # 临时禁用错误返回检查，捕获错误输出
        (docker exec -i "$container" mysql -uroot -p${root_password} <<EOF
CREATE USER IF NOT EXISTS 'replication_user'@'%' IDENTIFIED WITH mysql_native_password BY '$repl_password';
GRANT REPLICATION SLAVE ON *.* TO 'replication_user'@'%';
FLUSH PRIVILEGES;
EOF
        ) 2> /tmp/mysql_error.log
      )
      local exit_code=$?
      set +x
      
      # 检查是否有错误
      if [ $exit_code -ne 0 ]; then
        log_error "MySQL命令执行失败，退出码: $exit_code"
        log_error "错误信息: $(cat /tmp/mysql_error.log 2>/dev/null || echo '无法获取错误日志')"
        
        # 测试连接
        log_info "尝试简单连接测试..."
        docker exec -it "$container" mysql -uroot -p${root_password} -e "SELECT 1" || \
          log_error "基本连接测试失败，请检查MySQL服务状态和网络配置"
        
        # 检查容器内的MySQL状态
        log_info "检查容器内MySQL进程状态..."
        docker exec "$container" ps aux | grep mysql || true
        
        # 检查端口监听
        log_info "检查MySQL端口监听状态..."
        docker exec "$container" netstat -tulpn | grep mysql || true
        
        return 1
      fi
      ;;
      
    "both")
      # 在两个节点上都创建
      local container1="${product}-mysql-master1"
      local container2="${product}-mysql-master2"
      
      if ! docker ps | grep -q "$container1"; then
        log_warning "容器 $container1 不存在或未运行，跳过在此节点创建用户"
        log_info "当前运行的容器列表:"
        docker ps
      else
        log_info "在 $container1 上创建复制用户..."
        log_info "执行命令: docker exec -i $container1 mysql -uroot -p**** [SQL命令]"
        
        # 使用set -x在执行前打印命令，set +x在执行后关闭
        (
          set -x
          # 临时禁用错误返回检查，捕获错误输出
          (docker exec -i "$container1" mysql -uroot -p${root_password} <<EOF
CREATE USER IF NOT EXISTS 'replication_user'@'%' IDENTIFIED WITH mysql_native_password BY '$repl_password';
GRANT REPLICATION SLAVE ON *.* TO 'replication_user'@'%';
FLUSH PRIVILEGES;
EOF
          ) 2> /tmp/mysql_error_1.log
        )
        local exit_code=$?
        set +x
        
        # 检查是否有错误
        if [ $exit_code -ne 0 ]; then
          log_error "MySQL命令在 $container1 上执行失败，退出码: $exit_code"
          log_error "错误信息: $(cat /tmp/mysql_error_1.log 2>/dev/null || echo '无法获取错误日志')"
          
          # 测试连接
          log_info "尝试简单连接测试..."
          docker exec -it "$container1" mysql -uroot -p${root_password} -e "SELECT 1" || \
            log_error "基本连接测试失败，请检查MySQL服务状态和网络配置"
        fi
      fi
      
      if ! docker ps | grep -q "$container2"; then
        log_warning "容器 $container2 不存在或未运行，跳过在此节点创建用户"
        log_info "当前运行的容器列表:"
        docker ps
      else
        log_info "在 $container2 上创建复制用户..."
        log_info "执行命令: docker exec -i $container2 mysql -uroot -p**** [SQL命令]"
        
        # 使用set -x在执行前打印命令，set +x在执行后关闭
        (
          set -x
          # 临时禁用错误返回检查，捕获错误输出
          (docker exec -i "$container2" mysql -uroot -p${root_password} <<EOF
CREATE USER IF NOT EXISTS 'replication_user'@'%' IDENTIFIED WITH mysql_native_password BY '$repl_password';
GRANT REPLICATION SLAVE ON *.* TO 'replication_user'@'%';
FLUSH PRIVILEGES;
EOF
          ) 2> /tmp/mysql_error_2.log
        )
        local exit_code=$?
        set +x
        
        # 检查是否有错误
        if [ $exit_code -ne 0 ]; then
          log_error "MySQL命令在 $container2 上执行失败，退出码: $exit_code"
          log_error "错误信息: $(cat /tmp/mysql_error_2.log 2>/dev/null || echo '无法获取错误日志')"
          
          # 测试连接
          log_info "尝试简单连接测试..."
          docker exec -it "$container2" mysql -uroot -p${root_password} -e "SELECT 1" || \
            log_error "基本连接测试失败，请检查MySQL服务状态和网络配置"
        fi
      fi
      ;;
      
    *)
      log_error "无效的节点角色: $node_role"
      return 1
      ;;
  esac
  
  if [ $? -eq 0 ]; then
    log_success "MySQL复制用户创建成功"
    return 0
  else
    log_error "MySQL复制用户创建失败"
    return 1
  fi
}

# 检查复制用户是否存在
check_replication_user() {
  local container="$1"
  local host="$2"
  local port="${3:-3306}"
  local root_password="$4"
  
  log_info "检查 $container 上的复制用户..."
  log_info "执行命令: docker exec -i $container mysql -h $host -P$port -uroot -p**** [查询命令]"
  
  local check_result
  if [ -z "$host" ] || [ "$host" = "localhost" ]; then
    # 本地容器
    log_info "使用本地连接检查复制用户..."
    (
      set -x
      check_result=$(docker exec -i "$container" mysql -uroot -p${root_password} -e "SELECT COUNT(*) FROM mysql.user WHERE User='replication_user'" 2>/tmp/mysql_check.log)
    )
    local exit_code=$?
    set +x
    
    if [ $exit_code -ne 0 ]; then
      log_error "复制用户检查失败，退出码: $exit_code"
      log_error "错误信息: $(cat /tmp/mysql_check.log 2>/dev/null || echo '无法获取错误日志')"
      return 1
    fi
  else
    # 远程节点
    log_info "使用远程连接检查复制用户 (主机: $host, 端口: $port)..."
    (
      set -x
      check_result=$(docker exec -i "$container" mysql -h"$host" -P"$port" -uroot -p${root_password} -e "SELECT COUNT(*) FROM mysql.user WHERE User='replication_user'" 2>/tmp/mysql_check.log)
    )
    local exit_code=$?
    set +x
    
    if [ $exit_code -ne 0 ]; then
      log_error "远程复制用户检查失败，退出码: $exit_code"
      log_error "错误信息: $(cat /tmp/mysql_check.log 2>/dev/null || echo '无法获取错误日志')"
      return 1
    fi
  fi
  
  log_info "检查结果: $check_result"
  
  if echo "$check_result" | grep -q "1"; then
    log_success "$container 上复制用户已存在"
    return 0
  else
    log_error "$container 上缺少复制用户"
    return 1
  fi
}

# 如果直接执行此脚本而不是作为库导入，则显示用法
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
  # 直接执行该脚本的情况
  
  # 显示使用说明
  show_usage() {
    echo "用法: $0 [产品名] [节点角色] [base_dir]"
    echo ""
    echo "参数:"
    echo "  产品名    - 要配置的产品名称 (默认: cloudkey)"
    echo "             可选值: cloudkey, eseal, quicksign"
    echo "  节点角色  - 当前节点角色 (默认: master1)"
    echo "             可选值: master1, master2, both"
    echo "  base_dir  - MySQL基础目录 (可选)"
    echo ""
    echo "示例:"
    echo "  $0 cloudkey master1"
    echo "  $0 eseal master2 /path/to/mysql"
    echo ""
    echo "注: 此脚本通常作为库函数导入，而不是直接执行"
  }
  
  # 处理帮助参数
  if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    show_usage
    exit 0
  fi
  
  # 获取参数
  PRODUCT="${1:-cloudkey}"
  NODE_ROLE="${2:-master1}"
  BASE_DIR="${3:-}"
  
  # 执行创建复制用户函数
  create_replication_user "$PRODUCT" "$NODE_ROLE" "$BASE_DIR"
  exit $?
fi 