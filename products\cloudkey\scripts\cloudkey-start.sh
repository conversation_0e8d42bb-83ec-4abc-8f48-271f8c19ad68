#!/bin/bash
#
# CloudKey 启动脚本
# 用于准备环境并启动 CloudKey 服务
#

# 引入公共库函数
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${SCRIPT_DIR}/common_lib.sh"

# 显示帮助信息
show_help() {
  cat << EOF
用法: $(basename "$0") [选项]

CloudKey 启动脚本，用于准备环境并启动 CloudKey 服务容器。

选项:
  -h, --help    显示此帮助信息并退出

功能:
  - 检查 CloudKey WAR 包是否存在
  - 创建和设置必要的目录权限
  - 启动 Docker 容器服务
  - 验证服务是否成功启动
  - 自动开放服务所需的端口(8082)
  
示例:
  $(basename "$0")             启动服务
  $(basename "$0") --help      显示帮助信息

相关脚本:
  cloudkey-stop.sh       停止服务
  cloudkey-restart.sh    重启服务
  cloudkey-status.sh     查看服务状态
EOF
}

# 处理命令行参数
for arg in "$@"; do
  case $arg in
    -h|--help)
      show_help
      exit 0
      ;;
    *)
      echo -e "${RED}未知参数: $arg${NC}"
      echo -e "${YELLOW}使用 -h 或 --help 查看帮助信息${NC}"
      exit 1
      ;;
  esac
done

set -e

# 检查Docker环境
check_docker_environment || exit 1

# 获取Docker Compose命令
DOCKER_COMPOSE_CMD=$(get_docker_compose_cmd)

# 项目根目录（上级目录）
PROJECT_DIR="$(cd "${SCRIPT_DIR}/.." && pwd)"
# Tomcat webapps 目录
WEBAPPS_DIR="${PROJECT_DIR}/tomcat/webapps"
# 日志目录
LOG_DIR="${PROJECT_DIR}/logs"
# WAR文件名
WAR_FILE="cloudkeyserver.war"

# 环境准备 - 从deploy脚本合并的功能
echo -e "${BLUE}开始准备 CloudKey 部署环境...${NC}"

# 确保必要的目录存在
mkdir -p "${WEBAPPS_DIR}"
mkdir -p "${LOG_DIR}"
mkdir -p "${LOG_DIR}/tomcat"
mkdir -p "${PROJECT_DIR}/config/Catalina/localhost"

# 设置目录权限，确保容器有写入权限
echo -e "${BLUE}设置目录权限...${NC}"
chmod -R 777 "${WEBAPPS_DIR}" 2>/dev/null || true
chmod -R 777 "${LOG_DIR}" 2>/dev/null || true
chmod -R 777 "${PROJECT_DIR}/config" 2>/dev/null || true

# 检查应用是否已部署
if [ ! -f "${WEBAPPS_DIR}/${WAR_FILE}" ]; then
  echo -e "${RED}错误: 未找到 ${WAR_FILE} 文件，请将其放置在 tomcat/webapps 目录下${NC}"
  exit 1
fi

# 开放服务所需的端口
open_firewall_port 8082

# 启动服务
echo -e "${BLUE}正在启动 CloudKey 服务...${NC}"
cd "${PROJECT_DIR}"

# 检查服务是否已经在运行
CONTAINER_ID=$($DOCKER_COMPOSE_CMD ps -q cloudkey 2>/dev/null)
if [ -n "${CONTAINER_ID}" ] && docker ps -q --no-trunc | grep -q "${CONTAINER_ID}"; then
  echo -e "${YELLOW}CloudKey 服务已经在运行中，无需重复启动。${NC}"
  echo -e "${YELLOW}如需重启服务，请使用: ./scripts/cloudkey-restart.sh${NC}"
  exit 0
fi

# 启动服务
$DOCKER_COMPOSE_CMD up -d

# 检查启动状态
sleep 5
if $DOCKER_COMPOSE_CMD ps | grep -q "cloudkey.*Up"; then
  echo -e "${GREEN}CloudKey 服务启动成功！${NC}"
  echo -e "${GREEN}可以通过以下地址访问: http://localhost:8082/cloudkeyserver/${NC}"
  echo -e "${BLUE}查看日志: $DOCKER_COMPOSE_CMD logs -f cloudkey${NC}"
else
  echo -e "${RED}警告: CloudKey 服务可能未正常启动，请检查日志:${NC}"
  echo -e "${YELLOW}$DOCKER_COMPOSE_CMD logs cloudkey${NC}"
fi 