# MySQL 故障排除指南

## 目录

1. [常见问题](#常见问题)
2. [服务启动问题](#服务启动问题)
3. [主主复制问题](#主主复制问题)
4. [性能问题](#性能问题)
5. [连接问题](#连接问题)
6. [数据问题](#数据问题)
7. [日志分析](#日志分析)
8. [紧急情况](#紧急情况)
9. [多宿主机部署问题](#多宿主机部署问题)

## 常见问题

### MySQL服务无法启动

**症状**：
- Docker容器启动后立即退出
- 服务无法访问

**排查步骤**：
1. 检查容器日志：
   ```bash
   docker logs cloudkey-mysql-master1
   ```

2. 检查磁盘空间：
   ```bash
   df -h
   ```

3. 检查配置文件是否正确：
   ```bash
   cat /path/to/conf/my.cnf
   ```

**解决方案**：
- 如果是配置文件错误，修正配置后重启
- 如果是磁盘空间问题，清理空间或扩容
- 如果是权限问题，修正目录权限：
  ```bash
  chmod -R 777 /path/to/mysql/data
  ```

### 无法连接MySQL服务

**症状**：
- 应用程序报告无法连接数据库
- 连接被拒绝

**排查步骤**：
1. 检查服务状态：
   ```bash
   docker ps | grep mysql
   ```

2. 检查端口是否正确映射：
   ```bash
   docker port cloudkey-mysql-master1
   ```

3. 尝试从容器内部连接：
   ```bash
   docker exec -it cloudkey-mysql-master1 mysql -uroot -p
   ```

4. 检查端口是否匹配产品定义的端口：
   ```bash
   # 产品对应的端口
   # cloudkey: 13306
   # eseal: 13316
   # quicksign: 13326
   ```

**解决方案**：
- 如果服务未运行，启动服务
- 如果端口映射错误，修正Docker Compose配置
- 如果密码错误，重置密码

## 服务启动问题

### InnoDB数据文件损坏

**症状**：
- 错误日志中出现"InnoDB: Corrupted page"
- 服务启动失败

**排查步骤**：
1. 检查错误日志：
   ```bash
   docker exec -it cloudkey-mysql-master1 cat /var/log/mysql/error.log
   ```

**解决方案**：
1. 使用备份恢复：
   ```bash
   cd /path/to/compose
   ./maintenance.sh
   # 选择恢复数据库选项
   ```

2. 如无可用备份，尝试启动修复：
   ```bash
   # 修改配置文件，添加以下参数
   # innodb_force_recovery = 1
   # 逐步增加数值（最高为6），但数值越高风险越大
   ```

### 权限问题导致启动失败

**症状**：
- 错误日志中出现权限相关错误
- 容器无法访问挂载卷

**排查步骤**：
1. 检查目录权限：
   ```bash
   ls -la /path/to/mysql/data
   ```

**解决方案**：
1. 修正目录所有权和权限：
   ```bash
   chown -R 999:999 /path/to/mysql/data
   chown -R 999:999 /path/to/mysql/logs
   chmod -R 755 /path/to/mysql/data
   ```

## 主主复制问题

### 复制中断

**症状**：
- `SHOW SLAVE STATUS\G` 显示复制线程停止
- IO线程或SQL线程状态为"No"

**排查步骤**：
1. 检查复制状态：
   ```bash
   docker exec -it cloudkey-mysql-master1 mysql -uroot -p -e "SHOW SLAVE STATUS\G"
   ```

2. 检查错误信息：
   ```
   Last_IO_Error: ...
   Last_SQL_Error: ...
   ```

3. 检查网络连接（对于跨宿主机部署的环境）：
   ```bash
   # 测试从宿主机A到宿主机B的网络连接
   ping <宿主机B的IP>
   telnet <宿主机B的IP> 13306  # 使用对应产品的端口
   ```

**解决方案**：
1. 对于简单错误，跳过错误事件：
   ```sql
   STOP SLAVE;
   SET GLOBAL SQL_SLAVE_SKIP_COUNTER = 1;
   START SLAVE;
   ```

2. 对于数据不一致，使用维护工具：
   ```bash
   cd /path/to/scripts
   ./maintenance.sh
   # 选择修复复制错误选项
   ```

3. 对于网络问题（跨宿主机部署）：
   - 检查防火墙规则，确保MySQL端口开放
   - 确保两台宿主机都使用相同端口映射（如cloudkey产品使用13306）
   - 检查Docker网络配置

### 复制延迟

**症状**：
- `Seconds_Behind_Master` 值较大
- 数据同步有明显延迟

**排查步骤**：
1. 查看复制状态：
   ```bash
   docker exec -it cloudkey-mysql-master1 mysql -uroot -p -e "SHOW SLAVE STATUS\G" | grep Seconds_Behind_Master
   ```

2. 检查服务器负载：
   ```bash
   top
   ```

3. 检查网络延迟（对于跨宿主机部署）：
   ```bash
   ping -c 10 <另一宿主机IP>  # 检查网络延迟
   ```

**解决方案**：
1. 优化主服务器负载，减少大事务
2. 增加从服务器资源配置
3. 检查并优化网络连接
4. 对于跨地域部署，考虑使用性能更好的网络连接

## 性能问题

### 查询缓慢

**症状**：
- 应用程序响应慢
- 特定查询执行时间长

**排查步骤**：
1. 检查慢查询日志：
   ```bash
   docker exec -it cloudkey-mysql-master1 cat /var/log/mysql/mysql-slow.log
   ```

2. 检查当前运行的查询：
   ```sql
   SHOW PROCESSLIST;
   ```

**解决方案**：
1. 为频繁查询添加索引：
   ```sql
   ALTER TABLE table_name ADD INDEX idx_column_name (column_name);
   ```

2. 优化查询语句
3. 调整配置参数，如缓冲区大小

### 高CPU使用率

**症状**：
- 服务器CPU负载高
- 响应速度变慢

**排查步骤**：
1. 检查系统负载：
   ```bash
   top -c
   ```

2. 检查占用资源的查询：
   ```sql
   SHOW PROCESSLIST;
   ```

**解决方案**：
1. 终止占用资源的长查询：
   ```sql
   KILL query_id;
   ```

2. 优化频繁执行的查询
3. 考虑增加服务器资源

## 连接问题

### 连接数过多

**症状**：
- 错误"Too many connections"
- 新连接被拒绝

**排查步骤**：
1. 检查当前连接数：
   ```sql
   SHOW STATUS LIKE 'Threads_connected';
   ```

2. 检查最大连接数设置：
   ```sql
   SHOW VARIABLES LIKE 'max_connections';
   ```

**解决方案**：
1. 增加最大连接数：
   ```sql
   SET GLOBAL max_connections = 500;
   ```
   
2. 在配置文件中永久修改：
   ```
   [mysqld]
   max_connections = 500
   ```

3. 检查应用程序是否正确关闭连接

### 身份验证失败

**症状**：
- 错误"Access denied for user"
- 应用无法连接数据库

**排查步骤**：
1. 验证用户名和密码是否正确
2. 检查用户权限：
   ```sql
   SELECT User, Host, authentication_string FROM mysql.user WHERE User='username';
   SHOW GRANTS FOR 'username'@'hostname';
   ```

**解决方案**：
1. 重设用户密码：
   ```sql
   ALTER USER 'username'@'hostname' IDENTIFIED BY 'new_password';
   FLUSH PRIVILEGES;
   ```

2. 授予必要权限：
   ```sql
   GRANT SELECT, INSERT, UPDATE, DELETE ON database.* TO 'username'@'hostname';
   FLUSH PRIVILEGES;
   ```

## 数据问题

### 数据不一致

**症状**：
- 主节点间数据不同
- 查询结果在不同节点不一致

**排查步骤**：
1. 比较两个节点的数据：
   ```sql
   SELECT COUNT(*) FROM table_name; -- 在两个节点上运行
   ```

2. 检查复制状态：
   ```bash
   docker exec -it cloudkey-mysql-master1 mysql -uroot -p -e "SHOW SLAVE STATUS\G"
   docker exec -it cloudkey-mysql-master2 mysql -uroot -p -e "SHOW SLAVE STATUS\G"
   ```

**解决方案**：
1. 如果差异较小，修复特定数据
2. 如果差异较大，考虑重建复制：
   ```bash
   # 使用维护工具重设复制关系
   cd /path/to/scripts
   ./maintenance.sh
   # 选择重置复制关系选项
   ```

### 表损坏

**症状**：
- 查询报错："table is marked as crashed"
- 无法访问特定表

**排查步骤**：
1. 检查表状态：
   ```sql
   CHECK TABLE table_name;
   ```

**解决方案**：
1. 尝试修复表：
   ```sql
   REPAIR TABLE table_name;
   ```

2. 如果无法修复，从备份恢复：
   ```bash
   cd /path/to/scripts
   ./maintenance.sh
   # 选择恢复数据库选项
   ```

## 日志分析

### 重要日志位置

**错误日志**：
- 容器内位置：`/var/log/mysql/error.log`
- 主机映射位置：`/path/to/mysql/logs/error.log`

**慢查询日志**：
- 容器内位置：`/var/log/mysql/mysql-slow.log`
- 主机映射位置：`/path/to/mysql/logs/mysql-slow.log`

**查看方法**：
```bash
# 查看错误日志
docker exec -it cloudkey-mysql-master1 tail -100 /var/log/mysql/error.log

# 查看慢查询日志
docker exec -it cloudkey-mysql-master1 tail -100 /var/log/mysql/mysql-slow.log
```

### 常见错误信息解读

| 错误信息 | 可能原因 | 解决方法 |
|---------|---------|---------|
| "Can't open the mysql.plugin table" | 权限问题或数据损坏 | 检查目录权限，考虑重新初始化 |
| "Invalid (old?) table or database name" | 表名包含无效字符或数据库损坏 | 修正表名或恢复数据库 |
| "Duplicate entry" | 唯一键冲突 | 修改数据或应用程序逻辑 |
| "Disk full" | 磁盘空间不足 | 清理空间或扩展存储 |
| "Error connecting to master" | 复制连接问题 | 检查网络/端口/凭据，确保对应产品的端口开放 |

## 紧急情况

### 数据库无法启动且无备份

**应急步骤**：
1. 不要恐慌，保留现场
2. 尝试使用innodb_force_recovery：
   ```
   # 在my.cnf中添加
   [mysqld]
   innodb_force_recovery=1
   ```
3. 逐步增加innodb_force_recovery值（1-6），但不要超过必要值
4. 一旦可以启动，立即备份数据
5. 使用备份重建数据库

### 主主节点全部崩溃

**应急步骤**：
1. 保留现场，不要删除任何数据文件
2. 查看错误日志确定原因
3. 尝试单独启动一个节点：
   ```bash
   cd /path/to/compose
   docker-compose -f cloudkey-mysql.yml up -d cloudkey-mysql-master1
   ```
4. 一旦一个节点恢复，从该节点恢复另一个节点
5. 重建主主复制关系

### 紧急联系方式

如遇到无法解决的严重问题，请联系：

- **DBA团队**：例如 <EMAIL> 或 电话号码
- **服务提供商**：例如 <EMAIL> 或 电话号码

在联系支持人员时，请提供：
- 详细的错误信息和日志
- 问题发生的时间和操作步骤
- 已尝试的解决方案

## 多宿主机部署问题

### 端口分配与映射

**知识点**：
- 每个产品预设有固定端口号：
  - cloudkey: 13306
  - eseal: 13316
  - quicksign: 13326
- 两个主节点在不同宿主机上使用相同端口号
- 应用程序通过连接不同宿主机的相同端口实现高可用

**常见问题**：
1. 端口冲突：宿主机上已有其他服务占用了预设端口
2. 防火墙限制：端口未在防火墙中开放
3. 网络隔离：宿主机之间网络不通

**排查步骤**：
1. 检查端口是否被占用：
   ```bash
   netstat -tuln | grep 13306  # 替换为对应产品的端口
   ```

2. 检查防火墙规则：
   ```bash
   iptables -L -n | grep 13306  # 替换为对应产品的端口
   ```

3. 测试宿主机间连接：
   ```bash
   telnet <另一宿主机IP> 13306  # 替换为对应产品的端口
   ```

**解决方案**：
1. 对于端口冲突，修改端口或停止冲突服务
2. 对于防火墙限制，添加开放规则：
   ```bash
   iptables -A INPUT -p tcp --dport 13306 -j ACCEPT  # 替换为对应产品的端口
   ```
3. 对于网络隔离，检查网络配置，确保宿主机间可互相访问

### 端口冲突问题

**症状**：
- 部署脚本执行时报错：`Bind for 0.0.0.0:13306 failed: port is already allocated`
- 同一台宿主机上两个MySQL主节点尝试使用相同端口

**排查步骤**：
1. 检查当前宿主机上是否已经有一个MySQL主节点在运行：
   ```bash
   docker ps | grep mysql
   ```

2. 查看端口使用情况：
   ```bash
   netstat -tulpn | grep 13306
   ```

**解决方案**：
- 使用节点角色参数在不同宿主机上部署不同的主节点：
  ```bash
  # 在第一台宿主机上
  ./deploy.sh cloudkey master1
  
  # 在第二台宿主机上
  ./deploy.sh cloudkey master2
  ```

- 确保不要在同一台宿主机上同时部署两个主节点（除非是测试环境并修改了端口配置）

### 主主复制节点间通信问题

**症状**：
- 主主复制配置失败
- 主从状态显示IO线程未运行
- 提示无法连接到对方节点

**排查步骤**：
1. 检查两台宿主机之间的网络连通性：
   ```bash
   # 从宿主机A ping宿主机B
   ping <宿主机B_IP>
   
   # 从宿主机A尝试连接宿主机B的MySQL端口
   telnet <宿主机B_IP> 13306
   ```

2. 检查防火墙设置：
   ```bash
   # OpenEuler系统
   firewall-cmd --list-ports
   
   # Ubuntu系统
   ufw status
   ```

3. 检查MySQL主主复制配置：
   ```bash
   # 配置正确的IP地址
   ./setup-master-master.sh cloudkey <master1_IP> <master2_IP>
   ```

**解决方案**：
- 确保两台宿主机防火墙已开放MySQL端口（13306等）
  ```bash
  # OpenEuler系统
  firewall-cmd --zone=public --add-port=13306/tcp --permanent
  firewall-cmd --reload
  
  # Ubuntu系统
  ufw allow 13306/tcp
  ```

- 使用正确的IP地址重新配置主主复制：
  ```bash
  ./setup-master-master.sh cloudkey ************* *************
  ```

- 如果使用Docker网络，确保两台宿主机的Docker网络可以互相访问

### 数据不同步问题

**症状**：
- 主主复制已启动，但在不同节点上查询结果不一致
- 写入一个节点的数据在另一个节点上不可见

**排查步骤**：
1. 检查复制状态：
   ```bash
   # 在master1上
   docker exec -it cloudkey-mysql-master1 mysql -uroot -p -e "SHOW SLAVE STATUS\G"
   
   # 在master2上
   docker exec -it cloudkey-mysql-master2 mysql -uroot -p -e "SHOW SLAVE STATUS\G"
   ```

2. 检查数据延迟（Seconds_Behind_Master值）
3. 检查是否有复制错误（Last_Error字段）

**解决方案**：
- 如有复制错误，根据错误信息解决问题
- 对于数据不一致，可以使用以下方法修复：
  ```bash
  # 在出现问题的从节点上
  docker exec -it cloudkey-mysql-master1 mysql -uroot -p
  
  # 在MySQL命令行中执行
  STOP SLAVE;
  SET GLOBAL SQL_SLAVE_SKIP_COUNTER = 1; # 跳过一个错误
  START SLAVE;
  ```

- 对于严重不一致，可能需要重新同步数据：
  ```bash
  # 使用维护脚本
  ./maintenance.sh cloudkey
  # 然后选择"修复复制错误"选项
  ```

### 节点独立部署后难以配置主主复制

**症状**：
- 在两台不同宿主机上分别部署了主节点，但无法正确配置主主复制

**排查步骤**：
1. 确认两台宿主机上的产品名称一致（如都是cloudkey）
2. 确认两台宿主机上的端口配置一致（如都是13306）
3. 检查脚本执行权限和目录结构

**解决方案**：
- 使用setup-master-master.sh脚本显式指定节点IP地址：
  ```bash
  # 在其中一台宿主机上执行
  ./setup-master-master.sh cloudkey ************* *************
  ```

- 确保两台宿主机的密码文件内容一致：
  ```bash
  cat ../secrets/mysql_root_password.txt
  cat ../secrets/replication_password.txt
  ```

- 手动检查复制用户和权限：
  ```bash
  # 在两个节点上都执行
  docker exec -it cloudkey-mysql-master1 mysql -uroot -p -e "SELECT user, host FROM mysql.user WHERE user='replication_user'"
  docker exec -it cloudkey-mysql-master1 mysql -uroot -p -e "SHOW GRANTS FOR 'replication_user'@'%'"
  ```

## 端口开放问题

如果应用程序无法连接到MySQL数据库，可能是端口未正确开放。根据不同操作系统，排查步骤如下：

### OpenEuler 22.03 (使用firewalld)

1. 检查firewalld状态：
   ```bash
   sudo systemctl status firewalld
   ```

2. 检查端口是否已开放：
   ```bash
   sudo firewall-cmd --list-ports | grep <MYSQL_PORT>/tcp
   ```

3. 开放端口：
   ```bash
   sudo firewall-cmd --zone=public --add-port=<MYSQL_PORT>/tcp --permanent
   sudo firewall-cmd --reload
   ```

4. 验证端口是否开放：
   ```bash
   sudo ss -tulpn | grep <MYSQL_PORT>
   ```

### Ubuntu 22.04 (使用ufw)

1. 检查ufw状态：
   ```bash
   sudo ufw status
   ```

2. 开放端口：
   ```bash
   sudo ufw allow <MYSQL_PORT>/tcp
   ```

3. 验证端口是否开放：
   ```bash
   sudo ss -tulpn | grep <MYSQL_PORT>
   ```

### 通用端口测试

无论使用何种操作系统，都可以使用以下命令测试端口连通性：

1. 从本机测试：
   ```bash
   telnet localhost <MYSQL_PORT>
   ```

2. 从远程主机测试：
   ```bash
   telnet <主机IP> <MYSQL_PORT>
   ```

## 服务启动问题

如果MySQL服务无法正常启动，请检查：

1. 检查Docker容器状态：
   ```bash
   docker ps -a | grep mysql
   ```

2. 查看容器日志：
   ```bash
   docker logs <容器ID或名称>
   ```

3. 检查端口是否被占用：
   ```bash
   sudo lsof -i:<MYSQL_PORT>
   ```

4. 检查数据目录权限：
   ```bash
   ls -la infrastructure/database/mysql/data/<产品名>/
   ```

## 主主复制问题

如果主主复制不正常工作，请检查：

1. 查看复制状态：
   ```bash
   docker exec <容器名称> mysql -uroot -p$(cat infrastructure/database/mysql/secrets/mysql_root_password.txt) -e "SHOW SLAVE STATUS\G"
   ```

2. 常见错误及解决方案：
   - `Last_IO_Error: error connecting to master` - 检查网络连接和复制用户权限
   - `Last_SQL_Error: Could not execute Query event` - 数据不一致，可能需要跳过错误或重建复制

3. 重启复制进程：
   ```bash
   docker exec <容器名称> mysql -uroot -p$(cat infrastructure/database/mysql/secrets/mysql_root_password.txt) -e "STOP SLAVE; START SLAVE;"
   ```

## 多宿主机部署问题

当MySQL主节点部署在不同物理服务器上时：

1. 确保服务器间网络互通：
   ```bash
   ping <对方服务器IP>
   ```

2. 确保MySQL端口在两台服务器上都已开放：
   - OpenEuler: `sudo firewall-cmd --list-ports | grep <MYSQL_PORT>`
   - Ubuntu: `sudo ufw status | grep <MYSQL_PORT>`

3. 确保Docker容器可以通过主机名解析对方IP：
   - 检查Docker网络: `docker network inspect mysql-network`
   - 如必要，添加主机名映射: 在Docker Compose文件的`extra_hosts`部分

## 性能问题

如果遇到性能问题，请检查：

1. 系统资源使用情况：
   ```bash
   docker stats <容器名称>
   ```

2. MySQL状态变量：
   ```bash
   docker exec <容器名称> mysql -uroot -p$(cat infrastructure/database/mysql/secrets/mysql_root_password.txt) -e "SHOW GLOBAL STATUS;"
   ```

3. 慢查询日志：
   ```bash
   docker exec <容器名称> cat /var/log/mysql/slow-query.log
   ```

## 数据一致性问题

如果主节点之间的数据不一致：

1. 使用校验工具比较数据：
   ```bash
   # 安装percona-toolkit
   apt-get install percona-toolkit
   
   # 比较表数据
   pt-table-checksum h=主机1,u=root,p=密码,P=<MYSQL_PORT>
   ```

2. 修复不一致数据：
   ```bash
   pt-table-sync h=主机1,u=root,p=密码,P=<MYSQL_PORT> --sync-to-master --databases=<数据库名>
   ```

## 备份与恢复问题

如果备份或恢复过程失败：

1. 检查备份目录权限：
   ```bash
   ls -la infrastructure/database/mysql/backups/
   ```

2. 检查备份日志：
   ```bash
   cat infrastructure/database/mysql/logs/<产品名>/backup.log
   ```

3. 手动执行备份测试：
   ```bash
   docker exec <容器名称> mysqldump -uroot -p$(cat infrastructure/database/mysql/secrets/mysql_root_password.txt) --all-databases > test_backup.sql
   ```

如果您遇到本文档未涵盖的问题，请联系系统管理员或数据库管理员获取进一步帮助。 