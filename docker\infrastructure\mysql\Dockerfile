# 使用官方MySQL 8.0镜像作为基础镜像
FROM mysql:8.0

# 添加标签信息
LABEL maintainer="Medical Products Team"
LABEL description="MySQL database for medical products"
LABEL version="1.0"

# 设置环境变量
ENV TZ=Asia/Shanghai
ENV MYSQL_ROOT_PASSWORD=changeme

# 复制自定义配置文件
COPY config/my.cnf /etc/mysql/conf.d/

# 复制初始化SQL脚本
COPY scripts/init.sql /docker-entrypoint-initdb.d/

# 设置字符集和排序规则
RUN echo "character-set-server=utf8mb4" >> /etc/mysql/conf.d/docker.cnf \
    && echo "collation-server=utf8mb4_unicode_ci" >> /etc/mysql/conf.d/docker.cnf

# 暴露端口
EXPOSE 3306 33060

# 设置健康检查
HEALTHCHECK --interval=30s --timeout=3s \
  CMD mysqladmin ping -h localhost -u root -p${MYSQL_ROOT_PASSWORD} || exit 1 