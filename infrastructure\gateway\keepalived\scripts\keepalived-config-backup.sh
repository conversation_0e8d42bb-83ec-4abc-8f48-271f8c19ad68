#!/bin/bash
#
# Keepalived 配置备份脚本
# 用于备份 Keepalived 配置文件和日志
# 使用方法: ./keepalived-config-backup.sh [备份目录]
#

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# 默认备份目录
DEFAULT_BACKUP_DIR="/opt/backups/keepalived"
BACKUP_DIR=${DEFAULT_BACKUP_DIR}

# 源配置目录
CONFIG_DIR="/etc/keepalived"
LOG_DIR="/var/log/keepalived"

# 显示帮助信息
show_usage() {
    echo -e "${GREEN}Keepalived 配置备份脚本${NC}"
    echo "使用方法: $0 [备份目录]"
    echo "  默认备份目录: ${DEFAULT_BACKUP_DIR}"
}

# 检查是否为root用户
check_user() {
    if [ "$(id -u)" -ne 0 ]; then
        echo -e "${RED}错误: 必须以root用户运行此脚本${NC}" >&2
        exit 1
    fi
}

# 备份配置和日志
backup_files() {
    # 创建时间戳
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    BACKUP_NAME="keepalived_backup_${TIMESTAMP}"
    BACKUP_PATH="${BACKUP_DIR}/${BACKUP_NAME}"
    
    echo -e "${YELLOW}备份 Keepalived 配置和日志到 ${BACKUP_PATH}...${NC}"
    
    # 创建备份目录
    mkdir -p "${BACKUP_PATH}"
    
    # 备份配置文件
    if [ -d "${CONFIG_DIR}" ]; then
        echo -e "${YELLOW}备份配置文件...${NC}"
        cp -r "${CONFIG_DIR}" "${BACKUP_PATH}/"
    else
        echo -e "${RED}配置目录不存在: ${CONFIG_DIR}${NC}"
    fi
    
    # 备份日志文件
    if [ -d "${LOG_DIR}" ]; then
        echo -e "${YELLOW}备份日志文件...${NC}"
        cp -r "${LOG_DIR}" "${BACKUP_PATH}/"
    else
        echo -e "${YELLOW}日志目录不存在: ${LOG_DIR}${NC}"
    fi
    
    # 压缩备份
    echo -e "${YELLOW}压缩备份文件...${NC}"
    cd "${BACKUP_DIR}"
    tar -czf "${BACKUP_NAME}.tar.gz" "${BACKUP_NAME}"
    rm -rf "${BACKUP_NAME}"
    
    echo -e "${GREEN}备份完成: ${BACKUP_DIR}/${BACKUP_NAME}.tar.gz${NC}"
    
    # 清理旧备份（保留最近7个备份）
    echo -e "${YELLOW}清理旧备份...${NC}"
    ls -t "${BACKUP_DIR}"/*.tar.gz 2>/dev/null | tail -n +8 | xargs -r rm
    
    echo -e "${GREEN}备份过程完成${NC}"
}

# 主函数
main() {
    check_user
    
    # 解析参数
    if [ $# -eq 1 ]; then
        if [ "$1" == "-h" ] || [ "$1" == "--help" ]; then
            show_usage
            exit 0
        else
            BACKUP_DIR="$1"
        fi
    fi
    
    # 确保备份目录存在
    mkdir -p "${BACKUP_DIR}"
    
    # 执行备份
    backup_files
}

# 执行主函数
main "$@" 