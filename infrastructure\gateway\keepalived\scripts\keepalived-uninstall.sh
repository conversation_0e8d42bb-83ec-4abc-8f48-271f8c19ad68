#!/bin/bash
#
# Keepalived 卸载脚本
# 用于完全卸载 Keepalived 服务及其配置
# 使用方法: ./keepalived-uninstall.sh [选项]
#

set -e

# 脚本基础配置
SCRIPT_DIR=$(dirname "$(readlink -f "$0")")
BASE_DIR=$(dirname "${SCRIPT_DIR}")
CONFIG_DIR="/etc/keepalived"
LOG_DIR="/var/log/keepalived"
SYSTEM_SCRIPTS_DIR="${CONFIG_DIR}/scripts"
BACKUP_SCRIPT="${SCRIPT_DIR}/keepalived-config-backup.sh"

# 日志函数
log_info() { echo -e "\033[0;32m[INFO] $1\033[0m"; }
log_warn() { echo -e "\033[0;33m[WARN] $1\033[0m"; }
log_error() { echo -e "\033[0;31m[ERROR] $1\033[0m" >&2; }

# 显示使用帮助
show_usage() {
    cat << EOF
Keepalived 卸载脚本
使用方法: $0 [选项]

选项:
  --backup        卸载前备份配置文件
  --purge         完全清除配置文件和日志
  --force         强制卸载，不提示确认
  -h, --help      显示此帮助信息
EOF
}

# 检查运行环境
check_environment() {
    # 检查root权限
    if [ "$(id -u)" -ne 0 ]; then
        log_error "必须以root用户运行此脚本"
        exit 1
    fi

    # 检测操作系统
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS=$(echo $ID | tr '[:upper:]' '[:lower:]')
        VERSION_ID=$VERSION_ID
        log_info "检测到操作系统: $ID $VERSION_ID"
    else
        log_warn "无法确定操作系统类型，将使用通用方法卸载"
    fi
}

# 备份配置文件
backup_config() {
    log_info "备份 Keepalived 配置文件..."
    
    if [ -f "$BACKUP_SCRIPT" ]; then
        "$BACKUP_SCRIPT"
    else
        local backup_dir="/tmp/keepalived_backup_$(date +%Y%m%d_%H%M%S)"
        mkdir -p "$backup_dir"
        
        if [ -d "$CONFIG_DIR" ]; then
            cp -r "$CONFIG_DIR" "$backup_dir/"
            log_info "配置文件已备份至: $backup_dir"
        else
            log_warn "未找到配置目录: $CONFIG_DIR"
        fi
    fi
}

# 停止并禁用服务
stop_service() {
    log_info "停止并禁用 Keepalived 服务..."
    
    if systemctl is-active --quiet keepalived; then
        systemctl stop keepalived
        log_info "Keepalived 服务已停止"
    else
        log_warn "Keepalived 服务未运行"
    fi
    
    if systemctl is-enabled --quiet keepalived; then
        systemctl disable keepalived
        log_info "Keepalived 服务已禁用"
    fi
}

# 卸载RPM包
uninstall_rpm() {
    log_info "开始卸载 Keepalived RPM 包..."
    
    local keepalived_packages=$(rpm -qa | grep -i keepalived)
    if [ -n "$keepalived_packages" ]; then
        rpm -e $keepalived_packages
        log_info "Keepalived RPM 包已卸载"
    else
        log_warn "未找到已安装的 Keepalived RPM 包"
    fi
}

# 源码安装的卸载
uninstall_source() {
    log_info "尝试卸载源码安装的 Keepalived..."
    
    if [ -d "/usr/local/keepalived" ]; then
        rm -rf /usr/local/keepalived
        log_info "已删除源码安装目录: /usr/local/keepalived"
    fi
    
    # 删除可执行文件
    rm -f /usr/sbin/keepalived
    rm -f /usr/bin/keepalived
    rm -f /usr/local/sbin/keepalived
    rm -f /usr/local/bin/keepalived
    
    log_info "源码安装的 Keepalived 文件已删除"
}

# 清理配置和日志文件
cleanup_files() {
    log_info "清理 Keepalived 配置和日志文件..."
    
    # 删除配置目录
    if [ -d "$CONFIG_DIR" ]; then
        rm -rf "$CONFIG_DIR"
        log_info "已删除配置目录: $CONFIG_DIR"
    fi
    
    # 删除日志目录
    if [ -d "$LOG_DIR" ]; then
        rm -rf "$LOG_DIR"
        log_info "已删除日志目录: $LOG_DIR"
    fi
    
    # 删除系统服务文件
    rm -f /etc/systemd/system/keepalived.service
    rm -f /lib/systemd/system/keepalived.service
    
    # 删除系统配置
    rm -f /etc/sysconfig/keepalived
    
    # 刷新systemd
    systemctl daemon-reload
    
    log_info "Keepalived 配置和日志文件已清理"
}

# 清理系统配置
cleanup_system_config() {
    log_info "清理 Keepalived 相关系统配置..."
    
    # 删除 sysctl 配置
    rm -f /etc/sysctl.d/99-keepalived.conf
    
    # 删除 rsyslog 配置
    rm -f /etc/rsyslog.d/30-keepalived.conf
    
    # 删除 logrotate 配置
    rm -f /etc/logrotate.d/keepalived
    
    # 应用 sysctl 配置
    sysctl --system &>/dev/null || true
    
    # 重启 rsyslog 服务
    if systemctl is-active --quiet rsyslog; then
        systemctl restart rsyslog
    fi
    
    log_info "系统配置已清理"
}

# 确认卸载
confirm_uninstall() {
    if [ "$FORCE_UNINSTALL" = true ]; then
        return 0
    fi
    
    echo "警告: 此操作将卸载 Keepalived 服务及其配置"
    echo "      请确认您要继续执行此操作"
    echo
    echo "要继续卸载 Keepalived 吗? [y/N]: "
    read -r response
    
    case "$response" in
        [yY][eE][sS]|[yY])
            return 0
            ;;
        *)
            log_info "卸载操作已取消"
            exit 0
            ;;
    esac
}

# 主函数
main() {
    # 默认值
    BACKUP_CONFIG=false
    PURGE_CONFIG=false
    FORCE_UNINSTALL=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --backup)
                BACKUP_CONFIG=true
                shift
                ;;
            --purge)
                PURGE_CONFIG=true
                shift
                ;;
            --force)
                FORCE_UNINSTALL=true
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                log_error "无效的参数: '$1'"
                show_usage
                exit 1
                ;;
        esac
    done
    
    log_info "开始卸载 Keepalived"
    
    # 环境检查
    check_environment
    
    # 确认卸载
    confirm_uninstall
    
    # 备份配置
    if [ "$BACKUP_CONFIG" = true ]; then
        backup_config
    fi
    
    # 停止并禁用服务
    stop_service
    
    # 尝试RPM卸载和源码卸载
    if command -v rpm &>/dev/null && rpm -q keepalived &>/dev/null; then
        uninstall_rpm
    else
        uninstall_source
    fi
    
    # 清理文件
    if [ "$PURGE_CONFIG" = true ]; then
        cleanup_files
        cleanup_system_config
    else
        log_info "保留配置文件，如需完全清除请使用 --purge 选项"
    fi
    
    log_info "Keepalived 卸载完成"
}

# 执行主函数
main "$@" 