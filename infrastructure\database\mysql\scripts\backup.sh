#!/bin/bash
# MySQL 备份脚本
# 2023-10-01
# 版本: 1.0.0

# 环境变量配置
ROOT_PASSWORD_FILE=${ROOT_PASSWORD_FILE:-"/run/secrets/mysql_root_password"}
BACKUP_DIR=${BACKUP_DIR:-"/var/backups/mysql"}
PRODUCT=${PRODUCT:-"cloudkey"}
DB_NAME=${DB_NAME:-"$PRODUCT"}
HOSTNAME=$(hostname)
BACKUP_RETENTION_DAYS=${BACKUP_RETENTION_DAYS:-7}

# 获取日期和时间
DATE=$(date +%Y%m%d)
TIME=$(date +%H%M%S)
TIMESTAMP="${DATE}_${TIME}"

# 获取密码
if [ -f "$ROOT_PASSWORD_FILE" ]; then
  ROOT_PASSWORD=$(cat $ROOT_PASSWORD_FILE)
else
  echo "错误: 找不到密码文件 $ROOT_PASSWORD_FILE"
  exit 1
fi

# 确保备份目录存在
PRODUCT_BACKUP_DIR="${BACKUP_DIR}/${PRODUCT}"
if [ ! -d "$PRODUCT_BACKUP_DIR" ]; then
  mkdir -p "$PRODUCT_BACKUP_DIR"
fi

# 备份函数
perform_backup() {
  local backup_file="${PRODUCT_BACKUP_DIR}/${PRODUCT}_${HOSTNAME}_${TIMESTAMP}.sql.gz"
  
  echo "开始备份数据库 ${DB_NAME} 到 ${backup_file}..."
  
  # 执行备份，包含二进制日志位置
  mysqldump -h"localhost" -u"root" -p"$ROOT_PASSWORD" \
    --single-transaction \
    --flush-logs \
    --master-data=2 \
    --routines \
    --triggers \
    --events \
    --databases "$DB_NAME" | gzip > "$backup_file"
  
  # 检查备份是否成功
  if [ $? -eq 0 ] && [ -f "$backup_file" ]; then
    echo "备份成功: $backup_file ($(du -h "$backup_file" | cut -f1))"
    
    # 创建校验和
    md5sum "$backup_file" > "${backup_file}.md5"
    echo "MD5校验和已创建: ${backup_file}.md5"
    
    # 记录二进制日志位置
    binlog_info=$(zcat "$backup_file" | grep -oE "MASTER_LOG_FILE='[^']+', MASTER_LOG_POS=[0-9]+" | head -1)
    echo "二进制日志位置: $binlog_info" > "${backup_file}.binlog"
    echo "二进制日志信息已保存: ${backup_file}.binlog"
    
    return 0
  else
    echo "备份失败"
    [ -f "$backup_file" ] && rm -f "$backup_file"
    return 1
  fi
}

# 清理旧备份文件
cleanup_old_backups() {
  echo "清理${BACKUP_RETENTION_DAYS}天前的旧备份文件..."
  
  find "$PRODUCT_BACKUP_DIR" -name "${PRODUCT}_*_*.sql.gz*" -type f -mtime +${BACKUP_RETENTION_DAYS} -delete
  
  echo "旧备份文件清理完成"
}

# 主函数
main() {
  echo "===== MySQL 备份脚本开始执行 ====="
  echo "产品: $PRODUCT"
  echo "数据库: $DB_NAME"
  echo "节点: $HOSTNAME"
  echo "时间戳: $TIMESTAMP"
  
  # 执行备份
  perform_backup
  backup_result=$?
  
  # 清理旧备份
  cleanup_old_backups
  
  # 显示剩余备份文件
  echo "当前备份文件列表:"
  ls -lh "$PRODUCT_BACKUP_DIR" | grep "${PRODUCT}_.*\.sql\.gz$"
  
  echo "===== MySQL 备份脚本执行完成 ====="
  exit $backup_result
}

# 运行主函数
main "$@" 