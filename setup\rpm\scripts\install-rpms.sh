#!/bin/bash

# 设置错误时退出
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
    echo "用法: $0 [-f|--force] [-h|--help] [-o|--os <os_name>]"
    echo "选项:"
    echo "  -f, --force    强制重新安装所有包"
    echo "  -h, --help     显示此帮助信息"
    echo "  -o, --os       指定目标操作系统(默认: 当前系统)"
    echo
    echo "此脚本用于离线安装预下载的RPM包及其依赖。"
    echo "脚本会自动检测系统架构并安装对应的包。"
    echo "使用 -f 选项可以强制重新安装所有包。"
    echo "使用 -o 选项可以指定目标操作系统的包。"
}

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取当前系统架构
get_current_arch() {
    local arch=$(uname -m)
    case ${arch} in
        x86_64)
            echo "x86_64"
            ;;
        aarch64)
            echo "aarch64"
            ;;
        *)
            log_error "不支持的架构: ${arch}"
            exit 1
            ;;
    esac
}

# 获取当前操作系统
get_current_os() {
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        case ${ID} in
            ubuntu)
                echo "ubuntu"
                ;;
            openeuler)
                echo "openeuler"
                ;;
            *)
                log_error "不支持的操作系统: ${ID}"
                exit 1
                ;;
        esac
    else
        log_error "无法检测操作系统类型"
        exit 1
    fi
}

# 检查是否为root用户
check_root() {
    if [ "$(id -u)" != "0" ]; then
        log_error "此脚本需要root权限运行"
        exit 1
    fi
}

# 安装软件包
install_packages() {
    local pkg_dir="$1"
    local force_install="$2"
    local os_type="$3"
    local total_pkgs=0
    
    if [ "$os_type" = "ubuntu" ]; then
        # Ubuntu系统使用dpkg安装deb包
        total_pkgs=$(ls "${pkg_dir}"/*.deb 2>/dev/null | wc -l)
        if [ $total_pkgs -eq 0 ]; then
            log_error "在目录 $pkg_dir 中未找到deb包"
            return 1
        fi
        
        log_info "开始安装deb包，共计 ${total_pkgs} 个包..."
        
        # 准备dpkg安装命令的选项
        local dpkg_opts="-i"
        if [ "${force_install}" = "true" ]; then
            dpkg_opts="${dpkg_opts} --force-all"
            log_info "使用强制安装模式"
        else
            log_info "使用常规安装模式"
        fi
        
        # 先尝试安装所有包
        if ! dpkg ${dpkg_opts} "${pkg_dir}"/*.deb; then
            log_warn "首次安装可能因依赖关系失败，尝试修复依赖..."
            # 尝试修复依赖关系
            apt-get -f install -y
            # 再次尝试安装
            if ! dpkg ${dpkg_opts} "${pkg_dir}"/*.deb; then
                log_error "安装过程中出现错误"
                return 1
            fi
        fi
    else
        # OpenEuler系统使用rpm安装rpm包
        total_pkgs=$(ls "${pkg_dir}"/*.rpm 2>/dev/null | wc -l)
        if [ $total_pkgs -eq 0 ]; then
            log_error "在目录 $pkg_dir 中未找到RPM包"
            return 1
        fi
        
        log_info "开始安装RPM包，共计 ${total_pkgs} 个包..."
        
        # 检查rpm命令是否存在
        if ! command -v rpm >/dev/null 2>&1; then
            log_error "未找到rpm命令，请先安装rpm工具"
            return 1
        fi
        
        # 准备RPM安装命令的选项
        local rpm_opts="-Uvh"
        if [ "${force_install}" = "true" ]; then
            rpm_opts="${rpm_opts} --force"
            log_info "使用强制安装模式"
        else
            # 使用替换模式，如果包已安装则跳过
            rpm_opts="${rpm_opts} --replacefiles --replacepkgs"
            log_info "使用常规安装模式"
        fi
        
        # 第一次尝试安装
        if ! rpm ${rpm_opts} "${pkg_dir}"/*.rpm; then
            log_warn "首次安装可能因依赖关系失败，尝试第二次安装..."
            # 第二次尝试，处理可能的循环依赖
            if ! rpm ${rpm_opts} "${pkg_dir}"/*.rpm; then
                log_error "安装过程中出现错误"
                return 1
            fi
        fi
    fi
    
    log_info "所有包安装完成"
}

# 检查软件包目录是否存在
check_package_dir() {
    local dir="$1"
    local os_type="$2"
    
    if [ ! -d "$dir" ]; then
        log_error "软件包目录不存在: $dir"
        exit 1
    fi
    
    # 检查是否有对应类型的包
    if [ "$os_type" = "ubuntu" ]; then
        if ! ls "${dir}"/*.deb >/dev/null 2>&1; then
            log_error "在目录 $dir 中未找到deb包"
            exit 1
        fi
    else
        if ! ls "${dir}"/*.rpm >/dev/null 2>&1; then
            log_error "在目录 $dir 中未找到RPM包"
            exit 1
        fi
    fi
}

# 主函数
main() {
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    local base_dir="$(dirname "${script_dir}")"
    local force_install="false"
    local target_os=""
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -f|--force)
                force_install="true"
                shift
                ;;
            -o|--os)
                target_os="$2"
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查root权限
    check_root
    
    # 获取当前系统架构
    local current_arch=$(get_current_arch)
    log_info "当前系统架构: ${current_arch}"
    
    # 如果没有指定目标操作系统，则使用当前系统
    if [ -z "${target_os}" ]; then
        target_os=$(get_current_os)
    fi
    log_info "目标操作系统: ${target_os}"
    
    # 构建软件包路径
    local pkg_dir="${base_dir}/packages/${target_os}/${current_arch}"
    
    # 检查软件包目录
    check_package_dir "${pkg_dir}" "${target_os}"
    
    # 安装软件包
    install_packages "${pkg_dir}" "${force_install}" "${target_os}"
    
    log_info "离线安装完成！"
}

# 执行主函数
main "$@" 