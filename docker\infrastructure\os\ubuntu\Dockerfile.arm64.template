FROM docker.zysproxy.online/ubuntu:{{VERSION}}

# 接收构建参数
ARG CRYPTO_VERSION
ARG ARCH
ARG INSTALL_CARD_MNGR=false
ARG INSTALL_SC62=false
ARG INSTALL_SC34=false

# 设置环境变量
ENV LANG=C.UTF-8 \
    LC_ALL=C.UTF-8 \
    TERM=xterm-256color \
    DEBIAN_FRONTEND=noninteractive \
    TZ=Asia/Shanghai \
    LD_LIBRARY_PATH=/usr/lib64:/usr/lib/aarch64-linux-gnu

# 添加元数据
LABEL maintainer="维护团队 <<EMAIL>>" \
      description="基础Ubuntu {{VERSION}} ARM64镜像，含NETCA_CRYPTO支持" \
      version="{{VERSION}}" \
      codename="{{CODENAME}}" \
      crypto_version="${CRYPTO_VERSION}" \
      architecture="${ARCH}" \
      card_manager="${INSTALL_CARD_MNGR}" \
      sansec_sc62="${INSTALL_SC62}" \
      sansec_sc34="${INSTALL_SC34}"

# 配置apt源并安装必要软件包（ARM64优化版本）
RUN set -ex \
    && cp /etc/apt/sources.list /etc/apt/sources.list.bak \
    # ARM64架构使用Ubuntu Ports源
    && echo "deb http://ports.ubuntu.com/ubuntu-ports {{CODENAME}} main restricted universe multiverse" > /etc/apt/sources.list \
    && echo "deb http://ports.ubuntu.com/ubuntu-ports {{CODENAME}}-updates main restricted universe multiverse" >> /etc/apt/sources.list \
    && echo "deb http://ports.ubuntu.com/ubuntu-ports {{CODENAME}}-backports main restricted universe multiverse" >> /etc/apt/sources.list \
    && echo "deb http://ports.ubuntu.com/ubuntu-ports {{CODENAME}}-security main restricted universe multiverse" >> /etc/apt/sources.list \
    # 添加清华大学镜像源作为备用（对ARM64支持较好）
    && echo "" >> /etc/apt/sources.list \
    && echo "# 清华大学镜像源（备用）" >> /etc/apt/sources.list \
    && echo "deb https://mirrors.tuna.tsinghua.edu.cn/ubuntu-ports/ {{CODENAME}} main restricted universe multiverse" >> /etc/apt/sources.list \
    && echo "deb https://mirrors.tuna.tsinghua.edu.cn/ubuntu-ports/ {{CODENAME}}-updates main restricted universe multiverse" >> /etc/apt/sources.list \
    && echo "deb https://mirrors.tuna.tsinghua.edu.cn/ubuntu-ports/ {{CODENAME}}-backports main restricted universe multiverse" >> /etc/apt/sources.list \
    && echo "deb https://mirrors.tuna.tsinghua.edu.cn/ubuntu-ports/ {{CODENAME}}-security main restricted universe multiverse" >> /etc/apt/sources.list \
    # 更新包列表
    && apt-get update \
    && DEBIAN_FRONTEND=noninteractive apt-get install -y \
       ca-certificates \
       gnupg \
       lsb-release \
       curl \
       wget \
    # 添加Ubuntu官方GPG密钥
    && mkdir -p /etc/apt/keyrings \
    && curl -fsSL https://keyserver.ubuntu.com/pks/lookup?op=get\&search=0x871920D1991BC93C | gpg --dearmor -o /etc/apt/keyrings/ubuntu-archive-keyring.gpg \
    && chmod 644 /etc/apt/keyrings/ubuntu-archive-keyring.gpg \
    # 再次更新包列表
    && apt-get update \
    && DEBIAN_FRONTEND=noninteractive apt-get install -y \
       apt-utils \
       {{PACKAGES}} \
       dialog \
       libterm-readline-perl-perl \
       sqlite3 \
       libsqlite3-dev \
       libgmp10 \
       libgmp3-dev \
       libldap-2.5-0 \
       libssl3 \
       libssl-dev \
       openssl \
       vim-tiny \
       libzip4 \
       file \
       libxml2 \
       unzip \
       build-essential \
       pciutils \
       dnsutils \
       curl \
       ca-certificates \
       apt-transport-https \
       kmod \
       tzdata \
    # 配置时区
    && ln -snf /usr/share/zoneinfo/$TZ /etc/localtime \
    && echo $TZ > /etc/timezone \
    && dpkg-reconfigure -f noninteractive tzdata \
    # ARM64特定的库文件链接
    && ln -sf /usr/lib/aarch64-linux-gnu/libgmp.so.10 /usr/lib/aarch64-linux-gnu/libgmp.so.3 \
    && if [ ! -e /usr/lib/aarch64-linux-gnu/libzip.so.1 ]; then \
         ln -sf /usr/lib/aarch64-linux-gnu/libzip.so.4 /usr/lib/aarch64-linux-gnu/libzip.so.1; \
       fi \
    # 清理缓存
    && apt-get clean \
    && apt-get autoremove -y \
    && rm -rf /var/lib/apt/lists/* \
    && rm -rf /tmp/* /var/tmp/*

# 配置LD_LIBRARY_PATH
RUN echo "export LD_LIBRARY_PATH=${LD_LIBRARY_PATH}" >> /etc/profile

# Copy NETCA_CRYPTO files
COPY shared/crypto-files/${CRYPTO_VERSION}/${ARCH}/NETCA_CRYPTO_linux32_64 /tmp/NETCA_CRYPTO_linux32_64/

# Install NETCA_CRYPTO and related components
RUN set -ex \
    && mkdir -p /usr/lib64 /var/log \
    && cd /tmp/NETCA_CRYPTO_linux32_64 \
    && chmod +x setup.sh \
    # 安装NETCA CRYPTO
    && echo "开始安装NETCA CRYPTO组件..." \
    && bash -x ./setup.sh /usr/lib64 CRYPTO move \
    # 根据条件安装密码卡管理JNI库
    && if [ "$INSTALL_CARD_MNGR" = "true" ]; then \
         echo "开始安装NetcaCardMngr组件..." \
         && bash -x ./setup.sh /usr/lib64 NetcaCardMngr move; \
       fi \
    # 根据条件安装SansecCard_SC62组件
    && if [ "$INSTALL_SC62" = "true" ]; then \
         echo "开始安装SansecCard_SC62组件..." \
         && bash -x ./setup.sh /usr/lib64 SansecCard_SC62 Library ; \
       fi \
    # 根据条件安装SansecCard_SC34组件
    && if [ "$INSTALL_SC34" = "true" ]; then \
         echo "开始安装SansecCard_SC34组件..." \
         && bash -x ./setup.sh /usr/lib64 SansecCard Library ; \
       fi \
    # 创建必要的库文件链接（ARM64特定）
    && mkdir -p /usr/lib/aarch64-linux-gnu \
    && if [ -f /usr/lib64/libNetcaCrypto.so ]; then \
         ln -sf /usr/lib64/libNetcaCrypto.so /usr/lib/aarch64-linux-gnu/libNetcaCrypto.so; \
       fi \
    # 清理临时文件
    && rm -rf /tmp/NETCA_CRYPTO_linux32_64

# 设置工作目录
WORKDIR /

# 默认命令
CMD ["/bin/bash"]
