version: '3.8'

services:
  # 主节点2
  cloudkey-mysql-master2:
    image: **************:1443/btit/infra/mysql:8.0.41-debian
    container_name: cloudkey-mysql-master2
    hostname: cloudkey-mysql-master2
    restart: always
    environment:
      - MYSQL_ROOT_PASSWORD=NetcaMySQL@2023#Strong
      - TZ=Asia/Shanghai
    volumes:
      - ../data/cloudkey/master2:/var/lib/mysql
      - ../config/master2/my.cnf:/etc/mysql/conf.d/master.cnf:ro
      - ../init/cloudkey:/docker-entrypoint-initdb.d
      - ../scripts:/scripts
      - ../logs/cloudkey/master2:/var/log/mysql
      - ../backups/cloudkey:/backups
    ports:
      - "13306:3306"
    deploy:  
      resources:  
        limits:  
          memory: 3G  
          cpus: '2'
    networks:
      mysql-network:
        aliases:
          - cloudkey-mysql-master2
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-uroot", "--password=NetcaMySQL@2023#Strong"]
      interval: 10s
      timeout: 5s
      retries: 5

networks:
  mysql-network:
    driver: bridge 