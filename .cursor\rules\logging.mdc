---
description: 此规则用于管理项目中的日志配置和存储。   包括日志格式、轮转策略和存储位置。
globs: **/logs/**/*,**/logback.xml,operations/log_management/**/*
alwaysApply: false
---
---
description:   此规则用于管理项目中的日志配置和存储。   包括日志格式、轮转策略和存储位置。
globs: **/logs/**/*,**/logback.xml,operations/log_management/**/*
---
# 日志管理规则
description: |
  此规则用于管理项目中的日志配置和存储。
  包括日志格式、轮转策略和存储位置。

patterns:
  - "**/logs/**/*"
  - "**/logback.xml"
  - "operations/log_management/**/*"

references:
  - "@docs/PROJECT_RULES.md"

rules:
  - 日志文件命名：
    - 必须包含服务名称
    - 必须包含日期信息（YYYY-MM-DD）
    - 必须按日志级别分类（info/error/debug）
  - 日志内容格式：
    - 时间戳必须精确到毫秒
    - 必须包含日志级别
    - 必须包含线程信息
    - 必须包含类名和行号
  - 日志轮转策略：
    - 按天切割
    - 保留期限不超过30天
    - 单个日志文件大小不超过1GB
  - 审计日志要求：
    - 必须记录操作人
    - 必须记录操作时间
    - 必须记录操作内容
    - 必须记录操作结果
  - 监控指标：
    - 必须包含性能指标
    - 必须包含业务指标
    - 必须包含系统指标 