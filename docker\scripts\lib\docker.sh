#!/bin/bash
# ===============================================================
# Docker 功能库
# 提供 Docker 相关的功能函数
# ===============================================================

# 确保只加载一次
[[ -n "${_DOCKER_SH_LOADED:-}" ]] && return 0
readonly _DOCKER_SH_LOADED=1

# 获取脚本所在目录
readonly _DOCKER_SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 导入依赖库
source "${_DOCKER_SCRIPT_DIR}/logger.sh"
source "${_DOCKER_SCRIPT_DIR}/utils.sh"

# 导入通用函数库
source "${_DOCKER_SCRIPT_DIR}/common.sh"

# 检查 Docker 守护进程是否运行
check_docker_daemon() {
    log_info "检查 Docker 守护进程..."
    log_debug "执行 docker info 命令"
    
    # 设置超时时间（秒）
    local timeout=10
    local temp_file
    temp_file=$(mktemp)
    
    # 使用timeout命令执行docker info
    if timeout "$timeout" docker info >"$temp_file" 2>&1; then
        log_debug "Docker 守护进程正常运行"
        rm -f "$temp_file"
        return 0
    else
        local exit_code=$?
        local error_msg
        error_msg=$(cat "$temp_file")
        rm -f "$temp_file"
        
        case $exit_code in
            124|137)  # timeout的退出码
                log_error "检查 Docker 守护进程超时（${timeout}秒）"
                ;;
            *)
                log_error "Docker 守护进程未运行或无响应"
                log_debug "错误信息: $error_msg"
                ;;
        esac
        return 1
    fi
}

# 检查 Docker Compose 是否可用
check_docker_compose() {
    log_info "检查 Docker Compose..."
    log_debug "执行 docker-compose --version 命令"
    
    if ! docker-compose --version >/dev/null 2>&1; then
        log_error "Docker Compose 未安装或不可用"
        return 1
    fi
    
    log_debug "Docker Compose 可用"
    return 0
}

# 检查 Docker 用户组
check_docker_group() {
    log_info "检查 Docker 用户组..."
    log_debug "查找 docker 用户组"
    
    if ! getent group docker >/dev/null 2>&1; then
        log_warn "Docker 用户组不存在，正在创建..."
        if ! groupadd docker; then
            log_error "创建 Docker 用户组失败"
            return 1
        fi
        log_info "✓ Docker 用户组创建成功"
    else
        log_debug "Docker 用户组已存在"
    fi
    
    return 0
}

# 检查 Docker 网络
check_docker_network() {
    local network="$1"
    log_info "检查 Docker 网络: $network"
    log_debug "执行 docker network inspect 命令"
    
    if ! docker network inspect "$network" >/dev/null 2>&1; then
        log_warn "Docker 网络 $network 不存在"
        return 1
    fi
    
    log_debug "Docker 网络 $network 存在"
    return 0
}

# 创建 Docker 网络（如果不存在）
ensure_docker_network() {
    local network="$1"
    local driver="${2:-bridge}"
    local subnet="${3:-}"
    
    if ! check_docker_network "$network"; then
        log_info "创建 Docker 网络: $network"
        log_debug "使用驱动: $driver"
        
        local create_cmd=(docker network create --driver "$driver")
        [[ -n "$subnet" ]] && create_cmd+=(--subnet "$subnet")
        create_cmd+=("$network")
        
        if ! "${create_cmd[@]}"; then
            log_error "创建 Docker 网络失败: $network"
            return 1
        fi
        log_info "✓ Docker 网络创建成功"
    fi
    
    return 0
}

# 检查 Docker Registry
check_registry() {
    local registry="$1"
    log_info "检查 Docker Registry: $registry"
    log_debug "尝试连接 Registry: $registry"
    
    # 设置超时时间（秒）
    local timeout=5
    if ! timeout "$timeout" curl -s -f "http://$registry/v2/" >/dev/null 2>&1; then
        log_error "无法连接到 Registry: $registry"
        return 1
    fi
    
    log_debug "Registry 连接正常"
    return 0
}

# 检查镜像是否存在
check_image_exists() {
    local image="$1"
    log_info "检查镜像是否存在: $image"
    log_debug "执行 docker image inspect 命令"
    
    if ! docker image inspect "$image" >/dev/null 2>&1; then
        log_warn "镜像不存在: $image"
        return 1
    fi
    
    log_debug "镜像存在: $image"
    return 0
}

# 拉取镜像（如果不存在）
ensure_image() {
    local image="$1"
    if ! check_image_exists "$image"; then
        log_info "拉取镜像: $image"
        log_debug "执行 docker pull 命令"
        
        if ! docker pull "$image"; then
            log_error "拉取镜像失败: $image"
            return 1
        fi
        log_info "✓ 镜像拉取成功"
    fi
    
    return 0
}

# 检查容器是否运行
check_container_running() {
    local container="$1"
    log_info "检查容器是否运行: $container"
    log_debug "执行 docker container inspect 命令"
    
    if ! docker container inspect -f '{{.State.Running}}' "$container" >/dev/null 2>&1; then
        log_warn "容器未运行: $container"
        return 1
    fi
    
    log_debug "容器正在运行: $container"
    return 0
}

# 安全地停止容器
stop_container() {
    local container="$1"
    local timeout="${2:-10}"
    
    if check_container_running "$container"; then
        log_info "停止容器: $container"
        log_debug "使用超时时间: ${timeout}秒"
        
        if ! docker stop -t "$timeout" "$container"; then
            log_error "停止容器失败: $container"
            return 1
        fi
        log_info "✓ 容器已停止"
    fi
    
    return 0
}

# 检查 Docker 存储驱动
check_storage_driver() {
    local expected_driver="$1"
    local current_driver
    
    log_info "检查 Docker 存储驱动..."
    log_debug "期望的驱动: $expected_driver"
    
    current_driver=$(docker info --format '{{.Driver}}')
    if [ "$current_driver" != "$expected_driver" ]; then
        log_warn "当前存储驱动 ($current_driver) 与期望的驱动 ($expected_driver) 不匹配"
        return 1
    fi
    
    log_debug "存储驱动正确: $current_driver"
    return 0
}

# 检查 Docker API 版本
check_api_version() {
    local min_version="$1"
    local current_version
    
    log_info "检查 Docker API 版本..."
    log_debug "最小要求版本: $min_version"
    
    current_version=$(docker version --format '{{.Server.APIVersion}}')
    if [ "$(printf '%s\n' "$min_version" "$current_version" | sort -V | head -n1)" != "$min_version" ]; then
        log_error "Docker API 版本 ($current_version) 低于最小要求 ($min_version)"
        return 1
    fi
    
    log_debug "API 版本满足要求: $current_version"
    return 0
}

# 登录 Harbor 仓库
login_harbor() {
    local registry="$1"
    local username="$2"
    local password="$3"
    
    if [[ -z "$registry" ]]; then
        log_error "未指定 Harbor 仓库地址"
        return 1
    fi
    
    if [[ -z "$username" ]] || [[ -z "$password" ]]; then
        log_error "未提供 Harbor 登录凭证"
        return 1
    fi
    
    log_info "正在登录 Harbor 仓库: $registry"
    log_debug "使用用户名: $username"
    
    # 检查 Harbor 仓库是否可访问
    if ! check_registry "$registry"; then
        return 1
    fi
    
    if ! docker login "$registry" -u "$username" -p "$password" &>/dev/null; then
        log_error "Harbor 登录失败"
        return 1
    fi
    
    log_info "✓ Harbor 登录成功"
    return 0
}

# 构建 Docker 镜像
build_docker_image() {
    local tag="$1"
    local dockerfile="$2"
    local context="${3:-.}"
    local build_args=("${@:4}")
    
    if [[ -z "$tag" ]] || [[ -z "$dockerfile" ]]; then
        log_error "未提供足够的参数"
        return 1
    fi
    
    log_info "开始构建 Docker 镜像: $tag"
    log_debug "使用 Dockerfile: $dockerfile"
    log_debug "构建上下文: $context"
    
    local cmd=(docker build -t "$tag" -f "$dockerfile")
    [[ ${#build_args[@]} -gt 0 ]] && cmd+=("${build_args[@]}")
    cmd+=("$context")
    
    if ! "${cmd[@]}"; then
        log_error "镜像构建失败: $tag"
        return 1
    fi
    
    log_info "✓ 镜像构建成功: $tag"
    return 0
}

# 推送 Docker 镜像
push_docker_image() {
    local image="$1"
    
    if [[ -z "$image" ]]; then
        log_error "未指定要推送的镜像"
        return 1
    fi
    
    log_info "正在推送镜像: $image"
    log_debug "执行 docker push 命令"
    
    if ! docker push "$image"; then
        log_error "镜像推送失败: $image"
        return 1
    fi
    
    log_info "✓ 镜像推送成功: $image"
    return 0
}

# 保存 Docker 镜像到文件
save_docker_image() {
    local image="$1"
    local output_file="$2"
    
    if [[ -z "$image" ]] || [[ -z "$output_file" ]]; then
        log_error "未提供足够的参数"
        return 1
    fi
    
    # 确保输出目录存在
    local output_dir
    output_dir=$(dirname "$output_file")
    if ! safe_mkdir "$output_dir"; then
        return 1
    fi
    
    log_info "正在保存镜像到文件: $output_file"
    log_debug "镜像: $image"
    
    if ! docker save "$image" -o "$output_file"; then
        log_error "保存镜像失败: $image"
        return 1
    fi
    
    # 计算并保存校验和
    local checksum_file="${output_file}.sha256"
    local checksum
    if checksum=$(calculate_sha512 "$output_file"); then
        echo "$checksum" > "$checksum_file"
        log_info "镜像校验和已保存: $checksum_file"
    else
        log_warn "无法计算镜像文件校验和"
    fi
    
    log_info "✓ 镜像已保存: $output_file"
    return 0
}

# 拉取 Docker 镜像
pull_docker_image() {
    local image="$1"
    local quiet="${2:-false}"
    
    if [[ -z "$image" ]]; then
        log_error "未指定要拉取的镜像"
        return 1
    fi
    
    log_info "正在拉取镜像: $image"
    log_debug "安静模式: $quiet"
    
    local pull_opts=()
    [[ "$quiet" == "true" ]] && pull_opts+=("--quiet")
    
    if ! docker pull "${pull_opts[@]}" "$image"; then
        log_error "拉取镜像失败: $image"
        return 1
    fi
    
    log_info "✓ 镜像拉取成功: $image"
    return 0
}

# 导出公共函数
export -f check_docker_daemon
export -f check_docker_compose
export -f check_docker_group
export -f check_docker_network
export -f ensure_docker_network
export -f check_registry
export -f check_image_exists
export -f ensure_image
export -f check_container_running
export -f stop_container
export -f check_storage_driver
export -f check_api_version
export -f login_harbor
export -f build_docker_image
export -f push_docker_image
export -f save_docker_image
export -f pull_docker_image
