#!/bin/bash
#
# CloudKey 状态查询脚本
# 用于查询 CloudKey 服务状态
#

# 引入公共库函数
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${SCRIPT_DIR}/common_lib.sh"

# 显示帮助信息
show_help() {
  cat << EOF
用法: $(basename "$0") [选项]

CloudKey 状态查询脚本，用于检查和展示 CloudKey 服务的运行状态。

选项:
  -h, --help    显示此帮助信息并退出

功能:
  - 检查 CloudKey 服务是否正在运行
  - 显示容器的详细状态信息
  - 提供服务访问地址和日志查看指令
  
示例:
  $(basename "$0")             查询服务状态
  $(basename "$0") --help      显示帮助信息

相关脚本:
  cloudkey-start.sh     启动服务
  cloudkey-stop.sh      停止服务
  cloudkey-restart.sh   重启服务
EOF
}

# 处理命令行参数
for arg in "$@"; do
  case $arg in
    -h|--help)
      show_help
      exit 0
      ;;
    *)
      echo -e "${RED}未知参数: $arg${NC}"
      echo -e "${YELLOW}使用 -h 或 --help 查看帮助信息${NC}"
      exit 1
      ;;
  esac
done

set -e

# 检查Docker环境
check_docker_environment || exit 1

# 获取Docker Compose命令
DOCKER_COMPOSE_CMD=$(get_docker_compose_cmd)

# 项目根目录（上级目录）
PROJECT_DIR="$(cd "${SCRIPT_DIR}/.." && pwd)"

# 检查服务状态
echo -e "${BLUE}正在检查 CloudKey 服务状态...${NC}"
cd "${PROJECT_DIR}"

# 检查容器ID
CONTAINER_ID=$($DOCKER_COMPOSE_CMD ps -q cloudkey 2>/dev/null)

# 检查服务是否存在
if [ -z "${CONTAINER_ID}" ]; then
  echo -e "${YELLOW}CloudKey 服务尚未创建。${NC}"
  echo -e "${YELLOW}请先运行脚本启动服务: ./scripts/cloudkey-start.sh${NC}"
  exit 1
fi

# 检查服务是否正在运行
if docker ps -q --no-trunc | grep -q "${CONTAINER_ID}"; then
  echo -e "${GREEN}CloudKey 服务状态: 运行中${NC}"
  
  # 获取更多详细信息
  echo -e "\n${BLUE}容器详细信息:${NC}"
  docker ps --filter "id=${CONTAINER_ID}" --format "表格:{{.ID}}\t{{.Image}}\t{{.Status}}\t{{.Ports}}"
  
  echo -e "\n${GREEN}服务访问地址: http://localhost:8082/cloudkey/${NC}"
  echo -e "${BLUE}查看日志命令: $DOCKER_COMPOSE_CMD logs -f cloudkey${NC}"
  
  # 检查应用健康状态
  echo -e "\n${BLUE}正在检查应用健康状态...${NC}"
  if curl -s -I http://localhost:8082/cloudkey/ | grep -q "200 OK"; then
    echo -e "${GREEN}应用健康状态: 正常${NC}"
  else
    echo -e "${YELLOW}应用健康状态: 异常 (应用可能正在启动或遇到错误)${NC}"
    echo -e "${YELLOW}请检查日志获取更多信息: $DOCKER_COMPOSE_CMD logs cloudkey${NC}"
  fi
  
  exit 0
else
  echo -e "${YELLOW}CloudKey 服务状态: 已停止${NC}"
  echo -e "${YELLOW}请使用以下命令启动服务: ./scripts/cloudkey-start.sh${NC}"
  exit 1
fi 