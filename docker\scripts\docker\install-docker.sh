#!/bin/bash

# 设置错误时退出
set -e

# 加载公共函数库
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BASE_DIR="$(cd "${SCRIPT_DIR}/../../.." && pwd)"
LIB_DIR="${BASE_DIR}/docker/scripts/lib"
VERSION_FILE="${SCRIPT_DIR}/../build/versions/docker_versions.json"

# 导入公共函数
source "${LIB_DIR}/common.sh"
source "${LIB_DIR}/docker.sh"

# 检测文件格式并修复（如果需要）
if [ -f "$0" ] && grep -q $'\r' "$0"; then
    log_info "检测到 Windows 换行符，正在修复..."
    TMP_FILE=$(mktemp)
    tr -d '\r' < "$0" > "$TMP_FILE"
    cat "$TMP_FILE" > "$0"
    rm -f "$TMP_FILE"
    log_info "文件格式已修复，重新执行脚本..."
    exec bash "$0" "$@"
fi

# 默认Docker版本和支持的版本
DEFAULT_DOCKER_VERSION=$(jq -r '.default_version // "20.10.24"' "$VERSION_FILE")
SUPPORTED_VERSIONS=($(jq -r '.versions | keys | join(" ")' "$VERSION_FILE"))

# 解析命令行参数
parse_args() {
    # 默认值
    DOCKER_VERSION="${DEFAULT_DOCKER_VERSION}"
    UPGRADE_MODE=false
    FROM_VERSION=""
    SKIP_BACKUP=false
    
    while [[ $# -gt 0 ]]; do
        case "$1" in
            -h|--help)
                show_help
                exit 0
                ;;
            -v|--version)
                DOCKER_VERSION="$2"
                shift 2
                ;;
            -u|--upgrade)
                UPGRADE_MODE=true
                shift
                ;;
            --from)
                FROM_VERSION="$2"
                shift 2
                ;;
            --skip-backup)
                SKIP_BACKUP=true
                shift
                ;;
            *)
                echo "未知选项: $1" >&2
                show_help
                exit 1
                ;;
        esac
    done
    
    # 验证版本是否支持
    local version_supported=false
    for ver in "${SUPPORTED_VERSIONS[@]}"; do
        if [[ "$DOCKER_VERSION" == "$ver" ]]; then
            version_supported=true
            break
        fi
    done
    
    if [[ "$version_supported" != "true" ]]; then
        log_error "不支持的Docker版本: $DOCKER_VERSION"
        log_error "支持的版本: ${SUPPORTED_VERSIONS[*]}"
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    cat << EOF
用法: $(basename "$0") [选项]

选项:
  -h, --help               显示帮助信息
  -v, --version VERSION    指定Docker版本 (默认: ${DEFAULT_DOCKER_VERSION})
                          支持的版本: ${SUPPORTED_VERSIONS[*]}
  -u, --upgrade            执行升级而非全新安装
  --from VERSION           升级时指定源版本 (默认: 自动检测)
  --skip-backup            跳过备份步骤

示例:
  # 默认安装Docker 20.10.24
  $(basename "$0")
  
  # 指定版本安装
  $(basename "$0") -v 20.10.9
  
  # 升级Docker (自动检测当前版本)
  $(basename "$0") -u
  
  # 从指定版本升级
  $(basename "$0") -u --from 20.10.9
EOF
}

# 检查依赖工具
check_dependencies() {
    log_info "检查系统依赖..."
    
    local required_cmds="tar chmod mkdir cp rm"
    local missing_cmds=""
    
    for cmd in $required_cmds; do
        if ! command -v $cmd >/dev/null 2>&1; then
            missing_cmds="$missing_cmds $cmd"
        fi
    done
    
    if [ ! -z "$missing_cmds" ]; then
        log_error "系统缺少必要的命令:$missing_cmds"
        log_error "请先安装这些基础工具"
        log_info "在 RHEL/CentOS 系统上可以运行: yum install -y tar gzip coreutils"
        log_info "在 Ubuntu/Debian 系统上可以运行: apt-get install -y tar gzip coreutils"
        exit 1
    fi
    
    log_info "系统依赖检查完成"
}

# 检查工作目录
check_workdir() {
    if [ ! -d "${BASE_DIR}/setup" ] || [ ! -d "${BASE_DIR}/setup/docker" ]; then
        log_error "请在项目根目录下运行此脚本"
        log_error "期望的目录结构:"
        log_error "  ./setup/docker/"
        log_error "  └── bin/"
        log_error "      ├── x86_64/     # Intel架构二进制"
        log_error "      └── aarch64/    # ARM架构二进制"
        exit 1
    fi
    
    log_info "工作目录: ${BASE_DIR}"
    
    # 显示目录内容以帮助诊断
    log_info "当前目录结构:"
    ls -l "${BASE_DIR}/setup/docker/bin/" 2>/dev/null || true
}

# 检测系统架构
detect_arch() {
    local arch=$(uname -m)
    case "$arch" in
        x86_64)
            echo "x86_64"
            ;;
        aarch64)
            echo "aarch64"
            ;;
        *)
            log_error "不支持的系统架构: $arch"
            exit 1
            ;;
    esac
}

# 检测现有Docker版本
detect_current_docker_version() {
    if command -v docker >/dev/null 2>&1; then
        local version=$(docker --version | grep -oP "Docker version \K[0-9]+\.[0-9]+\.[0-9]+" 2>/dev/null || echo "")
        if [ -n "$version" ]; then
            echo "$version"
        else
            echo ""
        fi
    else
        echo ""
    fi
}

# 备份Docker安装
backup_docker() {
    if [[ "$SKIP_BACKUP" == "true" ]]; then
        log_info "跳过Docker备份（根据用户要求）"
        return 0
    fi
    
    log_info "备份现有Docker安装..."
    local backup_dir="/var/backup/docker/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    # 备份二进制文件
    if command -v docker >/dev/null 2>&1; then
        log_info "备份docker二进制文件"
        cp $(which docker) "$backup_dir/" 2>/dev/null || log_warn "无法备份docker二进制文件"
    fi
    
    # 备份containerd
    if command -v containerd >/dev/null 2>&1; then
        log_info "备份containerd二进制文件"
        cp $(which containerd) "$backup_dir/" 2>/dev/null || log_warn "无法备份containerd二进制文件"
        # 备份containerd相关文件
        for bin in containerd-shim containerd-shim-runc-v2 ctr; do
            if command -v $bin >/dev/null 2>&1; then
                cp $(which $bin) "$backup_dir/" 2>/dev/null || log_warn "无法备份$bin二进制文件"
            fi
        done
    fi
    
    # 备份runc
    if command -v runc >/dev/null 2>&1; then
        log_info "备份runc二进制文件"
        cp $(which runc) "$backup_dir/" 2>/dev/null || log_warn "无法备份runc二进制文件"
    fi
    
    # 备份docker-compose
    if command -v docker-compose >/dev/null 2>&1; then
        log_info "备份docker-compose二进制文件"
        cp $(which docker-compose) "$backup_dir/" 2>/dev/null || log_warn "无法备份docker-compose二进制文件"
    fi
    
    # 备份配置文件
    if [ -d "/etc/docker" ]; then
        log_info "备份Docker配置目录"
        cp -r /etc/docker "$backup_dir/" 2>/dev/null || log_warn "无法备份Docker配置目录"
    fi
    
    # 备份服务文件
    if [ -f "/usr/lib/systemd/system/docker.service" ]; then
        log_info "备份Docker服务文件"
        cp /usr/lib/systemd/system/docker.service "$backup_dir/" 2>/dev/null || log_warn "无法备份Docker服务文件"
    fi
    
    if [ -f "/usr/lib/systemd/system/docker.socket" ]; then
        log_info "备份Docker套接字文件"
        cp /usr/lib/systemd/system/docker.socket "$backup_dir/" 2>/dev/null || log_warn "无法备份Docker套接字文件"
    fi
    
    if [ -f "/etc/systemd/system/containerd.service" ]; then
        log_info "备份Containerd服务文件"
        cp /etc/systemd/system/containerd.service "$backup_dir/" 2>/dev/null || log_warn "无法备份Containerd服务文件"
    fi
    
    # 记录版本信息
    local current_version=$(detect_current_docker_version)
    if [ -n "$current_version" ]; then
        echo "$current_version" > "$backup_dir/VERSION"
    fi
    
    # 创建恢复脚本
    cat > "$backup_dir/restore.sh" << 'EOF'
#!/bin/bash
# Docker恢复脚本
set -e

BACKUP_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
echo "正在从备份目录恢复Docker: $BACKUP_DIR"

# 停止现有服务
systemctl stop docker containerd 2>/dev/null || true

# 恢复二进制文件
for bin in docker containerd containerd-shim containerd-shim-runc-v2 ctr runc docker-compose; do
    if [ -f "$BACKUP_DIR/$bin" ]; then
        echo "恢复 $bin 二进制文件"
        cp "$BACKUP_DIR/$bin" "/usr/bin/$bin"
        chmod +x "/usr/bin/$bin"
    fi
done

# 恢复配置目录
if [ -d "$BACKUP_DIR/docker" ]; then
    echo "恢复 Docker 配置目录"
    cp -r "$BACKUP_DIR/docker" /etc/
fi

# 恢复服务文件
for service in docker.service docker.socket containerd.service; do
    if [ -f "$BACKUP_DIR/$service" ]; then
        echo "恢复 $service 服务文件"
        cp "$BACKUP_DIR/$service" "/usr/lib/systemd/system/$service"
    fi
done

# 重新加载systemd
systemctl daemon-reload

# 启动服务
systemctl start containerd docker

echo "Docker恢复完成，版本信息:"
docker --version

exit 0
EOF
    
    chmod +x "$backup_dir/restore.sh"
    
    log_info "Docker备份已保存至: $backup_dir"
    log_info "如需恢复，请运行: $backup_dir/restore.sh"
    
    return 0
}

# 解压Docker二进制文件
extract_docker() {
    local arch=$1
    local version=$2
    local setup_dir="${BASE_DIR}/setup"
    local temp_dir="/tmp/docker-extract"
    local docker_tgz="${setup_dir}/docker/bin/${arch}/docker-${version}.tgz"
    
    log_info "解压Docker ${version}二进制文件..."
    
    if [ ! -r "${docker_tgz}" ]; then
        log_error "Docker压缩包不存在或无法读取: ${docker_tgz}"
        log_error "当前工作目录: ${BASE_DIR}"
        log_error "目录内容:"
        ls -l "${setup_dir}/docker/bin/${arch}/" 2>/dev/null || log_error "目录不存在"
        exit 1
    fi
    
    rm -rf "${temp_dir}"
    mkdir -p "${temp_dir}"
    
    log_info "正在解压: ${docker_tgz} 到 ${temp_dir}"
    if ! tar xf "${docker_tgz}" -C "${temp_dir}"; then
        log_error "解压Docker压缩包失败"
        rm -rf "${temp_dir}"
        exit 1
    fi
    
    if [ ! -d "${temp_dir}/docker" ]; then
        log_error "解压后未找到 docker 目录"
        rm -rf "${temp_dir}"
        exit 1
    fi
    
    log_info "复制Docker二进制文件到 /usr/bin/"
    for file in "${temp_dir}/docker/"*; do
        base_name=$(basename "$file")
        case "$base_name" in
            containerd*|runc*)
                continue
                ;;
            *)
                cp "$file" "/usr/bin/$base_name"
                chmod +x "/usr/bin/$base_name"
                ;;
        esac
    done
    
    rm -rf "${temp_dir}"
    log_info "Docker二进制文件解压完成"
}

# 检查必要文件
check_files() {
    local arch=$1
    local version=$2
    local setup_dir="${BASE_DIR}/setup"
    
    log_info "检查必要文件..."
    
    if [ ! -d "$setup_dir/docker/bin/$arch" ]; then
        log_error "架构 $arch 的二进制文件目录不存在: $setup_dir/docker/bin/$arch"
        log_error "当前目录结构:"
        ls -l "$setup_dir/docker/bin/" 2>/dev/null || log_error "bin 目录不存在"
        exit 1
    fi
    
    # 从版本配置文件获取对应版本的文件信息
    local containerd_file=$(jq -r --arg ver "$version" --arg arch "$arch" '.versions[$ver].containerd[$arch] // empty' "$VERSION_FILE")
    local runc_file=$(jq -r --arg ver "$version" --arg arch "$arch" '.versions[$ver].runc[$arch] // empty' "$VERSION_FILE")
    local compose_file=$(jq -r --arg ver "$version" --arg arch "$arch" '.versions[$ver].compose[$arch] // empty' "$VERSION_FILE")
    
    # 如果配置文件中没有指定，使用默认值
    if [ -z "$containerd_file" ]; then
        if [ "$version" = "20.10.9" ]; then
            containerd_file="containerd-1.4.12-linux-amd64.tar.gz"
            [ "$arch" = "aarch64" ] && containerd_file="containerd-1.4.12-linux-arm64.tar.gz"
        else  # 默认使用20.10.24对应的containerd版本
            containerd_file="containerd-1.6.22-linux-amd64.tar.gz"
            [ "$arch" = "aarch64" ] && containerd_file="containerd-1.6.22-linux-arm64.tar.gz"
        fi
    fi
    
    if [ -z "$runc_file" ]; then
        runc_file="runc.amd64"
        [ "$arch" = "aarch64" ] && runc_file="runc.arm64"
    fi
    
    if [ -z "$compose_file" ]; then
        compose_file="docker-compose-Linux-x86_64"
        [ "$arch" = "aarch64" ] && compose_file="docker-compose-Linux-aarch64"
    fi
    
    local required_files=(
        "docker-${version}.tgz"
        "$containerd_file"
        "$runc_file"
        "$compose_file"
    )
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$setup_dir/docker/bin/$arch/$file" ]; then
            log_error "缺少文件: $setup_dir/docker/bin/$arch/$file"
            exit 1
        fi
    done
    
    # 检查配置文件
    local config_dir="$setup_dir/docker/config"
    if [ "$version" = "20.10.9" ] && [ -d "$config_dir/20.10.9" ]; then
        # 使用版本特定的配置
        config_dir="$config_dir/20.10.9"
    fi
    
    if [ ! -f "$config_dir/daemon.json" ]; then
        log_error "缺少Docker配置文件: $config_dir/daemon.json"
        exit 1
    fi
    
    local systemd_files=(
        "systemd/docker.service"
        "systemd/docker.socket"
    )
    
    for file in "${systemd_files[@]}"; do
        if [ ! -f "$setup_dir/docker/$file" ]; then
            log_error "缺少系统服务文件: $setup_dir/docker/$file"
            exit 1
        fi
    done
    
    log_info "所需文件检查完成"
}

# 安装 containerd
install_containerd() {
    local arch=$1
    local version=$2
    local setup_dir="${BASE_DIR}/setup"
    
    # 确定对应版本的文件
    local containerd_tar
    if [ "$version" = "20.10.9" ]; then
        containerd_tar="${setup_dir}/docker/bin/${arch}/containerd-1.4.12-linux-amd64.tar.gz"
        [ "$arch" = "aarch64" ] && containerd_tar="${setup_dir}/docker/bin/${arch}/containerd-1.4.12-linux-arm64.tar.gz"
    else  # 默认使用20.10.24对应的containerd版本
        containerd_tar="${setup_dir}/docker/bin/${arch}/containerd-1.6.22-linux-amd64.tar.gz"
        [ "$arch" = "aarch64" ] && containerd_tar="${setup_dir}/docker/bin/${arch}/containerd-1.6.22-linux-arm64.tar.gz"
    fi
    
    local temp_dir="/tmp/containerd-extract"
    
    log_info "安装 containerd (与Docker ${version}兼容的版本)..."
    
    rm -rf "${temp_dir}"
    mkdir -p "${temp_dir}"
    
    log_info "解压 containerd..."
    if [ -f "${containerd_tar}" ]; then
        if ! tar -xzf "${containerd_tar}" -C "${temp_dir}"; then
            log_error "解压 containerd 失败"
            rm -rf "${temp_dir}"
            exit 1
        fi
        
        # 根据版本处理不同的二进制文件结构
        if [ "$version" = "20.10.9" ]; then
            cp "${temp_dir}/bin/containerd" /usr/bin/
            cp "${temp_dir}/bin/containerd-shim" /usr/bin/ 2>/dev/null || true
            cp "${temp_dir}/bin/containerd-shim-runc-v2" /usr/bin/ 2>/dev/null || true
            cp "${temp_dir}/bin/ctr" /usr/bin/
        else
            # 1.6.x版本的containerd可能有不同的目录结构
            if [ -d "${temp_dir}/bin" ]; then
                cp "${temp_dir}/bin/containerd" /usr/bin/ 2>/dev/null || true
                cp "${temp_dir}/bin/containerd-shim" /usr/bin/ 2>/dev/null || true
                cp "${temp_dir}/bin/containerd-shim-runc-v2" /usr/bin/ 2>/dev/null || true
                cp "${temp_dir}/bin/ctr" /usr/bin/ 2>/dev/null || true
            else
                # 直接从根目录复制
                cp "${temp_dir}/containerd" /usr/bin/ 2>/dev/null || true
                cp "${temp_dir}/containerd-shim" /usr/bin/ 2>/dev/null || true
                cp "${temp_dir}/containerd-shim-runc-v2" /usr/bin/ 2>/dev/null || true
                cp "${temp_dir}/ctr" /usr/bin/ 2>/dev/null || true
            fi
        fi
    else
        log_warn "未找到 containerd 压缩包，尝试使用已解压的二进制文件..."
        cp "${setup_dir}/docker/bin/${arch}/containerd" /usr/bin/ 2>/dev/null || true
        cp "${setup_dir}/docker/bin/${arch}/containerd-shim" /usr/bin/ 2>/dev/null || true
        cp "${setup_dir}/docker/bin/${arch}/containerd-shim-runc-v2" /usr/bin/ 2>/dev/null || true
        cp "${setup_dir}/docker/bin/${arch}/ctr" /usr/bin/ 2>/dev/null || true
    fi
    
    chmod +x /usr/bin/containerd 2>/dev/null || true
    chmod +x /usr/bin/containerd-shim 2>/dev/null || true
    chmod +x /usr/bin/containerd-shim-runc-v2 2>/dev/null || true
    chmod +x /usr/bin/ctr 2>/dev/null || true
    
    rm -rf "${temp_dir}"
    
    log_info "验证 containerd 安装..."
    if ! containerd --version; then
        log_error "containerd 安装验证失败"
        exit 1
    fi
    
    log_info "配置 containerd 服务..."
    cat > /etc/systemd/system/containerd.service << 'EOF'
[Unit]
Description=containerd container runtime
Documentation=https://containerd.io
After=network.target

[Service]
ExecStartPre=/sbin/modprobe overlay
ExecStart=/usr/bin/containerd
Restart=always
RestartSec=5
Delegate=yes
KillMode=process

[Install]
WantedBy=multi-user.target
EOF
    
    log_info "启动 containerd 服务..."
    systemctl daemon-reload
    systemctl enable --now containerd
    
    log_info "containerd 安装完成"
}

# 安装 runc
install_runc() {
    local arch=$1
    local version=$2  # 未使用但保留参数一致性
    local setup_dir="${BASE_DIR}/setup"
    local runc_bin="${setup_dir}/docker/bin/${arch}/runc.amd64"
    [ "$arch" = "aarch64" ] && runc_bin="${setup_dir}/docker/bin/${arch}/runc.arm64"
    
    log_info "安装 runc..."
    
    cp "${runc_bin}" /usr/bin/runc
    chmod +x /usr/bin/runc
    
    log_info "验证 runc 安装..."
    if ! runc --version; then
        log_error "runc 安装验证失败"
        exit 1
    fi
    
    log_info "runc 安装完成"
}

# 验证 JSON 配置文件
validate_json_file() {
    local json_file="$1"
    
    if [ ! -f "$json_file" ]; then
        log_error "JSON 配置文件不存在: $json_file"
        return 1
    fi
    
    # 检查 JSON 语法
    if ! python3 -c "import json; json.load(open('$json_file'))" 2>/dev/null; then
        if ! python -c "import json; json.load(open('$json_file'))" 2>/dev/null; then
            if ! jq '.' "$json_file" >/dev/null 2>&1; then
                log_error "JSON 配置文件格式无效: $json_file"
                return 1
            fi
        fi
    fi
    
    return 0
}

# 迁移配置
migrate_config() {
    local from_version="$1"
    local to_version="$2"
    local setup_dir="${BASE_DIR}/setup"
    
    log_info "迁移配置从 $from_version 到 $to_version..."
    
    # 检查是否有版本特定的配置
    local config_src="${setup_dir}/docker/config"
    if [ -d "${config_src}/${to_version}" ]; then
        log_info "使用 $to_version 特定的配置文件"
        config_src="${config_src}/${to_version}"
    fi
    
    # 备份现有daemon.json（如果存在）
    if [ -f "/etc/docker/daemon.json" ]; then
        log_info "备份现有 daemon.json 配置..."
        cp "/etc/docker/daemon.json" "/etc/docker/daemon.json.bak.$(date +%Y%m%d_%H%M%S)"
    fi
    
    # 复制新的daemon.json
    if [ -f "${config_src}/daemon.json" ]; then
        log_info "复制新的 daemon.json 配置..."
        mkdir -p /etc/docker
        cp "${config_src}/daemon.json" /etc/docker/
        chmod 644 /etc/docker/daemon.json
    fi
    
    return 0
}

# 安装Docker
install_docker() {
    local arch=$1
    local version=$2
    local setup_dir="${BASE_DIR}/setup"

    log_info "开始安装 Docker ${version}..."

    # 创建必要的目录
    mkdir -p /usr/bin /usr/local/bin /etc/docker /usr/lib/systemd/system

    log_info "安装 Docker Compose..."
    local compose_file="docker-compose-Linux-x86_64"
    [ "$arch" = "aarch64" ] && compose_file="docker-compose-Linux-aarch64"
    
    cp "$setup_dir/docker/bin/$arch/$compose_file" /usr/local/bin/docker-compose
    chmod +x /usr/local/bin/docker-compose
    
    log_info "验证 Docker Compose 安装..."
    if ! /usr/local/bin/docker-compose --version; then
        log_error "Docker Compose 安装验证失败"
        exit 1
    fi

    log_info "配置 Docker 守护进程..."
    mkdir -p /etc/docker
    
    # 确定配置文件位置
    local config_dir="$setup_dir/docker/config"
    if [ "$version" = "20.10.9" ] && [ -d "$config_dir/20.10.9" ]; then
        config_dir="$config_dir/20.10.9"
    fi
    
    # 验证 daemon.json 配置文件
    local daemon_json="$config_dir/daemon.json"
    log_info "验证 Docker 守护进程配置文件..."
    if ! validate_json_file "$daemon_json"; then
        log_error "Docker 守护进程配置文件无效，请检查格式"
        exit 1
    fi
    
    # 备份现有配置（如果存在）
    if [ -f "/etc/docker/daemon.json" ]; then
        log_info "备份现有 Docker 配置文件..."
        cp "/etc/docker/daemon.json" "/etc/docker/daemon.json.bak.$(date +%Y%m%d_%H%M%S)"
    fi
    
    # 复制并设置权限
    cp "$daemon_json" /etc/docker/
    chmod 644 /etc/docker/daemon.json

    log_info "配置 systemd 服务..."
    cp "$setup_dir/docker/systemd/docker.service" /usr/lib/systemd/system/
    cp "$setup_dir/docker/systemd/docker.socket" /usr/lib/systemd/system/
    chmod 644 /usr/lib/systemd/system/docker.service
    chmod 644 /usr/lib/systemd/system/docker.socket

    log_info "创建 docker 用户组..."
    groupadd docker 2>/dev/null || true

    log_info "重新加载 systemd 配置..."
    if ! systemctl daemon-reload; then
        log_error "systemd 配置重新加载失败"
        exit 1
    fi

    log_info "启动 Docker 服务..."
    if ! systemctl start docker.service; then
        log_error "Docker 服务启动失败"
        log_error "请检查系统日志: journalctl -xeu docker.service"
        # 输出最后 50 行日志以帮助诊断
        journalctl -xeu docker.service | tail -n 50
        exit 1
    fi

    log_info "启用 Docker 服务自启动..."
    systemctl enable docker.service
    systemctl enable docker.socket

    log_info "Docker ${version} 安装完成"
}

# 验证安装
verify_installation() {
    log_info "验证安装..."
    
    # 等待 Docker 守护进程完全启动
    log_info "等待 Docker 守护进程启动..."
    for i in {1..30}; do
        if docker info >/dev/null 2>&1; then
            break
        fi
        log_info "等待 Docker 守护进程响应... $i/30"
        sleep 1
    done

    if ! docker --version; then
        log_error "Docker 安装验证失败"
        exit 1
    fi

    if ! docker-compose --version; then
        log_error "Docker Compose 安装验证失败"
        log_info "请确保 /usr/local/bin 在系统 PATH 中"
        exit 1
    fi

    if ! systemctl is-active docker >/dev/null 2>&1; then
        log_error "Docker 服务未正常运行"
        systemctl status docker.service
        exit 1
    fi

    if ! docker info >/dev/null 2>&1; then
        log_error "Docker 守护进程未正常响应"
        journalctl -xeu docker.service | tail -n 50
        exit 1
    fi

    # 验证 Docker 网络
    if ! docker network ls >/dev/null 2>&1; then
        log_error "Docker 网络功能异常"
        exit 1
    fi

    log_info "安装验证完成"
}

# 升级Docker
upgrade_docker() {
    local from_version="$1"
    local to_version="$2"
    
    log_info "==========================================="
    log_info "开始升级Docker从 $from_version 到 $to_version"
    log_info "==========================================="
    
    # 1. 停止Docker服务
    log_info "停止Docker服务..."
    systemctl stop docker containerd 2>/dev/null || true
    
    # 2. 备份现有安装
    backup_docker
    
    # 3. 安装新版本
    local arch=$(detect_arch)
    extract_docker "$arch" "$to_version"
    
    # 4. 更新containerd和runc
    install_containerd "$arch" "$to_version"
    install_runc "$arch" "$to_version"
    
    # 5. 迁移配置文件
    migrate_config "$from_version" "$to_version"
    
    # 6. 重启服务
    log_info "启动Docker服务..."
    systemctl daemon-reload
    systemctl start containerd
    systemctl start docker
    
    # 7. 验证安装
    verify_installation
    
    log_info "==========================================="
    log_info "Docker已成功升级到 $to_version"
    log_info "==========================================="
}

# 主函数
main() {
    log_info "=========================================="
    log_info "开始Docker安装/升级流程..."
    log_info "=========================================="

    check_root
    check_dependencies
    check_workdir
    
    # 解析命令行参数
    parse_args "$@"
    
    # 当前架构
    arch=$(detect_arch)
    log_info "检测到系统架构: $arch"
    
    # 检测是否已安装Docker
    local existing_version=$(detect_current_docker_version)
    
    # 如果已安装Docker且不是升级模式，询问用户确认
    if [[ -n "$existing_version" && "$UPGRADE_MODE" != "true" ]]; then
        log_warn "检测到系统中已安装Docker版本: $existing_version"
        if [[ "$existing_version" == "$DOCKER_VERSION" ]]; then
            log_warn "当前已安装版本与要安装的版本 $DOCKER_VERSION 相同"
            read -p "是否继续重新安装? [y/N] " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                log_info "用户取消安装"
                exit 0
            fi
            log_info "用户确认继续安装相同版本: $DOCKER_VERSION"
        else
            log_warn "推荐使用升级模式安装: $0 -u"
            read -p "是否切换到升级模式? [Y/n] " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]] || [[ -z $REPLY ]]; then
                log_info "切换到升级模式"
                UPGRADE_MODE=true
                FROM_VERSION="$existing_version"
            else
                read -p "是否继续全新安装? [y/N] " -n 1 -r
                echo
                if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                    log_info "用户取消安装"
                    exit 0
                fi
                log_info "用户确认继续全新安装版本: $DOCKER_VERSION"
            fi
        fi
    fi
    
    # 如果是升级模式
    if [[ "$UPGRADE_MODE" == "true" ]]; then
        # 如果未指定源版本，则自动检测
        if [[ -z "$FROM_VERSION" ]]; then
            FROM_VERSION=$(detect_current_docker_version)
            if [[ -z "$FROM_VERSION" ]]; then
                log_error "未检测到已安装的Docker版本，无法执行升级"
                log_error "请使用 --from 参数指定源版本，或执行全新安装"
                exit 1
            fi
            log_info "检测到当前Docker版本: $FROM_VERSION"
        fi
        
        # 检查源版本是否支持
        local source_supported=false
        for ver in "${SUPPORTED_VERSIONS[@]}"; do
            if [[ "$FROM_VERSION" == "$ver" ]]; then
                source_supported=true
                break
            fi
        done
        
        if [[ "$source_supported" != "true" ]]; then
            log_warn "警告: 从不支持的版本 $FROM_VERSION 升级"
            log_warn "支持的源版本: ${SUPPORTED_VERSIONS[*]}"
            log_warn "升级可能不会完全兼容，请谨慎继续"
            
            # 提示用户确认
            read -p "是否继续升级? [y/N] " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                log_info "用户取消升级"
                exit 0
            fi
        fi
        
        # 确认从源版本到目标版本的升级路径
        if [[ "$FROM_VERSION" == "$DOCKER_VERSION" ]]; then
            log_warn "源版本与目标版本相同: $FROM_VERSION"
            log_warn "跳过升级，仅执行验证"
            verify_installation
            exit 0
        fi
        
        # 执行升级
        check_files "$arch" "$DOCKER_VERSION"
        upgrade_docker "$FROM_VERSION" "$DOCKER_VERSION"
    else
        # 全新安装流程
        log_info "=========================================="
        log_info "执行Docker ${DOCKER_VERSION}全新安装..."
        log_info "=========================================="
        check_files "$arch" "$DOCKER_VERSION"
        extract_docker "$arch" "$DOCKER_VERSION"
        install_containerd "$arch" "$DOCKER_VERSION"
        install_runc "$arch" "$DOCKER_VERSION"
        install_docker "$arch" "$DOCKER_VERSION" 
        verify_installation
    fi

    log_info "=========================================="
    log_info "Docker安装/升级流程完成"
    log_info "Docker版本: $(docker --version)"
    log_info "Containerd版本: $(containerd --version)"
    log_info "Runc版本: $(runc --version)"
    log_info "Docker Compose版本: $(docker-compose --version)"
    log_info "=========================================="
}

# 执行主函数
main "$@"
