#!/bin/bash

# ARM64构建问题修复脚本
# 用于解决Ubuntu ARM64架构Docker镜像构建失败的问题

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检测当前架构
detect_architecture() {
    local arch=$(uname -m)
    case $arch in
        x86_64)
            echo "amd64"
            ;;
        aarch64|arm64)
            echo "arm64"
            ;;
        *)
            echo "unknown"
            ;;
    esac
}

# 检查Docker是否支持多架构构建
check_docker_buildx() {
    log_info "检查Docker Buildx支持..."
    
    if ! docker buildx version >/dev/null 2>&1; then
        log_error "Docker Buildx未安装或不可用"
        log_info "请安装Docker Buildx或升级到Docker 19.03+版本"
        return 1
    fi
    
    log_info "✓ Docker Buildx可用"
    return 0
}

# 创建多架构构建器
setup_buildx_builder() {
    log_info "设置多架构构建器..."
    
    # 检查是否已存在构建器
    if docker buildx ls | grep -q "multiarch-builder"; then
        log_info "多架构构建器已存在，使用现有构建器"
        docker buildx use multiarch-builder
    else
        log_info "创建新的多架构构建器..."
        docker buildx create --name multiarch-builder --driver docker-container --use
        docker buildx inspect --bootstrap
    fi
    
    log_info "✓ 多架构构建器设置完成"
}

# 修复Dockerfile中的ARM64问题
fix_dockerfile_arm64() {
    local dockerfile_path="$1"
    local backup_path="${dockerfile_path}.backup.$(date +%Y%m%d_%H%M%S)"
    
    log_info "修复Dockerfile ARM64问题: $dockerfile_path"
    
    # 备份原文件
    if [ -f "$dockerfile_path" ]; then
        cp "$dockerfile_path" "$backup_path"
        log_info "已备份原文件到: $backup_path"
    fi
    
    # 检查是否需要使用ARM64专用模板
    if [ -f "${dockerfile_path%/*}/Dockerfile.arm64.template" ]; then
        log_info "发现ARM64专用模板，建议使用该模板进行构建"
        return 0
    fi
    
    log_info "✓ Dockerfile修复完成"
}

# 清理Docker构建缓存
clean_docker_cache() {
    log_info "清理Docker构建缓存..."
    
    # 清理构建缓存
    docker builder prune -f >/dev/null 2>&1 || true
    
    # 清理悬空镜像
    docker image prune -f >/dev/null 2>&1 || true
    
    log_info "✓ Docker缓存清理完成"
}

# 测试ARM64构建
test_arm64_build() {
    local dockerfile_dir="$1"
    local test_tag="test-arm64-ubuntu:latest"
    
    log_info "测试ARM64构建..."
    
    cd "$dockerfile_dir"
    
    # 创建简单的测试Dockerfile
    cat > Dockerfile.test << 'EOF'
FROM ubuntu:22.04

RUN set -ex \
    && apt-get update \
    && DEBIAN_FRONTEND=noninteractive apt-get install -y \
       ca-certificates \
       curl \
       gnupg \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

CMD ["echo", "ARM64 build test successful"]
EOF

    # 尝试构建测试镜像
    if docker buildx build --platform linux/arm64 -t "$test_tag" -f Dockerfile.test . --load; then
        log_info "✓ ARM64构建测试成功"
        docker rmi "$test_tag" >/dev/null 2>&1 || true
        rm -f Dockerfile.test
        return 0
    else
        log_error "ARM64构建测试失败"
        rm -f Dockerfile.test
        return 1
    fi
}

# 提供构建建议
provide_build_suggestions() {
    local arch=$(detect_architecture)
    
    log_info "构建建议："
    echo
    echo "1. 对于ARM64架构，建议使用以下命令构建："
    echo "   docker buildx build --platform linux/arm64 -t your-image:tag ."
    echo
    echo "2. 如果遇到镜像源问题，可以尝试："
    echo "   - 使用Ubuntu Ports源（已在修复后的模板中配置）"
    echo "   - 使用清华大学镜像源作为备用"
    echo "   - 在构建时添加 --network=host 参数"
    echo
    echo "3. 对于GPG密钥问题，新模板已使用现代化的密钥管理方式"
    echo
    echo "4. 如果构建仍然失败，可以尝试："
    echo "   - 清理Docker缓存: docker builder prune -f"
    echo "   - 重启Docker服务"
    echo "   - 检查网络连接"
    echo
}

# 主函数
main() {
    local dockerfile_path="${1:-}"
    
    log_info "ARM64构建问题修复工具"
    log_info "========================"
    
    # 检测架构
    local arch=$(detect_architecture)
    log_info "检测到的架构: $arch"
    
    # 检查Docker Buildx
    if ! check_docker_buildx; then
        exit 1
    fi
    
    # 设置构建器
    setup_buildx_builder
    
    # 清理缓存
    clean_docker_cache
    
    # 修复Dockerfile（如果提供了路径）
    if [ -n "$dockerfile_path" ] && [ -f "$dockerfile_path" ]; then
        fix_dockerfile_arm64 "$dockerfile_path"
    fi
    
    # 测试构建（如果在正确的目录中）
    if [ -f "Dockerfile.template" ] || [ -f "Dockerfile" ]; then
        test_arm64_build "$(pwd)"
    fi
    
    # 提供建议
    provide_build_suggestions
    
    log_info "修复完成！"
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [Dockerfile路径]"
    echo
    echo "ARM64构建问题修复工具"
    echo
    echo "选项:"
    echo "  -h, --help    显示此帮助信息"
    echo
    echo "示例:"
    echo "  $0                                          # 在当前目录运行修复"
    echo "  $0 docker/infrastructure/os/ubuntu/Dockerfile.template  # 修复指定文件"
}

# 参数处理
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
