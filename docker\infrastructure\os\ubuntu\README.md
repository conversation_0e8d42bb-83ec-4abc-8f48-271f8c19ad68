# Ubuntu基础镜像构建说明

本目录包含用于构建Ubuntu基础操作系统镜像的文件，这些镜像包括对NETCA_CRYPTO的支持，并可选择安装密码卡组件。

## 目录结构

- `Dockerfile.template`: Ubuntu镜像的Dockerfile模板
- `versions.json`: 支持的Ubuntu版本配置
- `README.md`: 本说明文件

## 支持的版本

目前支持以下Ubuntu版本:

- 22.04 (Jammy)
- 20.04 (Focal)
- 18.04 (Bionic)

每个版本支持amd64和arm64架构。

## 构建镜像

有两种方式构建Ubuntu基础镜像：

### 1. 单独构建特定版本

使用`build-os.sh`脚本构建特定版本的Ubuntu镜像。

```bash
# 基本用法
./docker/scripts/build/build-os.sh ubuntu <版本> [架构]

# 示例 - 构建Ubuntu 22.04 amd64版本
./docker/scripts/build/build-os.sh ubuntu 22.04 amd64

# 包含密码卡支持
./docker/scripts/build/build-os.sh ubuntu 22.04 amd64 --install-card-mgr --install-sc62
```

### 2. 批量构建所有版本

使用`build-all-os.sh`脚本批量构建所有版本的Ubuntu镜像。

```bash
# 构建所有支持的Ubuntu版本
./docker/scripts/build/build-all-os.sh --filter=ubuntu

# 构建所有支持的Ubuntu版本，并包含密码卡支持
./docker/scripts/build/build-all-os.sh --filter=ubuntu --install-card-mgr --install-sc62
```

## 密码卡支持选项

构建脚本支持以下密码卡选项：

- `--install-card-mgr`: 安装密码卡管理的JNI库
- `--install-sc62`: 安装SansecCard_SC62组件
- `--install-sc34`: 预留用于未来支持SansecCard_SC34卡
- `--install-all-cards`: 安装所有支持的密码卡组件

## 镜像标签命名规则

生成的镜像使用以下命名规则：

- 基本镜像: `<registry>/<namespace>/infra/os/ubuntu:<version>-<arch>`
- 包含密码卡: `<registry>/<namespace>/infra/os/ubuntu:<version>-<arch>-crypto`
- 包含特定卡: `<registry>/<namespace>/infra/os/ubuntu:<version>-<arch>-crypto-sc62`

例如：
- `harbor.example.com/btit/infra/os/ubuntu:22.04-amd64`
- `harbor.example.com/btit/infra/os/ubuntu:22.04-amd64-crypto-sc62`

## 注意事项

1. 运行构建脚本前，请确保已安装Docker和jq
2. 使用密码卡功能需要确保相关组件在`shared/crypto-files/<版本>/<架构>/NETCA_CRYPTO_linux32_64`目录中可用
3. 如果希望将镜像推送到Harbor仓库，请确保在构建前已配置Harbor相关信息

## 功能特点

- 基于docker.zysproxy.online/ubuntu官方镜像
- 设置亚洲/上海时区
- 配置UTF-8语言环境
- 安装常用工具包
- 集成NETCA CRYPTO组件
- 配置库文件链接和LD_LIBRARY_PATH环境变量

## 使用示例

此基础镜像可作为其他应用镜像的基础，例如：

```dockerfile
FROM docker.zysproxy.online/ubuntu:22.04-amd64

# 添加应用程序
COPY app /app

# 设置启动命令
CMD ["./app"]
``` 