#!/bin/bash

# ARM64架构操作系统镜像构建脚本
# 专门用于解决ARM64架构下的构建问题

set -euo pipefail

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../.." && pwd)"

# 加载公共库
source "$SCRIPT_DIR/../lib/common.sh"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
用法: $0 <操作系统> <版本> [选项]

ARM64架构操作系统镜像构建脚本

参数:
  操作系统    支持的操作系统 (ubuntu, alpine, centos等)
  版本        操作系统版本号

选项:
  --arch ARCH              目标架构 (默认: arm64)
  --crypto-version VER     CRYPTO版本 (默认: 从配置文件读取)
  --install-card-mgr       安装密码卡管理组件
  --install-sc62           安装SansecCard SC62组件
  --install-sc34           安装SansecCard SC34组件
  --registry REGISTRY      镜像仓库地址
  --namespace NAMESPACE    镜像命名空间
  --push                   构建后推送镜像
  --no-cache               不使用构建缓存
  --dry-run                仅显示构建命令，不实际执行
  -h, --help               显示此帮助信息

示例:
  $0 ubuntu 22.04
  $0 ubuntu 22.04 --install-sc34 --push
  $0 ubuntu 20.04 --arch arm64 --registry harbor.example.com

支持的操作系统和版本:
  ubuntu: 22.04, 20.04, 18.04
  alpine: 3.17, 3.16, 3.15
  centos: 7, 8
EOF
}

# 检查Docker Buildx
check_buildx() {
    if ! docker buildx version >/dev/null 2>&1; then
        log_error "Docker Buildx不可用，请安装或升级Docker"
        exit 1
    fi
    
    # 确保有多架构构建器
    if ! docker buildx ls | grep -q "multiarch"; then
        log_info "创建多架构构建器..."
        docker buildx create --name multiarch-builder --driver docker-container --use
        docker buildx inspect --bootstrap
    else
        docker buildx use multiarch-builder 2>/dev/null || docker buildx use default
    fi
}

# 检测并选择合适的Dockerfile模板
select_dockerfile_template() {
    local os="$1"
    local arch="$2"
    local base_dir="$PROJECT_ROOT/docker/infrastructure/os/$os"
    
    # ARM64专用模板优先
    if [ "$arch" = "arm64" ] && [ -f "$base_dir/Dockerfile.arm64.template" ]; then
        echo "$base_dir/Dockerfile.arm64.template"
        return 0
    fi
    
    # 通用模板
    if [ -f "$base_dir/Dockerfile.template" ]; then
        echo "$base_dir/Dockerfile.template"
        return 0
    fi
    
    log_error "未找到适用的Dockerfile模板: $base_dir"
    exit 1
}

# 构建镜像
build_image() {
    local os="$1"
    local version="$2"
    local arch="$3"
    local crypto_version="$4"
    local install_card_mgr="$5"
    local install_sc62="$6"
    local install_sc34="$7"
    local registry="$8"
    local namespace="$9"
    local push="${10}"
    local no_cache="${11}"
    local dry_run="${12}"
    
    # 获取版本配置
    local versions_file="$PROJECT_ROOT/docker/infrastructure/os/$os/versions.json"
    if [ ! -f "$versions_file" ]; then
        log_error "版本配置文件不存在: $versions_file"
        exit 1
    fi
    
    # 读取版本信息
    local codename=$(jq -r ".versions[] | select(.version == \"$version\") | .codename" "$versions_file")
    local packages=$(jq -r ".versions[] | select(.version == \"$version\") | .packages | join(\" \")" "$versions_file")
    
    if [ "$codename" = "null" ] || [ -z "$codename" ]; then
        log_error "不支持的版本: $version"
        exit 1
    fi
    
    # 选择Dockerfile模板
    local template_file=$(select_dockerfile_template "$os" "$arch")
    log_info "使用模板: $template_file"
    
    # 创建临时构建目录
    local build_dir=$(mktemp -d)
    local dockerfile="$build_dir/Dockerfile"
    
    # 处理模板变量
    sed -e "s/{{VERSION}}/$version/g" \
        -e "s/{{CODENAME}}/$codename/g" \
        -e "s/{{PACKAGES}}/$packages/g" \
        "$template_file" > "$dockerfile"
    
    # 复制必要的资源文件
    if [ -d "$PROJECT_ROOT/docker/infrastructure/os/shared" ]; then
        cp -r "$PROJECT_ROOT/docker/infrastructure/os/shared" "$build_dir/"
    fi
    
    # 构建镜像标签
    local image_tag="$registry/$namespace/infra/os/$os:$version-$arch"
    
    # 添加变体后缀
    local variant_suffix=""
    if [ "$install_card_mgr" = "true" ] || [ "$install_sc62" = "true" ] || [ "$install_sc34" = "true" ]; then
        variant_suffix="-crypto"
        [ "$install_sc62" = "true" ] && variant_suffix="${variant_suffix}-sc62"
        [ "$install_sc34" = "true" ] && variant_suffix="${variant_suffix}-sc34"
        image_tag="${image_tag}${variant_suffix}"
    fi
    
    # 构建参数
    local build_args=(
        "--platform" "linux/$arch"
        "--build-arg" "CRYPTO_VERSION=$crypto_version"
        "--build-arg" "ARCH=$arch"
        "--build-arg" "INSTALL_CARD_MNGR=$install_card_mgr"
        "--build-arg" "INSTALL_SC62=$install_sc62"
        "--build-arg" "INSTALL_SC34=$install_sc34"
        "-t" "$image_tag"
        "-f" "$dockerfile"
    )
    
    # 添加可选参数
    [ "$no_cache" = "true" ] && build_args+=("--no-cache")
    [ "$push" = "true" ] && build_args+=("--push") || build_args+=("--load")
    
    # 构建命令
    local build_cmd="docker buildx build ${build_args[*]} $build_dir"
    
    if [ "$dry_run" = "true" ]; then
        log_info "构建命令（干运行）:"
        echo "$build_cmd"
    else
        log_info "开始构建镜像: $image_tag"
        log_info "构建目录: $build_dir"
        
        # 执行构建
        if eval "$build_cmd"; then
            log_info "✓ 镜像构建成功: $image_tag"
        else
            log_error "镜像构建失败"
            rm -rf "$build_dir"
            exit 1
        fi
    fi
    
    # 清理临时目录
    rm -rf "$build_dir"
}

# 主函数
main() {
    # 默认参数
    local arch="arm64"
    local crypto_version=""
    local install_card_mgr="false"
    local install_sc62="false"
    local install_sc34="false"
    local registry="docker.zysproxy.online"
    local namespace="btit"
    local push="false"
    local no_cache="false"
    local dry_run="false"
    
    # 解析参数
    local os=""
    local version=""
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --arch)
                arch="$2"
                shift 2
                ;;
            --crypto-version)
                crypto_version="$2"
                shift 2
                ;;
            --install-card-mgr)
                install_card_mgr="true"
                shift
                ;;
            --install-sc62)
                install_sc62="true"
                shift
                ;;
            --install-sc34)
                install_sc34="true"
                shift
                ;;
            --registry)
                registry="$2"
                shift 2
                ;;
            --namespace)
                namespace="$2"
                shift 2
                ;;
            --push)
                push="true"
                shift
                ;;
            --no-cache)
                no_cache="true"
                shift
                ;;
            --dry-run)
                dry_run="true"
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            -*)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
            *)
                if [ -z "$os" ]; then
                    os="$1"
                elif [ -z "$version" ]; then
                    version="$1"
                else
                    log_error "过多的位置参数: $1"
                    show_help
                    exit 1
                fi
                shift
                ;;
        esac
    done
    
    # 检查必需参数
    if [ -z "$os" ] || [ -z "$version" ]; then
        log_error "缺少必需参数"
        show_help
        exit 1
    fi
    
    # 获取默认crypto版本
    if [ -z "$crypto_version" ]; then
        crypto_version="1.0.0"  # 默认版本
    fi
    
    log_info "ARM64架构镜像构建"
    log_info "=================="
    log_info "操作系统: $os"
    log_info "版本: $version"
    log_info "架构: $arch"
    log_info "CRYPTO版本: $crypto_version"
    log_info "镜像仓库: $registry"
    log_info "命名空间: $namespace"
    
    # 检查Docker Buildx
    check_buildx
    
    # 构建镜像
    build_image "$os" "$version" "$arch" "$crypto_version" \
                "$install_card_mgr" "$install_sc62" "$install_sc34" \
                "$registry" "$namespace" "$push" "$no_cache" "$dry_run"
}

# 执行主函数
main "$@"
