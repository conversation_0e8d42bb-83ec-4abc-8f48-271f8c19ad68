version: '3.8'

services:
  redis-master:
    image: 192.168.200.39:1443/btit/infra/redis:7.4.2
    container_name: ${PRODUCT_NAME:-cloudkey}-redis-master
    command: redis-server /usr/local/etc/redis/redis.conf --requirepass "${REDIS_PASSWORD}"
    user: "999:999"
    volumes:
      - ../config/redis.conf:/usr/local/etc/redis/redis.conf:ro
      - ../data/${PRODUCT_NAME:-cloudkey}/master:/data
      - ../logs/${PRODUCT_NAME:-cloudkey}/master:/var/log/redis
    ports:
      - "${REDIS_PORT:-16379}:6379"
    restart: always
    networks:
      - redis-net
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    env_file:
      - ../env/cloudkey.env

networks:
  redis-net:
    driver: bridge 