#!/bin/bash
# 部署Nginx网关服务

# 脚本所在目录
SCRIPT_DIR=$(cd "$(dirname "$0")" && pwd)
BASE_DIR=$(dirname "$SCRIPT_DIR")
SERVICE_NAME="Nginx网关服务"

# 在文件开头添加 Docker Compose 命令变量
DOCKER_COMPOSE_CMD=""

# 在颜色定义后添加函数
# 确定使用哪个版本的 Docker Compose 命令
determine_docker_compose_cmd() {
    # 首先尝试 Docker Compose V2
    if docker compose version &> /dev/null; then
        DOCKER_COMPOSE_CMD="docker compose"
        return 0
    fi
    
    # 如果 V2 不可用，尝试传统版本
    if command -v docker-compose &> /dev/null; then
        DOCKER_COMPOSE_CMD="docker-compose"
        return 0
    fi
    
    # 如果都不可用
    echo "错误: 未找到可用的 Docker Compose 命令"
    return 1
}

# 检查目录结构
check_dir_structure() {
    echo "检查目录结构..."
    mkdir -p "$BASE_DIR/config/conf.d"
    mkdir -p "$BASE_DIR/config/ssl"
    mkdir -p "$BASE_DIR/logs"
    mkdir -p "$BASE_DIR/backups"
    
    # 检查SSL证书
    if [ ! -f "$BASE_DIR/config/ssl/nginx.cer" ] || [ ! -f "$BASE_DIR/config/ssl/nginx_cert.key" ]; then
        echo "警告: SSL证书文件不存在，请放置证书文件到 $BASE_DIR/config/ssl/ 目录！"
        echo "     需要的证书文件: nginx.cer, nginx_cert.key"
    fi
    
    # 检查cloudkey配置
    if [ ! -f "$BASE_DIR/config/conf.d/cloudkey.conf" ]; then
        echo "警告: 未找到云密钥系统配置文件，请确认配置是否正确！"
    fi
    
    echo "目录结构检查完成。"
}

# 检查Docker环境
check_docker() {
    echo "检查Docker环境..."
    
    # 检查Docker是否安装
    if ! command -v docker &> /dev/null; then
        echo "错误: Docker未安装或不在PATH中！"
        exit 1
    fi
    
    # 检查Docker Compose是否安装
    if ! command -v docker-compose &> /dev/null; then
        echo "错误: Docker Compose未安装或不在PATH中！"
        exit 1
    fi
    
    # 检查Docker是否运行
    if ! docker info &> /dev/null; then
        echo "错误: Docker服务未运行或无权限访问！"
        exit 1
    fi
    
    echo "Docker环境检查通过。"
}

# 修改检查环境函数
check_environment() {
    echo "检查运行环境..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        echo "错误: Docker未安装或不在PATH中！"
        return 1
    fi
    
    # 检查Docker Compose
    if ! determine_docker_compose_cmd; then
        echo "错误: Docker Compose未安装或不在PATH中！"
        return 1
    fi
    
    return 0
}

# 启动服务
start_service() {
    echo "正在启动$SERVICE_NAME..."
    cd "$BASE_DIR" || exit 1
    
    # 备份当前配置（如果之前有部署）
    if [ -f "$BASE_DIR/docker-compose.yml" ] && [ -d "$BASE_DIR/config" ]; then
        echo "检测到现有配置，创建部署前备份..."
        "$SCRIPT_DIR/nginx-backup.sh" backup &> /dev/null
        echo "备份完成。"
    fi
    
    # 启动服务
    $DOCKER_COMPOSE_CMD up -d
    
    if [ $? -eq 0 ]; then
        echo "$SERVICE_NAME启动成功！"
        
        # 显示服务状态
        echo "服务状态:"
        docker ps --filter "name=gateway-nginx" --format "表格 {{.ID}}\t{{.Image}}\t{{.Status}}\t{{.Ports}}"
        
        echo "可以使用以下命令管理服务:"
        echo "  启动/停止: $SCRIPT_DIR/nginx-control.sh [start|stop|restart]"
        echo "  查看状态: $SCRIPT_DIR/nginx-control.sh status"
        echo "  备份配置: $SCRIPT_DIR/nginx-backup.sh backup"
    else
        echo "$SERVICE_NAME启动失败，请检查配置和日志！"
        exit 1
    fi
}

# 主函数
main() {
    echo "======== 开始部署$SERVICE_NAME ========"
    check_docker
    check_dir_structure
    start_service
    echo "======== $SERVICE_NAME部署完成 ========"
}

# 执行主函数
main 