#!/bin/bash
# 路径处理函数库
# 提供便捷的路径计算函数，简化脚本对各目录的访问

# 获取MySQL编排文件的路径
function get_compose_path() {
  local product="$1"
  echo "$(cd "$(dirname "${BASH_SOURCE[0]}")/../../compose" && pwd)/${product}-mysql.yml"
}

# 获取MySQL数据目录路径
function get_mysql_data_path() {
  local product="$1"
  echo "$(cd "$(dirname "${BASH_SOURCE[0]}")/../../data" && pwd)/${product}"
}

# 获取MySQL配置目录路径
function get_mysql_config_path() {
  echo "$(cd "$(dirname "${BASH_SOURCE[0]}")/../../config" && pwd)"
}

# 获取MySQL备份目录路径
function get_mysql_backup_path() {
  local product="$1"
  echo "$(cd "$(dirname "${BASH_SOURCE[0]}")/../../backups" && pwd)/${product}"
} 