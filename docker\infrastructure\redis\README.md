# Redis 容器镜像

## 简介
此目录包含用于构建医疗产品缓存服务的Redis容器镜像配置。基于官方Redis 7.2镜像，针对医疗产品场景进行了优化配置。

## 目录结构
```
redis/
├── Dockerfile          # 镜像构建文件
└── config/            # 配置文件目录
    └── redis.conf    # Redis主配置文件
```

## 特性
- 基于Redis 7.2官方镜像
- 持久化配置（AOF + RDB）
- 内存管理优化
- 完整的日志记录
- 内置健康检查
- 安全加固配置

## 配置说明
### 主要配置参数
- 最大内存：1GB
- 内存策略：allkeys-lru
- 持久化：AOF + RDB混合模式
- 连接超时：300秒
- 数据库数量：16

### 数据持久化
使用Docker命名卷进行数据持久化：
```yaml
volumes:
  - redis_data:/data/redis
  - redis_logs:/var/log/redis
```

### 环境变量
- `TZ`: 时区，默认Asia/Shanghai

## 使用方法
1. 构建镜像
```bash
docker build -t medical/redis:7.2 .
```

2. 运行容器
```bash
docker run -d \
  --name redis \
  -v redis_data:/data/redis \
  -v redis_logs:/var/log/redis \
  -p 6379:6379 \
  medical/redis:7.2
```

## 安全注意事项
1. 生产环境必须修改默认密码（requirepass）
2. 建议在受信任网络中使用
3. 定期备份数据
4. 监控内存使用情况

## 维护说明
1. 日志位置：/var/log/redis/
2. 配置文件：/usr/local/etc/redis/redis.conf
3. 数据目录：/data/redis/

## 健康检查
镜像内置健康检查机制，每30秒执行一次，通过redis-cli ping命令检查服务状态。

## 性能优化
1. 内存配置
   - maxmemory设置为1GB
   - maxmemory-policy设置为allkeys-lru
   - maxmemory-samples设置为5

2. 持久化配置
   - AOF：每秒同步
   - RDB：动态保存点

## 监控指标
1. 内存使用率
2. 连接数
3. 命中率
4. 持久化状态

## 故障排除
1. 查看日志：
```bash
docker exec redis tail -f /var/log/redis/redis.log
```

2. 检查内存使用：
```bash
docker exec redis redis-cli info memory
```

## 版本历史
- 1.0.0: 初始版本，基于Redis 7.2
