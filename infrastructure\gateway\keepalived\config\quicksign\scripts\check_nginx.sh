#!/bin/bash
# Nginx健康检查脚本模板
# 由generate_config.sh自动替换变量

NGINX_HOST="127.0.0.1"
NGINX_PORT="80"
NGINX_STATUS_PATH="/nginx_status"
PRODUCT_NAME="quicksign"
PRODUCT_DISPLAY_NAME="速签通系统"

LOG_FILE="/var/log/keepalived-nginx-${PRODUCT_NAME}-check.log"

# 记录日志
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> ${LOG_FILE}
}

# 检查Nginx服务是否正常
check_nginx() {
    local result
    local status_code
    
    result=$(curl -s -o /dev/null -w "%{http_code}" http://${NGINX_HOST}:${NGINX_PORT}${NGINX_STATUS_PATH} 2>&1)
    status_code=$?
    
    if [[ $status_code -eq 0 && "$result" == "200" ]]; then
        log "${PRODUCT_DISPLAY_NAME} Nginx服务状态正常"
        return 0
    else
        log "${PRODUCT_DISPLAY_NAME} Nginx服务异常: $result, 状态码: $status_code"
        return 1
    fi
}

# 执行检查
check_nginx
exit $? 