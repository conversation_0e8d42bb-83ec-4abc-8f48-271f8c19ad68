# 版本配置文件说明

本目录包含了所有组件的版本配置文件，用于管理各个组件的版本信息、构建参数和依赖关系。

## 文件命名规范

所有版本配置文件采用统一的命名格式：`{component}_versions.json`

当前包含的配置文件：
- `jdk_versions.json`: JDK版本配置
- `os_versions.json`: 操作系统（Ubuntu）版本配置
- `docker_versions.json`: Docker版本配置
- `tomcat_versions.json`: Tomcat版本配置

## 配置文件结构

### 通用字段
所有配置文件都应包含以下基本字段：
```json
{
  "versions": {},  // 版本详细信息
  "build_defaults": {
    "namespace": "btit",  // 默认命名空间
    "repository": "infra/xxx"  // 默认仓库路径
  }
}
```

### 特定组件配置

#### JDK配置 (jdk_versions.json)
```json
{
  "versions": {
    "8": {
      "jdk_version": "1.8.0_172",
      "build_number": "01",
      "variants": ["jammy"],
      "supported_architectures": ["amd64", "arm64"]
    }
  },
  "variants": {
    "jammy": {
      "ubuntu_version": "22.04",
      "description": "基于Ubuntu 22.04 (Jammy Jellyfish)"
    }
  }
}
```

#### OS配置 (os_versions.json)
```json
{
  "versions": [
    {
      "version": "22.04",
      "codename": "jammy",
      "architectures": ["amd64", "arm64"],
      "packages": "...",
      "crypto_version": "1.0.4"
    }
  ],
  "default_version": "22.04",
  "default_arch": "amd64"
}
```

#### Docker配置 (docker_versions.json)
```json
{
  "default_version": "20.10.24",
  "versions": {
    "20.10.24": {
      "containerd_version": "1.6.22",
      "runc_version": "1.1.7",
      "compose_version": "2.17.2"
    }
  }
}
```

## 使用说明

1. 所有构建脚本应从此目录读取版本配置
2. 配置文件修改需要同步更新相关的构建脚本
3. 添加新的版本配置时，需要在本文档中更新说明

## 注意事项

1. 所有JSON文件必须使用UTF-8编码
2. 版本号应遵循语义化版本规范
3. 配置文件的修改应通过版本控制系统追踪
4. 重要的配置修改需要在提交信息中说明原因

# Tomcat 版本配置说明

## 文件说明

`tomcat_versions.json` 文件用于配置Tomcat构建所需的版本信息，包括下载地址、SHA512校验和等。

## SHA512校验和自动获取功能

为了简化运维工作，系统支持自动获取SHA512校验和功能：

1. 在`build_defaults`中配置`auto_fetch_sha512`参数：
   - `true`：当版本配置中未提供SHA512时自动从Apache官方获取（默认）
   - `false`：当版本配置中未提供SHA512时报错

2. 在版本配置中：
   - 可以省略`sha512`字段，系统会自动获取
   - 也可以手动指定`sha512`字段的值

## 配置示例

```json
{
  "build_defaults": {
    "namespace": "btit",
    "repository": "infra/tomcat",
    "registry": "192.168.200.39:1443",
    "auto_fetch_sha512": true
  },
  "versions": {
    "9.0.100": {
      "download_url": "https://archive.apache.org/dist/tomcat/tomcat-9/v9.0.100/bin/apache-tomcat-9.0.100.tar.gz"
    },
    "10.1.17": {
      "download_url": "https://archive.apache.org/dist/tomcat/tomcat-10/v10.1.17/bin/apache-tomcat-10.1.17.tar.gz",
      "sha512": "92ca64e2a9118908281bc1db9804ca0dc85ab81cc2a9c31d5a404d23a6e78c11e2e73b95698bbcef3df4fb055ea0d0bb2aaa951f03d4f2dc6ef83025d08e92f3"
    }
  }
}
```

## 调试模式

在调试模式下运行脚本可以查看自动获取的SHA512值：

```bash
./build-tomcat.sh --debug
```

系统会显示自动获取的SHA512值并提供建议添加到配置文件的内容：

```
【调试信息】建议添加以下内容到配置文件 ./versions/tomcat_versions.json:
  "9.0.100": {
    "download_url": "https://archive.apache.org/dist/tomcat/tomcat-9/v9.0.100/bin/apache-tomcat-9.0.100.tar.gz",
    "sha512": "ac34f5c45ed5a5ba0a5a57a19a9d6e58bcc90954ece7130c86adff651659d86e88fcb27e4d3fbbb90a6fccaf01dbf3e7ecb0d7ca66e2da83eaad5dd9da0c18e1"
  }
```

## 安全考虑

1. 自动获取SHA512功能依赖于Apache官方服务器的可用性和安全性
2. 对于安全敏感环境，建议手动获取和验证SHA512值后添加到配置文件
3. 可以通过设置`auto_fetch_sha512: false`来禁用自动获取功能，强制要求手动配置SHA512 