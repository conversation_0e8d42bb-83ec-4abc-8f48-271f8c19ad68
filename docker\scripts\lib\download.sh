#!/bin/bash
# ===============================================================
# 下载功能库
# 提供文件下载和校验相关的功能
# ===============================================================

# 确保只加载一次
[[ -n "${_DOWNLOAD_SH_LOADED:-}" ]] && return 0
readonly _DOWNLOAD_SH_LOADED=1

# 导入依赖库
source "$(dirname "${BASH_SOURCE[0]}")/logger.sh"
source "$(dirname "${BASH_SOURCE[0]}")/utils.sh"

# 初始化完成后再输出日志
if [[ -n "${LOG_LEVEL:-}" ]]; then
    log_info "下载功能库加载完成"
    log_debug "当前脚本路径: ${BASH_SOURCE[0]}"
fi

# 检查并创建资源目录
ensure_resource_dir() {
    local resource_dir="$1"
    
    if [[ -z "$resource_dir" ]]; then
        log_error "未提供资源目录路径" >&2
        return 1
    fi
    
    if [[ ! -d "$resource_dir" ]]; then
        log_info "资源目录不存在，正在创建: $resource_dir" >&2
        if ! mkdir -p "$resource_dir"; then
            log_error "创建资源目录失败: $resource_dir" >&2
            return 1
        fi
        log_info "✓ 资源目录创建成功" >&2
    else
        log_debug "资源目录已存在: $resource_dir" >&2
    fi
    
    return 0
}

# 检查本地文件是否存在且有效
check_local_file() {
    log_debug "检查本地文件: $1" >&2
    local file_path="$1"
    local expected_sha512="$2"
    
    if [[ ! -f "$file_path" ]]; then
        log_debug "本地文件不存在: $file_path" >&2
        return 1
    fi
    
    if [[ -n "$expected_sha512" ]]; then
        log_info "检查本地文件校验和: $file_path" >&2
        local actual_sha512
        if actual_sha512=$(calculate_sha512 "$file_path") && [[ "$actual_sha512" == "$expected_sha512" ]]; then
            log_info "✓ 本地文件校验和正确" >&2
            return 0
        else
            log_warn "本地文件校验和不正确，需要重新下载" >&2
            return 1
        fi
    else
        # 如果没有提供校验和，只检查文件大小
        if [[ -s "$file_path" ]]; then
            log_info "✓ 本地文件有效" >&2
            return 0
        else
            log_warn "本地文件为空，需要重新下载" >&2
            return 1
        fi
    fi
}

# 下载SHA512校验和文件
download_sha512_file() {
    local url="$1"
    local output_file="$2"
    
    if [[ -z "$url" ]] || [[ -z "$output_file" ]]; then
        log_error "未提供足够的参数" >&2
        return 1
    fi
    
    log_info "正在获取SHA512校验和..." >&2
    if ! curl -sfL -o "$output_file" "$url"; then
        log_error "获取SHA512校验和失败: $url" >&2
        return 1
    fi
    
    # 验证文件格式
    if [[ ! -s "$output_file" ]]; then
        log_error "SHA512文件为空" >&2
        rm -f "$output_file"
        return 1
    fi
    
    # 提取SHA512值，移除所有空格和换行符
    local sha512
    sha512=$(tr -d '[:space:]' < "$output_file" | grep -o '^[a-fA-F0-9]\{128\}')
    
    if [[ ! "$sha512" =~ ^[a-fA-F0-9]{128}$ ]]; then
        log_error "无效的SHA512格式" >&2
        rm -f "$output_file"
        return 1
    fi
    
    log_info "✓ 成功获取SHA512校验和" >&2
    echo "$sha512"
    return 0
}

# 从指定URL下载文件
download_from_url() {
    local download_url="$1"
    local output_file="$2"
    local verify_checksum="${3:-true}"
    local max_retries=3
    local retry_delay=5
    
    log_info "从URL下载文件..." >&2
    log_info "- 下载地址: $download_url" >&2
    log_info "- 输出文件: $output_file" >&2
    log_info "- 是否验证校验和: $verify_checksum" >&2
    
    # 确保输出目录存在
    local output_dir
    output_dir=$(dirname "$output_file")
    log_info "确保输出目录存在: $output_dir" >&2
    if ! ensure_resource_dir "$output_dir"; then
        log_error "创建输出目录失败" >&2
        return 1
    fi

    # 获取SHA512校验和（如果需要验证）
    local expected_sha512=""
    if [[ "$verify_checksum" == "true" ]]; then
        local sha512_url="${download_url}.sha512"
        log_info "尝试下载SHA512校验和文件: $sha512_url" >&2
        expected_sha512=$(download_sha512_file "$sha512_url" "${output_file}.sha512")
        if [[ $? -eq 0 ]]; then
            log_info "成功获取SHA512校验和" >&2
        else
            log_warn "无法获取SHA512校验和，将跳过文件验证" >&2
            verify_checksum="false"
        fi
    fi

    # 检查本地文件是否可用
    log_info "检查本地文件: $output_file" >&2
    if check_local_file "$output_file" "$expected_sha512"; then
        log_info "✓ 本地文件有效，无需重新下载" >&2
        return 0
    fi
    
    # 下载文件
    log_info "开始下载文件..." >&2
    local success=false
    
    for ((i=1; i<=max_retries; i++)); do
        log_info "开始下载 (第 $i 次尝试)..." >&2
        if curl -# -fL --connect-timeout 10 --retry 3 --retry-delay 2 \
                -H "User-Agent: Netca-Build-Script" \
                -o "$output_file" "$download_url" 2>&1 | while read -r line; do
            if [[ "$line" =~ ^[0-9]+\.[0-9]% ]]; then
                printf "\r下载进度: %s" "$line" >&2
            fi
        done && [ -s "$output_file" ]; then
            echo >&2 # 换行
            success=true
            log_info "✓ 下载成功" >&2
            break
        else
            echo >&2 # 换行
            local status_code
            status_code=$(curl -s -o /dev/null -w "%{http_code}" "$download_url")
            log_warn "第 $i 次尝试失败，HTTP状态码: $status_code" >&2
            log_debug "下载URL: $download_url" >&2
            
            if [ $i -lt $max_retries ]; then
                log_info "等待 ${retry_delay} 秒后重试..." >&2
                sleep $retry_delay
            fi
        fi
    done
    
    if ! $success; then
        log_error "下载失败" >&2
        rm -f "$output_file"
        return 1
    fi
    
    # 验证下载的文件
    if [[ ! -f "$output_file" ]] || [[ ! -s "$output_file" ]]; then
        log_error "下载的文件无效或为空" >&2
        rm -f "$output_file"
        return 1
    fi

    # 如果需要验证校验和，验证下载后的文件
    if [[ "$verify_checksum" == "true" ]] && [[ -n "$expected_sha512" ]]; then
        log_info "验证下载文件的SHA512校验和..." >&2
        local actual_sha512
        if ! actual_sha512=$(calculate_sha512 "$output_file"); then
            log_error "计算下载文件校验和失败" >&2
            rm -f "$output_file"
            return 1
        fi
        
        if [[ "$actual_sha512" != "$expected_sha512" ]]; then
            log_error "下载文件校验和验证失败" >&2
            log_error "期望的校验和: ${expected_sha512}" >&2
            log_error "实际的校验和: ${actual_sha512}" >&2
            rm -f "$output_file"
            return 1
        fi
        log_info "✓ 文件校验和验证成功" >&2
    fi
    
    log_info "✓ 文件下载成功: $output_file" >&2
    return 0
}

# 下载 Tomcat 包
download_tomcat() {
    local version="$1"
    local output_dir="$2"
    local versions_file="$3"
    local file_name="apache-tomcat-${version}.tar.gz"
    local output_file="${output_dir}/${file_name}"

    # 移除条件检查，始终输出日志（重定向到标准错误以确保显示）
    log_info "开始下载 Tomcat ${version}..." >&2
    log_info "输出目录: $output_dir" >&2
    log_info "文件名: $file_name" >&2
    log_info "完整输出路径: $output_file" >&2
    log_info "配置文件路径: $versions_file" >&2
    
    # 确保输出目录存在
    if ! ensure_resource_dir "$output_dir"; then
        log_error "创建输出目录失败: $output_dir"
        return 1
    fi
    
    # 检查配置文件
    if [[ ! -f "$versions_file" ]]; then
        log_error "版本配置文件不存在: $versions_file"
        return 1
    fi
    
    # 从配置文件获取下载地址
    local download_url
    download_url=$(jq -r --arg ver "$version" '.versions[$ver].download_url' "$versions_file")
    
    log_info "获取到的下载URL: $download_url" >&2
    
    if [[ "$download_url" == "null" ]] || [[ -z "$download_url" ]]; then
        log_error "在配置文件中未找到版本 $version 的下载地址" >&2
        return 1
    fi
    
    # 使用 download_from_url 下载文件
    if ! download_from_url "$download_url" "$output_file" true; then
        log_error "Tomcat包下载失败: $download_url" >&2
        return 1
    fi
    
    log_info "Tomcat包下载完成: $output_file" >&2
    
    echo "$output_file"
    return 0
}

# 导出公共函数
export -f ensure_resource_dir
export -f check_local_file
export -f download_sha512_file
export -f download_from_url
export -f calculate_sha512
export -f download_tomcat