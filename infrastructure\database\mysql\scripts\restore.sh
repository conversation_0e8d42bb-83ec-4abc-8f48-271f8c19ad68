#!/bin/bash
# MySQL 恢复脚本
# 2023-10-01
# 版本: 1.0.0

# 环境变量配置
ROOT_PASSWORD_FILE=${ROOT_PASSWORD_FILE:-"/run/secrets/mysql_root_password"}
BACKUP_DIR=${BACKUP_DIR:-"/var/backups/mysql"}
PRODUCT=${PRODUCT:-"cloudkey"}
DB_NAME=${DB_NAME:-"$PRODUCT"}
HOSTNAME=$(hostname)

# 获取密码
if [ -f "$ROOT_PASSWORD_FILE" ]; then
  ROOT_PASSWORD=$(cat $ROOT_PASSWORD_FILE)
else
  echo "错误: 找不到密码文件 $ROOT_PASSWORD_FILE"
  exit 1
fi

# 显示可用备份
list_backups() {
  local product_backup_dir="${BACKUP_DIR}/${PRODUCT}"
  
  if [ ! -d "$product_backup_dir" ]; then
    echo "错误: 备份目录不存在: $product_backup_dir"
    return 1
  fi
  
  echo "可用备份:"
  local backups=($(find "$product_backup_dir" -name "${PRODUCT}_*_*.sql.gz" -type f | sort -r))
  
  if [ ${#backups[@]} -eq 0 ]; then
    echo "  没有找到可用备份"
    return 1
  fi
  
  for i in "${!backups[@]}"; do
    local backup="${backups[$i]}"
    local file_size=$(du -h "$backup" | cut -f1)
    local timestamp=$(basename "$backup" | grep -oE "[0-9]{8}_[0-9]{6}" || echo "未知时间")
    local node=$(basename "$backup" | sed -E "s/${PRODUCT}_([^_]+)_.*/\1/" || echo "未知节点")
    
    echo "  [$i] $(basename "$backup") ($file_size, 节点: $node, 时间: $timestamp)"
  done
  
  return 0
}

# 验证备份文件的完整性
verify_backup() {
  local backup_file="$1"
  local md5_file="${backup_file}.md5"
  
  echo "验证备份文件完整性..."
  
  if [ ! -f "$backup_file" ]; then
    echo "错误: 备份文件不存在: $backup_file"
    return 1
  fi
  
  if [ -f "$md5_file" ]; then
    local expected_md5=$(cat "$md5_file" | awk '{print $1}')
    local actual_md5=$(md5sum "$backup_file" | awk '{print $1}')
    
    if [ "$expected_md5" != "$actual_md5" ]; then
      echo "错误: MD5校验失败"
      echo "期望的MD5: $expected_md5"
      echo "实际的MD5: $actual_md5"
      return 1
    else
      echo "MD5校验通过"
    fi
  else
    echo "警告: 没有找到MD5校验文件，跳过验证"
  fi
  
  # 尝试解压缩备份文件以确保其有效
  if ! zcat "$backup_file" | head -n 10 > /dev/null; then
    echo "错误: 备份文件损坏或无法解压缩"
    return 1
  fi
  
  echo "备份文件验证通过"
  return 0
}

# 恢复备份
restore_backup() {
  local backup_file="$1"
  
  echo "开始从 $backup_file 恢复到数据库 $DB_NAME..."
  
  # 确保数据库存在
  mysql -h"localhost" -u"root" -p"$ROOT_PASSWORD" -e "CREATE DATABASE IF NOT EXISTS \`$DB_NAME\`;"
  
  # 恢复备份
  zcat "$backup_file" | mysql -h"localhost" -u"root" -p"$ROOT_PASSWORD"
  
  # 检查恢复结果
  if [ $? -eq 0 ]; then
    echo "备份恢复成功"
    
    # 检查是否有binlog信息
    local binlog_file="${backup_file}.binlog"
    if [ -f "$binlog_file" ]; then
      echo "备份的二进制日志位置:"
      cat "$binlog_file"
    fi
    
    return 0
  else
    echo "备份恢复失败"
    return 1
  fi
}

# 交互式恢复函数
interactive_restore() {
  # 列出可用备份
  list_backups
  if [ $? -ne 0 ]; then
    return 1
  fi
  
  local product_backup_dir="${BACKUP_DIR}/${PRODUCT}"
  local backups=($(find "$product_backup_dir" -name "${PRODUCT}_*_*.sql.gz" -type f | sort -r))
  
  # 选择备份
  local selection
  echo -n "请选择要恢复的备份编号 [0-$((${#backups[@]}-1))]: "
  read selection
  
  if ! [[ "$selection" =~ ^[0-9]+$ ]] || [ "$selection" -ge "${#backups[@]}" ]; then
    echo "错误: 无效的选择"
    return 1
  fi
  
  local selected_backup="${backups[$selection]}"
  echo "已选择: $selected_backup"
  
  # 验证备份
  verify_backup "$selected_backup"
  if [ $? -ne 0 ]; then
    echo "备份验证失败，中止恢复操作"
    return 1
  fi
  
  # 确认操作
  echo "警告: 此操作将覆盖数据库 $DB_NAME 中的所有现有数据。"
  echo -n "是否继续? [y/N]: "
  local confirm
  read confirm
  
  if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
    echo "操作已取消"
    return 1
  fi
  
  # 恢复备份
  restore_backup "$selected_backup"
  return $?
}

# 指定备份恢复函数
specified_restore() {
  local backup_file="$1"
  
  # 验证备份
  verify_backup "$backup_file"
  if [ $? -ne 0 ]; then
    echo "备份验证失败，中止恢复操作"
    return 1
  fi
  
  # 确认操作
  echo "警告: 此操作将覆盖数据库 $DB_NAME 中的所有现有数据。"
  echo -n "是否继续? [y/N]: "
  local confirm
  read confirm
  
  if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
    echo "操作已取消"
    return 1
  fi
  
  # 恢复备份
  restore_backup "$backup_file"
  return $?
}

# 主函数
main() {
  echo "===== MySQL 恢复脚本开始执行 ====="
  echo "产品: $PRODUCT"
  echo "数据库: $DB_NAME"
  echo "节点: $HOSTNAME"
  
  # 检查MySQL服务是否运行
  if ! mysqladmin ping -h"localhost" -u"root" -p"$ROOT_PASSWORD" --silent; then
    echo "错误: MySQL 服务未运行"
    exit 1
  fi
  
  # 判断恢复模式
  if [ -z "$1" ]; then
    # 交互式恢复
    interactive_restore
  else
    # 指定备份文件恢复
    specified_restore "$1"
  fi
  
  local restore_result=$?
  
  echo "===== MySQL 恢复脚本执行完成 ====="
  exit $restore_result
}

# 运行主函数
main "$@" 