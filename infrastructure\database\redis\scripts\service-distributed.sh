#!/bin/bash
#
# Redis 分布式服务管理脚本
# 用于管理不同宿主机上的Redis主从节点
#
# 说明:
# - 脚本中的master/slave参数仅用于找到对应的docker-compose配置文件
# - 实际的Redis角色由Redis实例自身决定，可能与配置不同
# - Redis在故障转移或手动切换时，会发生角色变化 (master变为slave或slave变为master)
# - 本脚本会根据Redis实例当前的实际角色显示信息，而非根据配置角色

# 设置错误时退出
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # 无颜色

# 脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
BASE_DIR="$(dirname "$SCRIPT_DIR")"

# 默认产品名称和节点类型
PRODUCT_NAME=${1:-"cloudkey"}
NODE_TYPE=${2:-"master"}
ACTION=${3:-"status"}

# 检查参数
if [[ "$PRODUCT_NAME" == "--help" || "$PRODUCT_NAME" == "-h" ]]; then
  echo "用法: $0 [产品名称] [配置类型] [action]"
  echo "产品名称: cloudkey, eseal, quicksign (默认: cloudkey)"
  echo "配置类型: (用于定位compose文件)"
  echo "  master - 主节点配置 (默认)"
  echo "  slave  - 从节点配置"
  echo "操作:"
  echo "  start   - 启动Redis服务"
  echo "  stop    - 停止Redis服务"
  echo "  restart - 重启Redis服务"
  echo "  status  - 查看服务状态(默认)"
  echo "  logs    - 查看服务日志"
  exit 0
fi

# 检查节点类型
if [[ "$NODE_TYPE" != "master" && "$NODE_TYPE" != "slave" ]]; then
  echo -e "${RED}错误: 配置类型必须是 'master' 或 'slave'${NC}"
  exit 1
fi

# 检查compose目录和配置文件
COMPOSE_DIR="$BASE_DIR/compose"
if [[ "$NODE_TYPE" == "master" ]]; then
  COMPOSE_FILE="$COMPOSE_DIR/$PRODUCT_NAME-redis-master.yml"
else
  COMPOSE_FILE="$COMPOSE_DIR/$PRODUCT_NAME-redis-slave.yml"
fi

# 注意: NODE_TYPE 参数仅用于定位docker compose文件
# 实际的角色和主从状态会在运行时根据Redis实例的实际状态进行检测

if [ ! -f "$COMPOSE_FILE" ]; then
  echo -e "${RED}错误: 找不到compose文件 $COMPOSE_FILE${NC}"
  echo -e "${YELLOW}请先运行部署脚本: ${GREEN}$SCRIPT_DIR/deploy-distributed.sh $PRODUCT_NAME $NODE_TYPE${NC}"
  exit 1
fi

# 根据产品名称设置端口
case $PRODUCT_NAME in
  "cloudkey")
    REDIS_PORT=16379
    ;;
  "eseal")
    REDIS_PORT=16389
    ;;
  "quicksign")
    REDIS_PORT=16399
    ;;
  *)
    REDIS_PORT=16379
    ;;
esac

# 设置环境变量
export PRODUCT_NAME=$PRODUCT_NAME
export REDIS_PORT=$REDIS_PORT
if [[ "$NODE_TYPE" == "master" ]]; then
  CONTAINER_NAME="$PRODUCT_NAME-redis-master"
else
  CONTAINER_NAME="$PRODUCT_NAME-redis-slave"
fi

# 如果密码文件存在，设置密码环境变量
REDIS_PASSWORD=""
if [ -f "$BASE_DIR/secrets/redis_password.txt" ]; then
  REDIS_PASSWORD=$(cat "$BASE_DIR/secrets/redis_password.txt")
  export REDIS_PASSWORD
fi

# 在文件开头添加 Docker Compose 命令变量
DOCKER_COMPOSE_CMD=""

# 在颜色定义后添加函数
# 确定使用哪个版本的 Docker Compose 命令
determine_docker_compose_cmd() {
    # 首先尝试 Docker Compose V2
    if docker compose version &> /dev/null; then
        DOCKER_COMPOSE_CMD="docker compose"
        return 0
    fi
    
    # 如果 V2 不可用，尝试传统版本
    if command -v docker-compose &> /dev/null; then
        DOCKER_COMPOSE_CMD="docker-compose"
        return 0
    fi
    
    # 如果都不可用
    echo -e "${RED}错误: 未找到可用的 Docker Compose 命令${NC}"
    return 1
}

# 在主要逻辑开始前调用检测函数
if ! determine_docker_compose_cmd; then
    exit 1
fi

# 产品环境变量文件路径
ENV_DIR="$BASE_DIR/env"
PRODUCT_ENV_FILE="$ENV_DIR/$PRODUCT_NAME.env"

# 检查并加载产品特定的环境变量文件
if [ -f "$PRODUCT_ENV_FILE" ]; then
  echo -e "${GREEN}加载产品环境配置: $PRODUCT_ENV_FILE${NC}"
  source "$PRODUCT_ENV_FILE"
else
  echo -e "${YELLOW}警告: 找不到产品环境配置文件: $PRODUCT_ENV_FILE${NC}"
fi

# 函数: 修复目录权限
fix_permissions() {
  local product=$1
  
  echo -e "${YELLOW}检查并修复目录权限...${NC}"
  
  # 检查目录是否存在
  local log_dir="$BASE_DIR/logs/$product"
  local data_dir="$BASE_DIR/data/$product"
  
  if [ -d "$log_dir" ]; then
    echo -e "${GREEN}设置日志目录权限: $log_dir${NC}"
    chown -R 999:999 "$log_dir"
    chmod -R 755 "$log_dir"
  else 
    echo -e "${RED}错误: 日志目录不存在: $log_dir${NC}"
    mkdir -p "$log_dir/master" "$log_dir/slave"
    chown -R 999:999 "$log_dir"
    chmod -R 755 "$log_dir"
  fi
  
  if [ -d "$data_dir" ]; then
    echo -e "${GREEN}设置数据目录权限: $data_dir${NC}"
    chown -R 999:999 "$data_dir"
    chmod -R 755 "$data_dir"
  else
    echo -e "${RED}错误: 数据目录不存在: $data_dir${NC}"
    mkdir -p "$data_dir/master" "$data_dir/slave"
    chown -R 999:999 "$data_dir"
    chmod -R 755 "$data_dir"
  fi
  
  echo -e "${GREEN}目录权限已修复${NC}"
}

# 函数: 启动Redis服务
start_redis() {
  echo -e "${BLUE}正在启动 $PRODUCT_NAME Redis 服务 (配置类型: $NODE_TYPE)...${NC}"
  
  # 首先检查服务是否已经在运行
  if docker ps | grep -q "$CONTAINER_NAME"; then
    echo -e "${YELLOW}提示: $CONTAINER_NAME 已经在运行中，无需重复启动${NC}"
    echo -e "${GREEN}如需重启服务，请使用: $0 $PRODUCT_NAME $NODE_TYPE restart${NC}"
    return 0
  fi
  
  # 修复权限问题
  fix_permissions "$PRODUCT_NAME"
  
  # 确保已设置密码环境变量，这对于docker-compose命令是必需的
  if [ -f "$BASE_DIR/secrets/redis_password.txt" ]; then
    export REDIS_PASSWORD=$(cat "$BASE_DIR/secrets/redis_password.txt")
    echo -e "${YELLOW}已加载Redis密码${NC}"
  else
    echo -e "${RED}错误: 找不到密码文件 $BASE_DIR/secrets/redis_password.txt${NC}"
    echo -e "${YELLOW}请先运行部署脚本创建密码文件${NC}"
    exit 1
  fi
  
  # 确保设置其他必要的环境变量
  export PRODUCT_NAME=$PRODUCT_NAME
  export REDIS_PORT=$REDIS_PORT
  
  echo -e "${YELLOW}使用配置:${NC}"
  echo -e "  ${YELLOW}产品名称: ${GREEN}$PRODUCT_NAME${NC}"
  echo -e "  ${YELLOW}Redis端口: ${GREEN}$REDIS_PORT${NC}"
  echo -e "  ${YELLOW}配置文件: ${GREEN}$COMPOSE_FILE${NC}"
  echo -e "  ${YELLOW}环境文件: ${GREEN}$PRODUCT_ENV_FILE${NC}"
  
  $DOCKER_COMPOSE_CMD --env-file "$PRODUCT_ENV_FILE" -f "$COMPOSE_FILE" up -d
  
  # 等待服务启动
  echo -e "${YELLOW}等待Redis服务启动...${NC}"
  sleep 3
  
  # 检查服务状态
  if docker ps | grep -q "$CONTAINER_NAME"; then
    echo -e "${GREEN}Redis 服务已成功启动!${NC}"
  else
    echo -e "${RED}Redis 服务启动异常，请检查日志${NC}"
    $DOCKER_COMPOSE_CMD --env-file "$PRODUCT_ENV_FILE" -f "$COMPOSE_FILE" logs
    exit 1
  fi
}

# 函数: 停止Redis服务
stop_redis() {
  echo -e "${BLUE}正在停止 $PRODUCT_NAME Redis 服务 (配置类型: $NODE_TYPE)...${NC}"
  
  # 确保已设置密码环境变量，这对于docker-compose命令是必需的
  if [ -f "$BASE_DIR/secrets/redis_password.txt" ]; then
    export REDIS_PASSWORD=$(cat "$BASE_DIR/secrets/redis_password.txt")
  fi
  
  # 确保设置其他必要的环境变量
  export PRODUCT_NAME=$PRODUCT_NAME
  export REDIS_PORT=$REDIS_PORT
  
  $DOCKER_COMPOSE_CMD --env-file "$PRODUCT_ENV_FILE" -f "$COMPOSE_FILE" down
  
  echo -e "${GREEN}Redis 服务已停止${NC}"
}

# 函数: 重启Redis服务
restart_redis() {
  echo -e "${BLUE}正在重启 $PRODUCT_NAME Redis 服务 (配置类型: $NODE_TYPE)...${NC}"
  
  stop_redis
  sleep 2
  
  # 修复权限问题
  fix_permissions "$PRODUCT_NAME"
  
  start_redis
}

# 函数: 查看服务状态
status_redis() {
  echo -e "${BLUE}$PRODUCT_NAME Redis 节点状态 (配置类型: $NODE_TYPE):${NC}"
  
  # 显示密码相关信息（仅在status时显示）
  if [ -f "$BASE_DIR/secrets/redis_password.txt" ]; then
    echo -e "${YELLOW}DEBUG: 正在使用密码文件 $BASE_DIR/secrets/redis_password.txt${NC}"
    echo -e "${YELLOW}DEBUG: 密码前10个字符: ${NC}$(echo "$REDIS_PASSWORD" | cut -c1-10)..."
    echo -e "${YELLOW}DEBUG: 密码长度: ${NC}${#REDIS_PASSWORD} 字符"
    
    # 检查密码是否包含特殊字符
    if [[ "$REDIS_PASSWORD" =~ [^a-zA-Z0-9] ]]; then
      echo -e "${YELLOW}DEBUG: 警告 - 密码包含特殊字符，可能需要转义${NC}"
    fi
  fi
  
  $DOCKER_COMPOSE_CMD --env-file "$PRODUCT_ENV_FILE" -f "$COMPOSE_FILE" ps
  
  # 检查节点连接状态
  echo
  echo -e "${BLUE}节点连接测试:${NC}"
  if docker ps | grep -q "$CONTAINER_NAME"; then
    # 检查Redis连接
    if [ -z "$REDIS_PASSWORD" ]; then
      # 无密码连接测试
      RESULT=$(docker exec -i "$CONTAINER_NAME" redis-cli ping 2>&1)
      if echo "$RESULT" | grep -q "PONG"; then
        echo -e "${GREEN}Redis连接正常 (无密码)${NC}"
      else
        echo -e "${RED}Redis连接失败 (无密码)${NC}"
        echo "错误: $RESULT"
      fi
    else
      # 有密码连接测试
      RESULT=$(docker exec -i "$CONTAINER_NAME" redis-cli -a "$REDIS_PASSWORD" ping 2>&1)
      if echo "$RESULT" | grep -q "PONG"; then
        echo -e "${GREEN}Redis连接正常${NC}"
      else
        echo -e "${RED}Redis连接失败${NC}"
        echo "错误: $RESULT"
        echo -e "${YELLOW}尝试无密码连接...${NC}"
        NOPW_RESULT=$(docker exec -i "$CONTAINER_NAME" redis-cli ping 2>&1)
        if echo "$NOPW_RESULT" | grep -q "PONG"; then
          echo -e "${GREEN}无密码连接成功${NC}"
        else
          echo -e "${RED}所有连接方法均失败${NC}"
        fi
      fi
    fi
    
    # 获取复制信息 (对主节点和从节点都进行获取)
    echo
    echo -e "${BLUE}节点角色和复制状态:${NC}"
    # 根据前面的测试结果决定是否使用密码
    REPL_CMD=""
    if [ -z "$REDIS_PASSWORD" ] || echo "$NOPW_RESULT" | grep -q "PONG"; then
      REPLICATION_INFO=$(docker exec -i "$CONTAINER_NAME" redis-cli info replication 2>&1)
    else
      REPLICATION_INFO=$(docker exec -i "$CONTAINER_NAME" redis-cli -a "$REDIS_PASSWORD" info replication 2>&1)
    fi
    
    # 检查复制状态
    if echo "$REPLICATION_INFO" | grep -q "role:master"; then
      # 主节点信息
      echo -e "${GREEN}节点角色: 主节点${NC}"
      
      # 如果配置为从节点但运行为主节点，显示警告
      if [[ "$NODE_TYPE" == "slave" ]]; then
        echo -e "${YELLOW}注意: 该节点配置为从节点，但当前运行为主节点 (可能由于主从切换)${NC}"
        echo -e "${YELLOW}提示: 这通常是由Redis哨兵或集群进行的自动故障转移导致的，属于正常情况${NC}"
      fi
      
      # 提取从节点数量和复制信息
      SLAVES_COUNT=$(echo "$REPLICATION_INFO" | grep "connected_slaves:" | cut -d ":" -f2 | tr -d '[:space:]')
      ROLE_CHANGE_TIME=$(echo "$REPLICATION_INFO" | grep "master_failover_state:" | cut -d ":" -f2 | tr -d '[:space:]')
      
      # 显示详细信息
      echo -e "  ${BLUE}连接的从节点数: ${GREEN}$SLAVES_COUNT${NC}"
      
      # 显示故障转移状态（如果存在）
      if [ ! -z "$ROLE_CHANGE_TIME" ]; then
        echo -e "  ${BLUE}故障转移状态: ${GREEN}$ROLE_CHANGE_TIME${NC}"
      fi
      
      # 显示从节点信息
      if [ "$SLAVES_COUNT" -gt 0 ]; then
        echo -e "  ${BLUE}从节点列表:${NC}"
        for ((i=0; i<$SLAVES_COUNT; i++)); do
          SLAVE_INFO=$(echo "$REPLICATION_INFO" | grep -E "slave${i}:" | sed 's/^/    /')
          echo -e "    ${GREEN}从节点 $i:${NC} $SLAVE_INFO"
        done
      fi
      
      # 显示更多详细信息
      echo
      echo -e "${BLUE}详细复制信息:${NC}"
      echo "$REPLICATION_INFO" | grep -E "role:|connected_slaves:|master_replid:|master_repl_offset:|repl_backlog_active:|repl_backlog_size:|master_failover_state:|failover_" | sed 's/^/  /'
      
    elif echo "$REPLICATION_INFO" | grep -q "role:slave"; then
      # 从节点信息 - 不论连接状态如何
      echo -e "${GREEN}节点角色: 从节点${NC}"
      
      # 如果配置为主节点但运行为从节点，显示警告
      if [[ "$NODE_TYPE" == "master" ]]; then
        echo -e "${YELLOW}注意: 该节点配置为主节点，但当前运行为从节点 (可能由于主从切换)${NC}"
      fi
      
      # 获取主节点连接状态
      MASTER_LINK_STATUS=$(echo "$REPLICATION_INFO" | grep "master_link_status:" | cut -d ":" -f2 | tr -d '[:space:]')
      
      # 显示主节点信息，无论连接状态如何
      echo -e "  ${BLUE}主节点信息:${NC}"
      echo "$REPLICATION_INFO" | grep -E "master_host:|master_port:" | sed 's/^/    /'
      
      # 根据连接状态区分显示
      if [ "$MASTER_LINK_STATUS" == "up" ]; then
        echo -e "${GREEN}主从复制状态: 正常连接${NC}"
        echo -e "    ${GREEN}master_link_status: up${NC}"
      else
        # 这是主从切换过程中的正常状态
        echo -e "${BLUE}主从复制状态: 切换中${NC}"
        echo -e "    ${BLUE}master_link_status: down${NC}"
        
        # 显示连接中断时长
        DOWN_SECONDS=$(echo "$REPLICATION_INFO" | grep "master_link_down_since_seconds:" | cut -d ":" -f2 | tr -d '[:space:]')
        if [ ! -z "$DOWN_SECONDS" ] && [ "$DOWN_SECONDS" != "-1" ]; then
          echo -e "    ${BLUE}切换进行时长: ${DOWN_SECONDS} 秒${NC}"
        else
          echo -e "    ${BLUE}切换时长: 未知${NC}"
        fi
        
        # 添加帮助信息
        echo -e "${YELLOW}提示: 当前节点处于主从切换状态，这是Redis高可用机制的正常现象${NC}"
        echo -e "${YELLOW}      如需手动配置主从关系: redis-cli -h <主机名> -p <端口> slaveof <新主节点IP> <新主节点端口>${NC}"
      fi
      
      # 显示更多详细信息
      echo
      echo -e "${BLUE}详细复制信息:${NC}"
      echo "$REPLICATION_INFO" | grep -E "role:|master_host:|master_port:|master_link_status:|slave_repl_offset:|slave_priority:|master_link_down_since_seconds:" | sed 's/^/  /'
    
    else
      # 角色未知或异常
      echo -e "${RED}无法确定节点角色${NC}"
      echo "$REPLICATION_INFO" | grep -E "role:" | sed 's/^/  /'
    fi
  else
    echo -e "${RED}节点未运行${NC}"
  fi
}

# 函数: 查看日志
view_logs() {
  echo -e "${BLUE}查看 $PRODUCT_NAME Redis 服务日志 (配置类型: $NODE_TYPE):${NC}"
  
  $DOCKER_COMPOSE_CMD --env-file "$PRODUCT_ENV_FILE" -f "$COMPOSE_FILE" logs --tail=100 -f
}

# 主函数
case $ACTION in
  "start")
    start_redis
    ;;
  "stop")
    stop_redis
    ;;
  "restart")
    restart_redis
    ;;
  "status")
    # status操作不需要在错误时退出
    set +e
    status_redis
    exit_code=$?
    set -e
    exit $exit_code
    ;;
  "logs")
    # logs操作不需要在错误时退出
    set +e
    view_logs
    exit_code=$?
    set -e
    exit $exit_code
    ;;
  *)
    echo -e "${RED}错误: 未知操作 '$ACTION'${NC}"
    echo -e "${YELLOW}可用操作: start, stop, restart, status, logs${NC}"
    exit 1
    ;;
esac 