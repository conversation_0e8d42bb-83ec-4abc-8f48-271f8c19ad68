-- MySQL dump 10.13  Distrib 8.0.36, for Linux (x86_64)
--
-- Host: localhost    Database: cloudkeyTest
-- ------------------------------------------------------
-- Server version	8.0.36

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;


CREATE DATABASE IF NOT EXISTS cloudkey_archive  DEFAULT CHARACTER SET utf8mb4 COLLATE=utf8mb4_general_ci;
CREATE DATABASE IF NOT EXISTS cloudkey  DEFAULT CHARACTER SET utf8mb4 COLLATE=utf8mb4_general_ci;
-- 创建数据库用户 cloudkey
CREATE USER IF NOT EXISTS cloudkey@'%' IDENTIFIED BY 'Netca@2006';
GRANT ALL PRIVILEGES ON cloudkey.* TO cloudkey@'%';
GRANT ALL PRIVILEGES ON cloudkey_archive.* TO cloudkey@'%';
GRANT FILE ON *.* TO cloudkey@'%';
FLUSH PRIVILEGES;

use cloudkey;


--
-- Table structure for table `audit_operator_log`
--

DROP TABLE IF EXISTS `audit_operator_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `audit_operator_log` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `operator_id` int NOT NULL,
  `client_ip_addr` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `client_ip_port` int DEFAULT NULL,
  `request_uri` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `request_description` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `request_signature` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `request_signature_original` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `request_content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `request_start` datetime DEFAULT NULL,
  `request_end` datetime DEFAULT NULL,
  `response_code` int DEFAULT NULL,
  `response_content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `memo` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `gmt_audit` datetime DEFAULT NULL,
  `audit_id` int DEFAULT NULL COMMENT '审计员的id',
  `audit_status` int NOT NULL DEFAULT '0' COMMENT '0可审计;1已审计;2无需审计',
  `district_id` int NOT NULL DEFAULT '1' COMMENT '院区ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `audit_operator_log`
--

LOCK TABLES `audit_operator_log` WRITE;
/*!40000 ALTER TABLE `audit_operator_log` DISABLE KEYS */;
/*!40000 ALTER TABLE `audit_operator_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `authority_operator`
--

DROP TABLE IF EXISTS `authority_operator`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `authority_operator` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `identity_type` int NOT NULL COMMENT '证件类型',
  `identity` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '证件号码',
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `email` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `phone` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `address` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `memo` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `type` int NOT NULL COMMENT '管理员类型',
  `project_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '[]' COMMENT '关联的项目id，JSON表示[1,2,3,4]',
  `cert_sn` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `cert_thumbprint` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `cert_thumbprint_aglo` int NOT NULL COMMENT 'NetcaJCrpyto中间件HASH算法',
  `sign_cert` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '签名证书',
  `validity_start` datetime DEFAULT NULL,
  `validity_end` datetime DEFAULT NULL,
  `status` tinyint unsigned NOT NULL,
  `gmt_create` datetime NOT NULL,
  `gmt_modified` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;


--
-- Table structure for table `authority_operator_role_mapping`
--

DROP TABLE IF EXISTS `authority_operator_role_mapping`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `authority_operator_role_mapping` (
  `id` int NOT NULL AUTO_INCREMENT,
  `operator_id` int NOT NULL COMMENT '操作员id',
  `role_id` int NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='操作员-角色关系表';
/*!40101 SET character_set_client = @saved_cs_client */;


--
-- Table structure for table `authority_permission`
--

DROP TABLE IF EXISTS `authority_permission`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `authority_permission` (
  `id` int NOT NULL AUTO_INCREMENT,
  `permission_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '权限代码',
  `permission_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '权限名称',
  `parent_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '父级权限code',
  `description` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '权限描述',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=197 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='权限信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `authority_permission`
--

LOCK TABLES `authority_permission` WRITE;
/*!40000 ALTER TABLE `authority_permission` DISABLE KEYS */;
INSERT INTO `authority_permission` VALUES (1,'userManager','用户管理',NULL,NULL),(2,'sys:org','机构管理','sys',NULL),(3,'sys:org:search','机构管理 查询','sys:org',NULL),(4,'sys:org:insert','机构管理 添加','sys:org',NULL),(5,'sys:org:modify','机构管理 修改','sys:org',NULL),(6,'sys:org:info','机构管理 详情','sys:org',NULL),(7,'userManager:certUser','证书用户管理','userManager',NULL),(8,'userManager:certUser:search','证书用户管理 查询','userManager:certUser',NULL),(9,'userManager:certUser:insert','证书用户管理 添加','userManager:certUser',NULL),(10,'userManager:certUser:info','证书用户管理 详情','userManager:certUser',NULL),(11,'userManager:certUser:apply','证书用户管理 申请','userManager:certUser',NULL),(12,'userManager:certUser:delete','证书用户管理 删除','userManager:certUser',NULL),(13,'userManager:certUser:modify','证书用户管理 修改','userManager:certUser',NULL),(14,'userManager:business','业务管理','userManager',NULL),(15,'userManager:business:search','业务管理 查询','userManager:business',NULL),(16,'userManager:business:info','业务管理 详情','userManager:business',NULL),(17,'userManager:business:process','业务管理 进度查询','userManager:business',NULL),(18,'userManager:cert','证书管理','userManager',NULL),(19,'userManager:cert:search','证书管理 查询','userManager:cert',NULL),(20,'userManager:cert:updatePic','证书管理 上传签章','userManager:cert',NULL),(21,'userManager:cert:updateCert','证书管理 证书更新','userManager:cert',NULL),(22,'userManager:cert:unlock','证书管理 解锁','userManager:cert',NULL),(23,'userManager:cert:renewal','证书管理 续期','userManager:cert',NULL),(24,'userManager:cert:cancelation','证书管理 注销','userManager:cert',NULL),(25,'userManager:cert:authority','证书管理 授权','userManager:cert',NULL),(26,'userManager:cert:authorityNotice','证书管理 授权通知','userManager:cert',NULL),(27,'userManager:cert:sealPic','证书管理 签章管理','userManager:cert',NULL),(28,'userManager:cert:info','证书管理 证书信息','userManager:cert',NULL),(29,'userManager:cert:reviewPic','证书管理 签章审核','userManager:cert',NULL),(30,'userManager:authUser','授权用户管理','userManager',NULL),(31,'userManager:authUser:search','授权用户管理 查询','userManager:authUser',NULL),(32,'userManager:authUser:edit','授权用户管理 编辑','userManager:authUser',NULL),(33,'userManager:authUser:delete','授权用户管理 删除','userManager:authUser',NULL),(34,'userManager:authUser:info','授权用户管理 详情','userManager:authUser','delete'),(35,'userManager:approve','审核管理','userManager',NULL),(36,'userManager:approve:search','审核管理 查询','userManager:approve',NULL),(37,'userManager:approve:level1','审核管理 一级审核','userManager:approve',NULL),(38,'userManager:approve:level2','审核管理 二级审核','userManager:approve',NULL),(39,'userManager:certUser:export','证书用户管理 导出','userManager:certUser',NULL),(40,'userManager:userStatistics','用户统计管理','userManager',NULL),(41,'userManager:userStatistics:search','用户统计管理 查询','userManager:userStatistics',NULL),(42,'userManager:userStatistics:export','用户统计管理 导出','userManager:userStatistics',NULL),(43,'sys','系统管理',NULL,NULL),(44,'sys:project','项目配置','sys',NULL),(45,'sys:project:insert','项目配置 添加','sys:project',NULL),(46,'sys:project:info','项目配置 详情','sys:project',NULL),(47,'sys:project:modify','项目配置 修改','sys:project',NULL),(48,'sys:project:delete','项目配置 删除','sys:project',NULL),(49,'sys:project:search','项目配置 查询','sys:project',NULL),(50,'sys:department','部门管理','sys',NULL),(51,'sys:department:search','部门管理 查询','sys:department',NULL),(52,'sys:department:insert','部门管理 添加','sys:department',NULL),(53,'sys:department:info','部门管理 详情','sys:department',NULL),(54,'sys:department:modify','部门管理 修改','sys:department',NULL),(55,'sys:department:delete','部门管理 删除','sys:department',NULL),(56,'sys:linkman','经办人管理','sys',NULL),(57,'sys:linkman:search','经办人管理 查询','sys:linkman',NULL),(58,'sys:linkman:insert','经办人管理 添加','sys:linkman',NULL),(59,'sys:linkman:info','经办人管理 详情','sys:linkman',NULL),(60,'sys:linkman:modify','经办人管理 修改','sys:linkman',NULL),(61,'sys:linkman:frozen','经办人管理 冻结/解冻','sys:linkman',NULL),(62,'sys:loginRule','登陆规则管理','sys',NULL),(63,'sys:loginRule:search','登陆规则管理 查询','sys:loginRule',NULL),(64,'sys:loginRule:info','登陆规则管理 详情','sys:loginRule',NULL),(65,'sys:loginRule:modify','登陆规则管理 修改','sys:loginRule',NULL),(66,'sys:loginRule:insert','登陆规则管理 添加','sys:loginRule',NULL),(67,'sys:pinRule','口令规则管理','sys',NULL),(68,'sys:pinRule:search','口令规则管理 查询','sys:pinRule',NULL),(69,'sys:pinRule:insert','口令规则管理 添加','sys:pinRule',NULL),(70,'sys:pinRule:info','口令规则管理 详情','sys:pinRule',NULL),(71,'sys:pinRule:modify','口令规则管理 修改','sys:pinRule',NULL),(72,'sys:application','应用管理','sys',NULL),(73,'sys:application:insert','应用管理 添加','sys:application',NULL),(74,'sys:application:search','应用管理 查询','sys:application',NULL),(75,'sys:application:info','应用管理 详情','sys:application',NULL),(76,'sys:application:modify','应用管理 修改','sys:application',NULL),(77,'sys:sysConfig','系统配置','sys',NULL),(78,'sys:sysConfig:search','系统配置 查询','sys:sysConfig',NULL),(79,'sys:sysConfig:info','系统配置 详情','sys:sysConfig',NULL),(80,'sys:sysConfig:modify','系统配置 修改','sys:sysConfig',NULL),(81,'sys:sysConfig:refresh','系统配置 刷新','sys:sysConfig',NULL),(82,'sys:softwareUpdate','软件更新配置','sys',NULL),(83,'sys:softwareUpdate:search','软件更新配置 查询','sys:softwareUpdate',NULL),(84,'sys:softwareUpdate:info','软件更新配置 详情','sys:softwareUpdate',NULL),(85,'sys:softwareUpdate:modify','软件更新配置 修改','sys:softwareUpdate',NULL),(86,'sys:thirdSys','第三方系统配置','sys',NULL),(87,'sys:thirdSys:search','第三方系统配置 查询','sys:thirdSys',NULL),(88,'sys:thirdSys:info','第三方系统配置 详情','sys:thirdSys',NULL),(89,'sys:thirdSys:modify','第三方系统配置 修改','sys:thirdSys',NULL),(90,'sys:thirdSys:insert','第三方系统配置 添加','sys:thirdSys',NULL),(91,'sys:service','服务配置','sys',NULL),(92,'sys:service:insert','服务配置 添加','sys:service',NULL),(93,'sys:service:info','服务配置 详情','sys:service',NULL),(94,'sys:service:modify','服务配置 修改','sys:service',NULL),(95,'sys:service:search','服务配置 查询','sys:service',NULL),(96,'sys:service:show','服务配置 优先展示','sys:service',NULL),(97,'sys:service:delete','服务配置 删除','sys:service',NULL),(98,'sys:process','流程配置','sys',NULL),(99,'sys:process:insert','流程配置 添加','sys:process',NULL),(100,'sys:process:search','流程配置 查询','sys:process',NULL),(101,'sys:process:info','流程配置 详情','sys:process',NULL),(102,'sys:process:modify','流程配置 修改','sys:process',NULL),(103,'sys:process:delete','流程配置 删除','sys:process',NULL),(104,'authority','权限管理',NULL,NULL),(105,'authority:operator','系统用户管理','authority',NULL),(106,'authority:operator:search','系统用户管理 查询','authority:operator',NULL),(107,'authority:operator:insert','系统用户管理 添加','authority:operator',NULL),(108,'authority:operator:info','系统用户管理 详情','authority:operator',NULL),(109,'authority:operator:modify','系统用户管理 修改','authority:operator',NULL),(110,'authority:operator:frozen','系统用户管理 冻结/解冻','authority:operator',NULL),(111,'authority:operator:cert','系统用户管理 签名证书','authority:operator',NULL),(112,'authority:operator:authority','系统用户管理 授权','authority:operator',NULL),(113,'authority:role','系统角色','authority',NULL),(114,'authority:role:insert','系统角色 添加角色','authority:role',NULL),(115,'authority:role:search','系统角色 查询','authority:role',NULL),(116,'authority:role:modify','系统角色 修改','authority:role',NULL),(117,'authority:role:info','系统角色 详情','authority:role',NULL),(118,'authority:role:authority','系统角色 授权','authority:role',NULL),(119,'operatorLog','审计日志管理',NULL,NULL),(120,'operatorLog:sysUserLog','日志管理','operatorLog',NULL),(121,'operatorLog:sysUserLog:search','日志管理 查询','operatorLog:sysUserLog',NULL),(122,'operatorLog:sysUserLog:info','日志管理 详情','operatorLog:sysUserLog',NULL),(123,'operatorLog:sysUserLog:audit','日志管理 审计','operatorLog:sysUserLog',NULL),(124,'operatorLog:thirdSystemLog','第三方系统日志管理','operatorLog',NULL),(125,'operatorLog:thirdSystemLog:search','第三方系统日志管理 查询','operatorLog:thirdSystemLog',NULL),(126,'operatorLog:thirdSystemLog:info','第三方系统日志管理 详情','operatorLog:thirdSystemLog',NULL),(127,'operatorLog:auditAppLog','应用日志管理','operatorLog',NULL),(128,'operatorLog:auditAppLog:search','应用日志管理 查询','operatorLog:auditAppLog',NULL),(129,'operatorLog:auditAppLog:info','应用日志管理 详情','operatorLog:auditAppLog',NULL),(130,'userManager:cert:freeze','证书管理 冻结','userManager:cert',NULL),(131,'userManager:cert:unfreeze','证书管理 解冻','userManager:cert',NULL),(132,'userManager:sealSignInfo','签章统计管理','userManager',NULL),(133,'userManager:sealSignInfo:search','签章统计管理 查询','userManager:sealSignInfo',NULL),(134,'userManager:sealSignInfo:export','签章统计管理 导出','userManager:sealSignInfo',NULL),(135,'userManager:cert:reserve','证书管理 预约','userManager:cert',''),(136,'userManager:cert:reserve:revoke','证书管理 预约注销','userManager:cert:reserve',''),(137,'userManager:cert:reserve:cancel','证书管理 取消预约','userManager:cert:reserve',''),(138,'userManager:cert:reserve:cancel:revoke','证书管理 取消预约注销','userManager:cert:reserve:cancel',''),(139,'userManager:cert:batchDownWithProject','选中项目签章批量下载','userManager:cert',''),(140,'operatorLog:smsLog','短信日志管理','operatorLog',NULL),(141,'operatorLog:smsLog:search','短信日志管理 查询','operatorLog:smsLog',NULL),(142,'operatorLog:smsLog:info','短信日志管理 详情','operatorLog:smsLog',NULL),(143,'sys:occupation','职业管理','sys',NULL),(144,'sys:occupation:search','职业管理 查询','sys:occupation',NULL),(145,'sys:occupation:insert','职业管理 添加','sys:occupation',NULL),(146,'sys:occupation:info','职业管理 详情','sys:occupation',NULL),(147,'sys:occupation:modify','职业管理 修改','sys:occupation',NULL),(148,'sys:occupation:delete','职业管理 删除','sys:occupation',NULL),(149,'userManager:noCertUser','非证书用户管理','userManager',NULL),(150,'userManager:noCertUser:search','非证书用户管理 查询','userManager:noCertUser',NULL),(151,'userManager:noCertUser:insert','非证书用户管理 添加','userManager:noCertUser',NULL),(152,'userManager:noCertUser:info','非证书用户管理 详情','userManager:noCertUser',NULL),(153,'userManager:noCertUser:modify','非证书用户管理 修改','userManager:noCertUser',NULL),(154,'userManager:noCertUser:delete','非证书用户管理 删除','userManager:noCertUser',NULL),(155,'userManager:noCertUser:updatePic','非证书用户管理 图片上传','userManager:noCertUser',NULL),(156,'userManager:noCertUser:reviewPic','非证书用户管理 图片审核','userManager:noCertUser',NULL),(157,'sys:district','院区管理','sys',NULL),(158,'sys:district:insert','院区管理 添加','sys:district',NULL),(159,'sys:district:update','院区管理 修改','sys:district',NULL),(160,'sys:district:delete','院区管理 删除','sys:district',NULL),(161,'sys:district:query','院区管理 查询','sys:district',NULL),(162,'sys:certAlertPolicy','证书预警策略管理','sys',NULL),(163,'sys:certAlertPolicy:insert','证书预警策略管理 添加','sys:certAlertPolicy',NULL),(164,'sys:certAlertPolicy:update','证书预警策略管理 修改','sys:certAlertPolicy',NULL),(165,'sys:certAlertPolicy:delete','证书预警策略管理 删除','sys:certAlertPolicy',NULL),(166,'sys:certAlertPolicy:query','证书预警策略管理 查询','sys:certAlertPolicy',NULL),(167,'storageData','数据管理',NULL,NULL),(168,'storageData:signature','数据管理 电子签名数据','storageData',NULL),(169,'storageData:signature:search','数据管理 电子签名数据查询','storageData:signature',NULL),(170,'storageData:signature:info','数据管理 电子签名数据详情','storageData:signature',NULL),(171,'storageData:seal','数据管理 电子签章数据','storageData',NULL),(172,'storageData:seal:search','数据管理 电子签章数据查询','storageData:seal',NULL),(173,'storageData:seal:info','数据管理 电子签章数据详情','storageData:seal',NULL),(174,'storageData:statistic','数据管理 统计','storageData',NULL),(175,'storageData:statistic:search','数据管理 统计查询','storageData:statistic',NULL),(176,'userManager:device','设备管理','userManager',NULL),(177,'userManager:device:delete','设备管理 删除','userManager:device',NULL),(178,'userManager:device:update','设备管理 修改','userManager:device',NULL),(179,'userManager:device:query','设备管理 查询','userManager:device',NULL),(180,'coSignManager','协同签名管理',NULL,NULL),(181,'coSignManager:signTaskManager','协同签名任务管理','coSignManager',NULL),(182,'coSignManager:signTaskManager:search','协同签名任务管理 查询','coSignManager:signTaskManager',NULL),(183,'coSignManager:signTaskManager:info','协同签名任务管理 详情','coSignManager:signTaskManager',NULL),(184,'coSignManager:signTaskManager:push','协同签名任务管理 推送','coSignManager:signTaskManager',NULL),(185,'coSignManager:apiLog','接口日志管理','coSignManager',NULL),(186,'coSignManager:apiLog:search','接口日志管理 查询','coSignManager:apiLog',NULL),(187,'coSignManager:apiLog:info','接口日志管理 详情','coSignManager:apiLog',NULL),(188,'coSignManager:signTaskRecordManager','签名记录管理','coSignManager',NULL),(189,'coSignManager:signTaskRecordManager:search','签名记录管理 查询','coSignManager:signTaskRecordManager',NULL),(190,'coSignManager:signTaskRecordManager:info','签名记录管理 详情','coSignManager:signTaskRecordManager',NULL),(191,'coSignManager:signTaskManager:downloadPdf','协同签名任务管理 下载待签署文件','coSignManager:signTaskManager',NULL),(192,'coSignManager:signTemplateManager','协同签名模板管理','coSignManager',NULL),(193,'coSignManager:signTemplateManager:search','协同签名模板管理 查询','coSignManager:signTemplateManager',NULL),(194,'coSignManager:signTemplateManager:info','协同签名模板管理 详情','coSignManager:signTemplateManager',NULL),(195,'coSignManager:signTemplateManager:edit','协同签名模板管理 更新','coSignManager:signTemplateManager',NULL),(196,'coSignManager:signTaskManager:downloadPreviewFilePdf','下载预览文件','coSignManager:signTaskManager',NULL);
/*!40000 ALTER TABLE `authority_permission` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `authority_role`
--

DROP TABLE IF EXISTS `authority_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `authority_role` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色名称',
  `type` int NOT NULL COMMENT '角色类型',
  `status` int NOT NULL COMMENT '角色状态',
  `description` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '角色描述',
  `gmt_create` datetime NOT NULL COMMENT '创建时间',
  `gmt_modified` datetime DEFAULT NULL COMMENT '最后修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='角色信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `authority_role`
--

LOCK TABLES `authority_role` WRITE;
/*!40000 ALTER TABLE `authority_role` DISABLE KEYS */;
INSERT INTO `authority_role` VALUES (1,'业务操作员',0,0,'操作员','2024-05-23 14:47:59','2024-05-23 14:47:59'),(2,'审计员',1,0,'审计员','2024-05-23 14:47:59','2024-05-23 14:47:59'),(3,'超级管理员',2,0,'超级管理员','2024-05-23 14:47:59','2024-05-23 14:47:59'),(4,'系统管理员',2,0,'系统管理员','2024-05-23 14:47:59','2024-05-23 14:47:59');
/*!40000 ALTER TABLE `authority_role` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `authority_role_permission_mapping`
--

DROP TABLE IF EXISTS `authority_role_permission_mapping`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `authority_role_permission_mapping` (
  `id` int NOT NULL AUTO_INCREMENT,
  `role_id` int NOT NULL COMMENT '角色ID',
  `permission_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '权限id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=403 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='角色-权限关系表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `authority_role_permission_mapping`
--

LOCK TABLES `authority_role_permission_mapping` WRITE;
/*!40000 ALTER TABLE `authority_role_permission_mapping` DISABLE KEYS */;
INSERT INTO `authority_role_permission_mapping` VALUES (179,3,'sys:org:search'),(180,3,'sys:org:insert'),(181,3,'sys:org:modify'),(182,3,'sys:org:info'),(183,3,'sys:project:insert'),(184,3,'sys:project:info'),(185,3,'sys:project:modify'),(186,3,'sys:project:delete'),(187,3,'sys:project:search'),(188,3,'sys:department:search'),(189,3,'sys:department:insert'),(190,3,'sys:department:info'),(191,3,'sys:department:modify'),(192,3,'sys:department:delete'),(193,3,'sys:linkman:search'),(194,3,'sys:linkman:insert'),(195,3,'sys:linkman:info'),(196,3,'sys:linkman:modify'),(197,3,'sys:linkman:frozen'),(198,3,'sys:loginRule:search'),(199,3,'sys:loginRule:info'),(200,3,'sys:loginRule:modify'),(201,3,'sys:loginRule:insert'),(202,3,'sys:pinRule:search'),(203,3,'sys:pinRule:insert'),(204,3,'sys:pinRule:info'),(205,3,'sys:pinRule:modify'),(206,3,'sys:application:insert'),(207,3,'sys:application:search'),(208,3,'sys:application:info'),(209,3,'sys:application:modify'),(210,3,'sys:sysConfig:search'),(211,3,'sys:sysConfig:info'),(212,3,'sys:sysConfig:modify'),(213,3,'sys:sysConfig:refresh'),(214,3,'sys:softwareUpdate:search'),(215,3,'sys:softwareUpdate:info'),(216,3,'sys:softwareUpdate:modify'),(217,3,'sys:thirdSys:search'),(218,3,'sys:thirdSys:info'),(219,3,'sys:thirdSys:modify'),(220,3,'sys:thirdSys:insert'),(221,3,'sys:service:insert'),(222,3,'sys:service:info'),(223,3,'sys:service:modify'),(224,3,'sys:service:search'),(225,3,'sys:service:show'),(226,3,'sys:service:delete'),(227,3,'sys:process:insert'),(228,3,'sys:process:search'),(229,3,'sys:process:info'),(230,3,'sys:process:modify'),(231,3,'sys:process:delete'),(232,3,'sys:occupation:search'),(233,3,'sys:occupation:insert'),(234,3,'sys:occupation:info'),(235,3,'sys:occupation:modify'),(236,3,'sys:occupation:delete'),(237,3,'sys:district:insert'),(238,3,'sys:district:update'),(239,3,'sys:district:delete'),(240,3,'sys:district:query'),(241,3,'sys:certAlertPolicy:insert'),(242,3,'sys:certAlertPolicy:update'),(243,3,'sys:certAlertPolicy:delete'),(244,3,'sys:certAlertPolicy:query'),(245,3,'authority:operator:search'),(246,3,'authority:operator:insert'),(247,3,'authority:operator:info'),(248,3,'authority:operator:modify'),(249,3,'authority:operator:frozen'),(250,3,'authority:operator:cert'),(251,3,'authority:operator:authority'),(252,3,'authority:role:insert'),(253,3,'authority:role:search'),(254,3,'authority:role:modify'),(255,3,'authority:role:info'),(256,3,'authority:role:authority'),(290,2,'operatorLog:sysUserLog:search'),(291,2,'operatorLog:sysUserLog:info'),(292,2,'operatorLog:sysUserLog:audit'),(293,2,'operatorLog:thirdSystemLog:search'),(294,2,'operatorLog:thirdSystemLog:info'),(295,2,'operatorLog:auditAppLog:search'),(296,2,'operatorLog:auditAppLog:info'),(297,2,'operatorLog:smsLog:search'),(298,2,'operatorLog:smsLog:info'),(299,2,'storageData:signature:search'),(300,2,'storageData:signature:info'),(301,2,'storageData:seal:search'),(302,2,'storageData:seal:info'),(303,2,'storageData:statistic:search'),(304,4,'sys:org:search'),(305,4,'sys:org:insert'),(306,4,'sys:org:modify'),(307,4,'sys:org:info'),(308,4,'sys:project:insert'),(309,4,'sys:project:info'),(310,4,'sys:project:modify'),(311,4,'sys:project:delete'),(312,4,'sys:project:search'),(313,4,'sys:department:search'),(314,4,'sys:department:insert'),(315,4,'sys:department:info'),(316,4,'sys:department:modify'),(317,4,'sys:department:delete'),(318,4,'sys:linkman:search'),(319,4,'sys:linkman:insert'),(320,4,'sys:linkman:info'),(321,4,'sys:linkman:modify'),(322,4,'sys:linkman:frozen'),(323,4,'sys:loginRule:search'),(324,4,'sys:loginRule:info'),(325,4,'sys:loginRule:modify'),(326,4,'sys:loginRule:insert'),(327,4,'sys:pinRule:search'),(328,4,'sys:pinRule:insert'),(329,4,'sys:pinRule:info'),(330,4,'sys:pinRule:modify'),(331,4,'sys:application:insert'),(332,4,'sys:application:search'),(333,4,'sys:application:info'),(334,4,'sys:application:modify'),(335,4,'sys:sysConfig:search'),(336,4,'sys:sysConfig:info'),(337,4,'sys:sysConfig:modify'),(338,4,'sys:sysConfig:refresh'),(339,4,'sys:softwareUpdate:search'),(340,4,'sys:softwareUpdate:info'),(341,4,'sys:softwareUpdate:modify'),(342,4,'sys:thirdSys:search'),(343,4,'sys:thirdSys:info'),(344,4,'sys:thirdSys:modify'),(345,4,'sys:thirdSys:insert'),(346,4,'sys:service:insert'),(347,4,'sys:service:info'),(348,4,'sys:service:modify'),(349,4,'sys:service:search'),(350,4,'sys:service:show'),(351,4,'sys:service:delete'),(352,4,'sys:process:insert'),(353,4,'sys:process:search'),(354,4,'sys:process:info'),(355,4,'sys:process:modify'),(356,4,'sys:process:delete'),(357,4,'sys:occupation:search'),(358,4,'sys:occupation:insert'),(359,4,'sys:occupation:info'),(360,4,'sys:occupation:modify'),(361,4,'sys:occupation:delete'),(362,4,'sys:district:insert'),(363,4,'sys:district:update'),(364,4,'sys:district:delete'),(365,4,'sys:district:query'),(366,4,'sys:certAlertPolicy:insert'),(367,4,'sys:certAlertPolicy:update'),(368,4,'sys:certAlertPolicy:delete'),(369,4,'sys:certAlertPolicy:query'),(370,1,'userManager:certUser:search'),(371,1,'userManager:certUser:insert'),(372,1,'userManager:certUser:info'),(373,1,'userManager:certUser:apply'),(374,1,'userManager:certUser:delete'),(375,1,'userManager:certUser:modify'),(376,1,'userManager:certUser:export'),(377,1,'userManager:business:search'),(378,1,'userManager:business:info'),(379,1,'userManager:business:process'),(380,1,'userManager:cert:search'),(381,1,'userManager:cert:updatePic'),(382,1,'userManager:cert:updateCert'),(383,1,'userManager:cert:unlock'),(384,1,'userManager:cert:renewal'),(385,1,'userManager:cert:cancelation'),(386,1,'userManager:cert:authority'),(387,1,'userManager:cert:authorityNotice'),(388,1,'userManager:cert:sealPic'),(389,1,'userManager:cert:info'),(390,1,'userManager:cert:reviewPic'),(391,1,'userManager:cert:freeze'),(392,1,'userManager:cert:unfreeze'),(393,1,'userManager:cert:reserve:revoke'),(394,1,'userManager:cert:reserve:cancel:revoke'),(395,1,'userManager:cert:batchDownWithProject'),(396,1,'userManager:authUser:search'),(397,1,'userManager:authUser:edit'),(398,1,'userManager:authUser:delete'),(399,1,'userManager:authUser:info'),(400,1,'userManager:device:delete'),(401,1,'userManager:device:update'),(402,1,'userManager:device:query');
/*!40000 ALTER TABLE `authority_role_permission_mapping` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `authority_role_type_permission_mapping`
--

DROP TABLE IF EXISTS `authority_role_type_permission_mapping`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `authority_role_type_permission_mapping` (
  `id` int NOT NULL AUTO_INCREMENT,
  `type` int NOT NULL COMMENT '角色类型',
  `permission_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '权限代码',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='角色-权限关系表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `authority_role_type_permission_mapping`
--

LOCK TABLES `authority_role_type_permission_mapping` WRITE;
/*!40000 ALTER TABLE `authority_role_type_permission_mapping` DISABLE KEYS */;
INSERT INTO `authority_role_type_permission_mapping` VALUES (1,0,'userManager'),(2,1,'operatorLog'),(3,2,'sys'),(4,2,'authority'),(5,1,'storageData'),(6,2,'coSignManager');
/*!40000 ALTER TABLE `authority_role_type_permission_mapping` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `authorize_validity`
--

DROP TABLE IF EXISTS `authorize_validity`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `authorize_validity` (
  `id` int NOT NULL AUTO_INCREMENT,
  `valid_start` datetime DEFAULT NULL,
  `valid_end` datetime DEFAULT NULL,
  `valid_count` int NOT NULL DEFAULT '0',
  `usage_count` int NOT NULL DEFAULT '0',
  `status` int NOT NULL,
  `gmt_create` datetime NOT NULL,
  `gmt_modified` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `authorize_validity`
--

LOCK TABLES `authorize_validity` WRITE;
/*!40000 ALTER TABLE `authorize_validity` DISABLE KEYS */;
/*!40000 ALTER TABLE `authorize_validity` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `business_application`
--

DROP TABLE IF EXISTS `business_application`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `business_application` (
  `id` int NOT NULL AUTO_INCREMENT,
  `application_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '应用id',
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '应用名称',
  `status` int NOT NULL COMMENT '状态',
  `gmt_create` datetime NOT NULL,
  `gmt_modified` datetime NOT NULL,
  `district_id` int NOT NULL DEFAULT '1' COMMENT '院区ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `application_id` (`application_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='应用信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `business_application`
--

LOCK TABLES `business_application` WRITE;
/*!40000 ALTER TABLE `business_application` DISABLE KEYS */;
INSERT INTO `business_application` VALUES (1,'NetcaCloudKeyiOS','网证通云密钥iOS客户端',0,'2024-05-23 14:47:42','2024-05-23 14:47:42',1),(2,'NetcaCloudKeyAndroid','网证通云密钥Android客户端',0,'2024-05-23 14:47:42','2024-05-23 14:47:42',1),(3,'eventcertservice','事件证书服务',0,'2025-01-15 15:56:44','2025-01-15 15:56:44',1);
/*!40000 ALTER TABLE `business_application` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `business_approve`
--

DROP TABLE IF EXISTS `business_approve`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `business_approve` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `cert_id` int DEFAULT NULL,
  `cloudkey_project_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `approve_level` tinyint unsigned NOT NULL COMMENT '审核等级',
  `business_type` tinyint unsigned NOT NULL COMMENT '业务类型',
  `present_approve_progress_id` int DEFAULT NULL COMMENT '当前审批进程表id',
  `memo` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注字段',
  `gmt_create` datetime NOT NULL,
  `gmt_modified` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='审批业务表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `business_approve`
--

LOCK TABLES `business_approve` WRITE;
/*!40000 ALTER TABLE `business_approve` DISABLE KEYS */;
/*!40000 ALTER TABLE `business_approve` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `business_approve_progress`
--

DROP TABLE IF EXISTS `business_approve_progress`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `business_approve_progress` (
  `id` int NOT NULL AUTO_INCREMENT,
  `approve_status` tinyint unsigned NOT NULL COMMENT '审核状态',
  `operator_id` int DEFAULT NULL COMMENT '审核人id',
  `approve_id` int NOT NULL COMMENT '审核表id',
  `present_approve_progress` tinyint NOT NULL COMMENT '当前所处审核流程等级',
  `pre_approve_progress_id` int DEFAULT NULL COMMENT '前一个审核进程id',
  `memo` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注字段',
  `gmt_create` datetime NOT NULL,
  `gmt_modified` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='审批进程表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `business_approve_progress`
--

LOCK TABLES `business_approve_progress` WRITE;
/*!40000 ALTER TABLE `business_approve_progress` DISABLE KEYS */;
/*!40000 ALTER TABLE `business_approve_progress` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `business_async_task`
--

DROP TABLE IF EXISTS `business_async_task`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `business_async_task` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `operator_id` int NOT NULL,
  `processSuccess` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '批量任务执行状态',
  `checkFinish` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '批量任务校验状态',
  `running` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '批量任务运行状态',
  `taskFile` blob COMMENT '批量任务文件',
  `memo` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `gmt_create` datetime NOT NULL,
  `gmt_modified` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `business_async_task`
--

LOCK TABLES `business_async_task` WRITE;
/*!40000 ALTER TABLE `business_async_task` DISABLE KEYS */;
/*!40000 ALTER TABLE `business_async_task` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `business_authorized_user`
--

DROP TABLE IF EXISTS `business_authorized_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `business_authorized_user` (
  `id` int NOT NULL AUTO_INCREMENT,
  `cert_id` int DEFAULT NULL COMMENT '关联的，被授权证书的ID',
  `rel_auth_id` int DEFAULT NULL COMMENT '关联 business_authorized_user_info 的 id',
  `authorize_validity_id` int DEFAULT NULL COMMENT '关联authorize_validity的id',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '被授权用户名称',
  `phone` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '被授权用户的手机号',
  `confirm_time` datetime DEFAULT NULL COMMENT '确认授权时间',
  `memo` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注信息',
  `authorize_status` int NOT NULL DEFAULT '0' COMMENT '授权状态：0：录入，1：已授权，2：解除授权',
  `project_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '关联项目id',
  `locked` int NOT NULL COMMENT '表示授权用户是否被锁定。1：被锁定；0：未被锁。',
  `keypair_sign` blob COMMENT '加密后的签名密钥对',
  `signkeypair_enc_version` tinyint unsigned NOT NULL COMMENT '签名密钥对加密版本',
  `keypair_enc` blob COMMENT '加密后的加密密钥对',
  `enckeypair_enc_version` tinyint unsigned NOT NULL COMMENT '加密密钥对加密版本',
  `user_face_info_id` int DEFAULT NULL COMMENT '用户刷脸+口令登录的刷脸数据id',
  `keypair_info_enc_parameter_id` int DEFAULT NULL,
  `gmt_create` datetime NOT NULL,
  `gmt_modified` datetime NOT NULL,
  `keypair_enc_0` blob COMMENT '备份旧数据加密密钥对的冗余字段',
  `keypair_sign_0` blob COMMENT '备份旧数据签名密钥对的冗余字段',
  `last_pin_change` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'PIN码最后修改时间',
  `apply_user_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '授权用户申请的申请人ID（发出申请的用户）',
  `apply_user_type` tinyint DEFAULT NULL COMMENT '授权用户申请的申请人类型，0：证书用户，1：授权用户。',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='被授权用户表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `business_authorized_user`
--

LOCK TABLES `business_authorized_user` WRITE;
/*!40000 ALTER TABLE `business_authorized_user` DISABLE KEYS */;
/*!40000 ALTER TABLE `business_authorized_user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `business_authorized_user_info`
--

DROP TABLE IF EXISTS `business_authorized_user_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `business_authorized_user_info` (
  `id` int NOT NULL AUTO_INCREMENT,
  `uid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户工号/唯一标识',
  `project_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目id',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户名称',
  `phone` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户手机号',
  `memo` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注信息',
  `gmt_create` datetime NOT NULL,
  `gmt_modified` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='带有工号（唯一标识）的授权用户';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `business_authorized_user_info`
--

LOCK TABLES `business_authorized_user_info` WRITE;
/*!40000 ALTER TABLE `business_authorized_user_info` DISABLE KEYS */;
/*!40000 ALTER TABLE `business_authorized_user_info` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `business_cert`
--

DROP TABLE IF EXISTS `business_cert`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `business_cert` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `user_face_info_id` int DEFAULT NULL COMMENT '用户刷脸+口令登录的刷脸数据id',
  `sign_cert_sn` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '签名证书序列号',
  `enc_cert_sn` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '加密证书序列号',
  `type` int NOT NULL COMMENT '证书类型-机构还是员工或者个人',
  `locked` int DEFAULT NULL COMMENT '表示证书是否被锁定。1：被锁定；0：未被锁。如果登录失败次数大于rule表规定的次数，则锁定证书。',
  `project_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `p10` blob,
  `validity_end` datetime DEFAULT NULL,
  `validity_start` datetime DEFAULT NULL,
  `gmt_create` datetime NOT NULL,
  `gmt_modified` datetime NOT NULL,
  `status` int DEFAULT NULL,
  `pre_cert_id` int DEFAULT NULL,
  `memo` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '证书备注字段',
  PRIMARY KEY (`id`),
  KEY `idx_cert_sn` (`sign_cert_sn`) USING BTREE,
  KEY `user_id` (`user_id`),
  KEY `pre_cert_id` (`pre_cert_id`),
  KEY `idx_status_gmtModified` (`status`,`gmt_modified`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `business_cert`
--

LOCK TABLES `business_cert` WRITE;
/*!40000 ALTER TABLE `business_cert` DISABLE KEYS */;
/*!40000 ALTER TABLE `business_cert` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `business_cert_attribute`
--

DROP TABLE IF EXISTS `business_cert_attribute`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `business_cert_attribute` (
  `id` int NOT NULL AUTO_INCREMENT,
  `cert_id` int NOT NULL,
  `user_id` int NOT NULL,
  `cert_usage` int DEFAULT NULL COMMENT '证书类别',
  `cert_sn` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `o` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `ou` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `cn` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `cert_content` blob,
  `validity_start` datetime NOT NULL,
  `validity_end` datetime NOT NULL,
  `pre_cert_sn` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `cert_thumbprint` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `cert_thumbprint_algo` int DEFAULT NULL COMMENT '微缩图算法',
  `gmt_create` datetime NOT NULL,
  `gmt_modified` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_cert_id` (`cert_id`) USING BTREE,
  KEY `idx_cert_sn` (`cert_sn`),
  KEY `business_cert_attribute_cert_id_IDX` (`cert_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `business_cert_attribute`
--

LOCK TABLES `business_cert_attribute` WRITE;
/*!40000 ALTER TABLE `business_cert_attribute` DISABLE KEYS */;
/*!40000 ALTER TABLE `business_cert_attribute` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `business_cert_pin_error_info`
--

DROP TABLE IF EXISTS `business_cert_pin_error_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `business_cert_pin_error_info` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int DEFAULT NULL,
  `cert_id` int NOT NULL,
  `error_count` int NOT NULL,
  `first_login_time` datetime NOT NULL,
  `user_type` int NOT NULL COMMENT '0为证书用户，1位证书授权用户',
  `gmt_create` datetime NOT NULL,
  `gmt_modified` datetime NOT NULL,
  `authorize_user_id` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `business_cert_pin_error_info`
--

LOCK TABLES `business_cert_pin_error_info` WRITE;
/*!40000 ALTER TABLE `business_cert_pin_error_info` DISABLE KEYS */;
/*!40000 ALTER TABLE `business_cert_pin_error_info` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `business_coord_sign_content`
--

DROP TABLE IF EXISTS `business_coord_sign_content`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `business_coord_sign_content` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `task_id` bigint NOT NULL COMMENT '任务ID',
  `content_type` tinyint NOT NULL DEFAULT '0' COMMENT '内容类型：0-文本内容，1-文件',
  `text_content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '文本内容',
  `original_file_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '原文件名称',
  `file_base64` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '文件base64',
  `signature` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '签名信息',
  `pre_file_base64` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '前一次文件base64',
  `pre_signature` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '签名信息',
  `content_timestamp_token` blob COMMENT '内容时间戳token',
  `other_detail_info` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '其他详细内容',
  `can_withdraw_uid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '可撤回的用户Uid',
  `creator_id` bigint DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `updater` bigint DEFAULT NULL COMMENT '修改者ID',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除：0-否，1-是',
  `preview_file_name` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '用于预览的文件名称',
  `preview_file_base64` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '用于预览的文件(base64)',
  PRIMARY KEY (`id`),
  KEY `idx_bus_coord_sign_content_task_id` (`task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='签署任务内容表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `business_coord_sign_content`
--

LOCK TABLES `business_coord_sign_content` WRITE;
/*!40000 ALTER TABLE `business_coord_sign_content` DISABLE KEYS */;
/*!40000 ALTER TABLE `business_coord_sign_content` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `business_coord_sign_task`
--

DROP TABLE IF EXISTS `business_coord_sign_task`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `business_coord_sign_task` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `task_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务编号',
  `third_system_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '第三方系统编号',
  `third_app_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '第三方应用编号',
  `title` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标题',
  `expire_time` datetime NOT NULL COMMENT '任务截止时间',
  `task_status` tinyint NOT NULL DEFAULT '-1' COMMENT '任务状态：-1：初始状态(不可以签名)，0-未签名，1-进行中，2-已拒绝，3-已过期，4-已取消，5-已完成',
  `segment_num` int DEFAULT '0' COMMENT '环节数量',
  `cur_segment_id` bigint DEFAULT NULL COMMENT '当前处理环节ID',
  `sign_type` tinyint NOT NULL DEFAULT '0' COMMENT '签名类型：0-P7签名，1-电子签章',
  `project_uid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'project_uid',
  `sign_option` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '签名配置json',
  `patient_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '患者标识',
  `patient_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '患者姓名',
  `third_order_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '第三方业务流水号',
  `item_index` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '存证结果ID',
  `push_result` tinyint NOT NULL DEFAULT '0' COMMENT '推送状态：0-未推送，1-已推送，2-推送失败',
  `push_result_msg` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '推送结果描述',
  `push_time` datetime DEFAULT NULL COMMENT '推送时间',
  `starter_uid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '发起者UID',
  `starter_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '发起者名称（冗余）',
  `creator_id` bigint DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `updater` bigint DEFAULT NULL COMMENT '修改者ID',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除：0-否，1-是',
  `bus_category` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务分类',
  `start_time` datetime DEFAULT NULL COMMENT '发起时间',
  PRIMARY KEY (`id`),
  KEY `idx_bus_coord_sign_task_third_system_code` (`third_system_code`),
  KEY `idx_bus_coord_sign_task_patient_id_patient_name` (`patient_id`,`patient_name`),
  KEY `idx_bus_coord_sign_task_task_code` (`task_code`),
  KEY `idx_bus_coord_sign_task_task_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='签署任务表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `business_coord_sign_task`
--

LOCK TABLES `business_coord_sign_task` WRITE;
/*!40000 ALTER TABLE `business_coord_sign_task` DISABLE KEYS */;
/*!40000 ALTER TABLE `business_coord_sign_task` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `business_coord_sign_task_segment`
--

DROP TABLE IF EXISTS `business_coord_sign_task_segment`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `business_coord_sign_task_segment` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `task_id` bigint NOT NULL COMMENT '任务ID',
  `segment_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '环节名称',
  `segment_seq` int NOT NULL DEFAULT '1' COMMENT '环节序号',
  `signer_num` int NOT NULL DEFAULT '0' COMMENT '总签署者人数',
  `limit_num` int NOT NULL DEFAULT '1' COMMENT '门限值',
  `segment_status` tinyint NOT NULL DEFAULT '0' COMMENT '环节状态：0-未签名，1-进行中，2-已拒绝，3-已过期，4-已撤回，5-已完成',
  `push_result` tinyint NOT NULL DEFAULT '0' COMMENT '推送状态：0-未推送，1-已推送，2-推送失败',
  `push_result_msg` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '推送结果描述',
  `creator_id` bigint DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `pre_segment_id` bigint DEFAULT NULL COMMENT '前一环节ID',
  `updater` bigint DEFAULT NULL COMMENT '修改者ID',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除：0-否，1-是',
  PRIMARY KEY (`id`),
  KEY `idx_bus_coord_sign_task_segment_task_id` (`task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='签署任务环节表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `business_coord_sign_task_segment`
--

LOCK TABLES `business_coord_sign_task_segment` WRITE;
/*!40000 ALTER TABLE `business_coord_sign_task_segment` DISABLE KEYS */;
/*!40000 ALTER TABLE `business_coord_sign_task_segment` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `business_coord_sign_task_segment_record`
--

DROP TABLE IF EXISTS `business_coord_sign_task_segment_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `business_coord_sign_task_segment_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `task_id` bigint NOT NULL COMMENT '任务ID',
  `segment_id` bigint NOT NULL COMMENT '任务环节ID',
  `signer_id` bigint DEFAULT NULL COMMENT '签署者ID（对应business_coord_sign_task_segment_signer表主键）',
  `signer_uid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '签署者UID',
  `signer_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '签署者名称',
  `sign_status` tinyint NOT NULL DEFAULT '0' COMMENT '签署状态：0-未签名，2-已拒绝，4-已撤回，5-已完成',
  `sign_opinion` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '签名意见',
  `opinion_signature` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '签名意见签名值',
  `creator_id` bigint DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `updater` bigint DEFAULT NULL COMMENT '修改者ID',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除：0-否，1-是',
  PRIMARY KEY (`id`),
  KEY `idx_bus_coord_sign_task_segment_signer_task_id` (`task_id`),
  KEY `idx_bus_coord_sign_task_segment_signer_segment_id` (`segment_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='签署环节签署记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `business_coord_sign_task_segment_record`
--

LOCK TABLES `business_coord_sign_task_segment_record` WRITE;
/*!40000 ALTER TABLE `business_coord_sign_task_segment_record` DISABLE KEYS */;
/*!40000 ALTER TABLE `business_coord_sign_task_segment_record` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `business_coord_sign_task_segment_signer`
--

DROP TABLE IF EXISTS `business_coord_sign_task_segment_signer`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `business_coord_sign_task_segment_signer` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `task_id` bigint NOT NULL COMMENT '任务ID',
  `segment_id` bigint NOT NULL COMMENT '任务环节ID',
  `signer_seq` int NOT NULL DEFAULT '1' COMMENT '签署者顺序',
  `signer_type` tinyint NOT NULL DEFAULT '1' COMMENT '签署者类型:1-用户个体，2-角色，3-某一组织',
  `signer_uid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '签署者UID',
  `signer_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '签署者名称',
  `seal_option` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '签署者签章配置json',
  `sign_status` tinyint NOT NULL DEFAULT '0' COMMENT '签署状态：0-未签名，2-已拒绝，4-已撤回，5-已完成',
  `sign_opinion` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '签名意见',
  `revoked` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否撤回：0-否，1-是',
  `creator_id` bigint DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `updater` bigint DEFAULT NULL COMMENT '修改者ID',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除：0-否，1-是',
  PRIMARY KEY (`id`),
  KEY `idx_bus_coord_sign_task_segment_signer_task_id_segment_id` (`task_id`,`segment_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='签署环节签署者表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `business_coord_sign_task_segment_signer`
--

LOCK TABLES `business_coord_sign_task_segment_signer` WRITE;
/*!40000 ALTER TABLE `business_coord_sign_task_segment_signer` DISABLE KEYS */;
/*!40000 ALTER TABLE `business_coord_sign_task_segment_signer` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `business_coord_sign_template`
--

DROP TABLE IF EXISTS `business_coord_sign_template`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `business_coord_sign_template` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `template_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模板编号',
  `template_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模板名称',
  `default_task_title` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '默认发起任务标题',
  `template_status` tinyint NOT NULL DEFAULT '0' COMMENT '模板状态：0-正常 1-冻结',
  `segment_num` int DEFAULT '0' COMMENT '环节数量',
  `sign_type` tinyint NOT NULL DEFAULT '0' COMMENT '模板类型：0-P7签名，1-电子签章',
  `project_uid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目UID',
  `sign_option` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '签名配置json',
  `creator_id` bigint DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `updater` bigint DEFAULT NULL COMMENT '修改者ID',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除：0-否，1-是',
  PRIMARY KEY (`id`),
  KEY `idx_business_coord_sign_template_task_code` (`template_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='签署任务模板表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `business_coord_sign_template`
--

LOCK TABLES `business_coord_sign_template` WRITE;
/*!40000 ALTER TABLE `business_coord_sign_template` DISABLE KEYS */;
/*!40000 ALTER TABLE `business_coord_sign_template` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `business_coord_sign_template_segment`
--

DROP TABLE IF EXISTS `business_coord_sign_template_segment`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `business_coord_sign_template_segment` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `template_id` bigint NOT NULL COMMENT '模板ID',
  `segment_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '环节名称',
  `segment_seq` int NOT NULL DEFAULT '1' COMMENT '环节序号',
  `signer_num` int NOT NULL DEFAULT '0' COMMENT '总签署者人数',
  `limit_num` int NOT NULL DEFAULT '1' COMMENT '门限值',
  `creator_id` bigint DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `pre_segment_id` bigint DEFAULT NULL COMMENT '前一环节ID',
  `updater` bigint DEFAULT NULL COMMENT '修改者ID',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除：0-否，1-是',
  PRIMARY KEY (`id`),
  KEY `idx_bus_coord_sign_template_segment_template_id` (`template_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='签署模板环节表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `business_coord_sign_template_segment`
--

LOCK TABLES `business_coord_sign_template_segment` WRITE;
/*!40000 ALTER TABLE `business_coord_sign_template_segment` DISABLE KEYS */;
/*!40000 ALTER TABLE `business_coord_sign_template_segment` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `business_coord_sign_template_segment_signer`
--

DROP TABLE IF EXISTS `business_coord_sign_template_segment_signer`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `business_coord_sign_template_segment_signer` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `template_id` bigint NOT NULL COMMENT '模板ID',
  `segment_id` bigint NOT NULL COMMENT '模板环节ID',
  `signer_seq` int NOT NULL DEFAULT '1' COMMENT '签署者顺序',
  `signer_type` tinyint NOT NULL DEFAULT '1' COMMENT '签署者类型:1-用户个体，2-角色，3-某一组织',
  `signer_uid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '签署者UID',
  `signer_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '签署者名称',
  `seal_option` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '签署者签章配置json',
  `creator_id` bigint DEFAULT NULL COMMENT '创建者ID',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `updater` bigint DEFAULT NULL COMMENT '修改者ID',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除：0-否，1-是',
  PRIMARY KEY (`id`),
  KEY `idx_bus_coord_sign_template_segment_signer_template_segment_id` (`template_id`,`segment_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='签署环节模板签署者表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `business_coord_sign_template_segment_signer`
--

LOCK TABLES `business_coord_sign_template_segment_signer` WRITE;
/*!40000 ALTER TABLE `business_coord_sign_template_segment_signer` DISABLE KEYS */;
/*!40000 ALTER TABLE `business_coord_sign_template_segment_signer` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `business_department`
--

DROP TABLE IF EXISTS `business_department`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `business_department` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `status` tinyint unsigned NOT NULL,
  `gmt_create` datetime NOT NULL,
  `gmt_modified` datetime NOT NULL,
  `district_id` int NOT NULL DEFAULT '1' COMMENT '院区ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='部门信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `business_department`
--

LOCK TABLES `business_department` WRITE;
/*!40000 ALTER TABLE `business_department` DISABLE KEYS */;
INSERT INTO `business_department` VALUES (1,'医护;信息;医务',0,'2025-01-15 16:53:24','2025-01-15 16:59:09',1);
/*!40000 ALTER TABLE `business_department` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `business_keypair_info`
--

DROP TABLE IF EXISTS `business_keypair_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `business_keypair_info` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `keypair_user_enc` blob,
  `keypair_admin_enc` blob NOT NULL,
  `keypair_type` int NOT NULL COMMENT '密钥对类型',
  `keypair_enc_version` tinyint unsigned NOT NULL COMMENT '密钥对加密版本',
  `status` tinyint unsigned NOT NULL DEFAULT '0',
  `cert_attribute_id` int DEFAULT NULL,
  `user_id` int unsigned NOT NULL,
  `keypair_info_enc_parameter_id` int NOT NULL,
  `business_req_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `gmt_create` datetime NOT NULL,
  `gmt_modified` datetime NOT NULL,
  `keypair_admin_enc_0` blob COMMENT '备份旧数据管理员密钥对的冗余字段',
  `keypair_user_enc_0` blob COMMENT '备份旧数据用户密钥对的冗余字段',
  `last_pin_change` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'PIN码最后修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_keypair_cert_attr_id` (`cert_attribute_id`) USING BTREE,
  KEY `idx_keypair_user_id` (`user_id`) USING BTREE,
  KEY `idx_req_id` (`business_req_id`) USING BTREE,
  KEY `business_keypair_info_business_req_id_IDX` (`business_req_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `business_keypair_info`
--

LOCK TABLES `business_keypair_info` WRITE;
/*!40000 ALTER TABLE `business_keypair_info` DISABLE KEYS */;
/*!40000 ALTER TABLE `business_keypair_info` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `business_keypair_info_enc_parameter`
--

DROP TABLE IF EXISTS `business_keypair_info_enc_parameter`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `business_keypair_info_enc_parameter` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `kdf_iter_count` int DEFAULT NULL COMMENT 'kdf导出密钥的迭代次数',
  `salt` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '随机数盐',
  `symmetry_key_enc` blob,
  `initial_variable` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '初始化向量',
  `admin_pin_bpms_enc` blob,
  `gmt_create` datetime NOT NULL,
  `gmt_modified` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `business_keypair_info_enc_parameter`
--

LOCK TABLES `business_keypair_info_enc_parameter` WRITE;
/*!40000 ALTER TABLE `business_keypair_info_enc_parameter` DISABLE KEYS */;
/*!40000 ALTER TABLE `business_keypair_info_enc_parameter` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `business_linkman`
--

DROP TABLE IF EXISTS `business_linkman`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `business_linkman` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `identity_type` int NOT NULL,
  `identity` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `phone` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `email` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `address` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `gmt_create` datetime NOT NULL,
  `gmt_modified` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `business_linkman`
--

LOCK TABLES `business_linkman` WRITE;
/*!40000 ALTER TABLE `business_linkman` DISABLE KEYS */;
/*!40000 ALTER TABLE `business_linkman` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `business_mobile_valid_code`
--

DROP TABLE IF EXISTS `business_mobile_valid_code`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `business_mobile_valid_code` (
  `id` int NOT NULL AUTO_INCREMENT,
  `cert_id` int DEFAULT NULL,
  `user_id` int DEFAULT NULL,
  `phone` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '手机号',
  `valid_code` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '验证码',
  `use_status` tinyint unsigned NOT NULL COMMENT '状态',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `authorized_user_id` int DEFAULT NULL,
  `req_id` int unsigned DEFAULT NULL,
  `gmt_create` datetime NOT NULL,
  `gmt_modified` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='验证码表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `business_mobile_valid_code`
--

LOCK TABLES `business_mobile_valid_code` WRITE;
/*!40000 ALTER TABLE `business_mobile_valid_code` DISABLE KEYS */;
/*!40000 ALTER TABLE `business_mobile_valid_code` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `business_no_cert_user`
--

DROP TABLE IF EXISTS `business_no_cert_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `business_no_cert_user` (
  `id` int NOT NULL AUTO_INCREMENT,
  `uid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '唯一标识',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `phone` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `province` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `city` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `official_residence` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `country_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `gender` tinyint DEFAULT NULL,
  `identity_type` int NOT NULL,
  `identity` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `organization_id` int DEFAULT NULL,
  `cloudkey_project_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `cert_user_type` int DEFAULT '1',
  `gmt_create` datetime NOT NULL,
  `gmt_modified` datetime NOT NULL,
  `department` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `occupation` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `pic_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '签章图片Base64编码文本',
  `pending_pic_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '待审核的签章图片Base64编码文本',
  `pending_pic_status` tinyint NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `business_no_cert_user_uid_idx` (`uid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `business_no_cert_user`
--

LOCK TABLES `business_no_cert_user` WRITE;
/*!40000 ALTER TABLE `business_no_cert_user` DISABLE KEYS */;
/*!40000 ALTER TABLE `business_no_cert_user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `business_occupation`
--

DROP TABLE IF EXISTS `business_occupation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `business_occupation` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `status` tinyint unsigned NOT NULL,
  `gmt_create` datetime NOT NULL,
  `gmt_modified` datetime NOT NULL,
  `district_id` int NOT NULL DEFAULT '1' COMMENT '院区ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='职业信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `business_occupation`
--

LOCK TABLES `business_occupation` WRITE;
/*!40000 ALTER TABLE `business_occupation` DISABLE KEYS */;
INSERT INTO `business_occupation` VALUES (1,'医生;护士;工程师',0,'2025-01-15 17:03:13','2025-01-15 17:03:13',1);
/*!40000 ALTER TABLE `business_occupation` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `business_organization`
--

DROP TABLE IF EXISTS `business_organization`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `business_organization` (
  `id` int NOT NULL AUTO_INCREMENT,
  `identity` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '证件号码',
  `identity_type` int NOT NULL COMMENT '证件类型',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '机构名称',
  `organization_type` int NOT NULL,
  `official_residence` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '机构地址',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '机构电话',
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '机构邮箱',
  `country_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'CN',
  `province` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `legal_person_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '法人名称',
  `legal_person_phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '法人联系电话',
  `legal_person_identity_type` int DEFAULT NULL COMMENT '法人证件类型',
  `legal_person_identity` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '法人证件号码',
  `status` int NOT NULL COMMENT '机构状态',
  `gmt_create` datetime NOT NULL,
  `gmt_modified` datetime NOT NULL,
  `memo` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `district_id` int NOT NULL DEFAULT '1' COMMENT '院区ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `business_organization`
--

LOCK TABLES `business_organization` WRITE;
/*!40000 ALTER TABLE `business_organization` DISABLE KEYS */;
/*!40000 ALTER TABLE `business_organization` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `business_request`
--

DROP TABLE IF EXISTS `business_request`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `business_request` (
  `request_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `bpms_req_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务平台业务单号',
  `request_type` int NOT NULL COMMENT '业务类型',
  `user_id` int NOT NULL,
  `request_status` int NOT NULL COMMENT '业务状态',
  `project_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `cert_id` int NOT NULL,
  `linkman_id` bigint DEFAULT NULL,
  `operator_id` int DEFAULT NULL COMMENT '操作员id',
  `request_time` datetime NOT NULL,
  `success_time` datetime DEFAULT NULL,
  `gmt_create` datetime NOT NULL,
  `gmt_modified` datetime NOT NULL,
  `memo` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  PRIMARY KEY (`request_id`),
  KEY `cert_id` (`cert_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `business_request`
--

LOCK TABLES `business_request` WRITE;
/*!40000 ALTER TABLE `business_request` DISABLE KEYS */;
/*!40000 ALTER TABLE `business_request` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `business_request_apply_temp`
--

DROP TABLE IF EXISTS `business_request_apply_temp`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `business_request_apply_temp` (
  `id` int NOT NULL AUTO_INCREMENT,
  `uid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户唯一标识符',
  `operator_id` int NOT NULL COMMENT '操作员id',
  `project_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目id',
  `keypair_sign_seri` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '序列化后的相关密钥对密文',
  `register_request` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `gmt_create` datetime NOT NULL,
  `gmt_modified` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='暂存的证书申请业务';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `business_request_apply_temp`
--

LOCK TABLES `business_request_apply_temp` WRITE;
/*!40000 ALTER TABLE `business_request_apply_temp` DISABLE KEYS */;
/*!40000 ALTER TABLE `business_request_apply_temp` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `business_user`
--

DROP TABLE IF EXISTS `business_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `business_user` (
  `id` int NOT NULL AUTO_INCREMENT,
  `uid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户唯一标识符',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `phone` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `province` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `city` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `official_residence` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `country_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `gender` tinyint(1) DEFAULT NULL,
  `identity_type` int NOT NULL,
  `identity` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `make_cert_status` int NOT NULL,
  `make_cert_count` int NOT NULL DEFAULT '0' COMMENT '已制证对数',
  `status` int NOT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `organization_id` int DEFAULT NULL,
  `cloudkey_project_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `gmt_create` datetime NOT NULL,
  `gmt_modified` datetime NOT NULL,
  `cert_user_type` int DEFAULT '1' COMMENT '冗余config_project的cert_user_type',
  `department` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `occupation` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `cert_validity_end` datetime DEFAULT NULL COMMENT '指定证书到期时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_uid` (`uid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `business_user`
--

LOCK TABLES `business_user` WRITE;
/*!40000 ALTER TABLE `business_user` DISABLE KEYS */;
/*!40000 ALTER TABLE `business_user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `cert_alert_policy`
--

DROP TABLE IF EXISTS `cert_alert_policy`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cert_alert_policy` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '策略ID，自增',
  `district_id` int NOT NULL COMMENT '院区ID',
  `name` varchar(64) NOT NULL COMMENT '策略名称',
  `notify_interval` int NOT NULL COMMENT '通知间隔时间，单位：天',
  `cert_alert_days` int NOT NULL COMMENT '证书过期报警天数',
  `notifier` varchar(64) DEFAULT NULL COMMENT '通知人',
  `notify_method` tinyint(1) NOT NULL COMMENT '通知方式，如：短信、电子邮件',
  `notify_time` time NOT NULL COMMENT '通知时间',
  `notify_number` varchar(64) NOT NULL COMMENT '通知号码，如：手机号码、邮箱地址',
  `sms_platform` varchar(64) DEFAULT NULL COMMENT '短信平台',
  `status` tinyint(1) NOT NULL COMMENT '启用状态，0：已禁用 1：已启用',
  `gmt_create` datetime NOT NULL COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='证书预警策略表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `cert_alert_policy`
--

LOCK TABLES `cert_alert_policy` WRITE;
/*!40000 ALTER TABLE `cert_alert_policy` DISABLE KEYS */;
/*!40000 ALTER TABLE `cert_alert_policy` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `cert_last_app_log`
--

DROP TABLE IF EXISTS `cert_last_app_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cert_last_app_log` (
  `id` int NOT NULL AUTO_INCREMENT,
  `cert_id` int NOT NULL COMMENT '证书id',
  `gmt_create` datetime NOT NULL COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL COMMENT '修改时间',
  `operator_name` varchar(255) DEFAULT NULL COMMENT '操作用户名称',
  `user_type` int DEFAULT NULL COMMENT '用户类型：证书用户、授权用户',
  `description` varchar(255) DEFAULT '' COMMENT '接口操作中文描述',
  `success` int DEFAULT '-1' COMMENT '操作状态：成功(0)、失败(-1)',
  `request_time` datetime DEFAULT NULL COMMENT '请求时间',
  PRIMARY KEY (`id`),
  KEY `idx_cert_last_app_log_cert_id` (`cert_id`) USING BTREE COMMENT '证书Id索引，用于快速更新'
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `cert_last_app_log`
--

LOCK TABLES `cert_last_app_log` WRITE;
/*!40000 ALTER TABLE `cert_last_app_log` DISABLE KEYS */;
/*!40000 ALTER TABLE `cert_last_app_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `cert_reserve_task`
--

DROP TABLE IF EXISTS `cert_reserve_task`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cert_reserve_task` (
  `id` int NOT NULL AUTO_INCREMENT,
  `cert_id` int NOT NULL COMMENT '证书id',
  `memo` varchar(255) DEFAULT NULL COMMENT '备注信息',
  `other_info` varchar(512) DEFAULT NULL COMMENT '用于填写其他信息(json结构存储)',
  `reserve_request_type` int NOT NULL COMMENT '预约请求类型，参考数据字典CloudkeyRequestTypeConstant',
  `reserve_request_time` datetime NOT NULL COMMENT '预约日期',
  `status` int NOT NULL COMMENT '状态，0未执行; 1已执行',
  `execute_time` datetime DEFAULT NULL COMMENT '执行时间',
  `gmt_create` datetime NOT NULL COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL COMMENT '最后修改时间(变动了预约时间时会修改)',
  PRIMARY KEY (`id`),
  KEY `idx_cert_id` (`cert_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `cert_reserve_task`
--

LOCK TABLES `cert_reserve_task` WRITE;
/*!40000 ALTER TABLE `cert_reserve_task` DISABLE KEYS */;
/*!40000 ALTER TABLE `cert_reserve_task` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `change_encrypt_card_record`
--

DROP TABLE IF EXISTS `change_encrypt_card_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `change_encrypt_card_record` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` int NOT NULL COMMENT '用户id',
  `old_key_id` int NOT NULL COMMENT '旧的密钥对ID',
  `new_key_id` int DEFAULT NULL COMMENT '新产生的密钥对ID',
  `access_data` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '访问码',
  `status` int DEFAULT '0' COMMENT '是否完成更换，0未完成，1已完成。',
  `card_type` int DEFAULT '2' COMMENT '更换成的密码卡类型，1：得安卡，2：三未卡，默认为2',
  `gmt_create` datetime NOT NULL COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户更换密码卡的记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `change_encrypt_card_record`
--

LOCK TABLES `change_encrypt_card_record` WRITE;
/*!40000 ALTER TABLE `change_encrypt_card_record` DISABLE KEYS */;
/*!40000 ALTER TABLE `change_encrypt_card_record` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `config_key_value`
--

DROP TABLE IF EXISTS `config_key_value`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `config_key_value` (
  `id` int NOT NULL AUTO_INCREMENT,
  `config_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `config_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `config_value` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `modify_enable` tinyint(1) NOT NULL DEFAULT '1' COMMENT '默认为1，表示(建议运行时)允许修改；0 为禁止修改',
  `show_enable` tinyint(1) NOT NULL DEFAULT '1' COMMENT '默认为1，表示(建议运行时)可见；0 为不可见',
  `gmt_create` datetime NOT NULL,
  `gmt_modified` datetime NOT NULL,
  `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `config_key_unique` (`config_key`)
) ENGINE=InnoDB AUTO_INCREMENT=160 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `config_key_value`
--

LOCK TABLES `config_key_value` WRITE;
/*!40000 ALTER TABLE `config_key_value` DISABLE KEYS */;
INSERT INTO `config_key_value` VALUES (1,'PIN码短信提示语','SMS_SEND_PIN','您的数字证书（用户名为%{UserName}）密钥已产生，密码为%{UserPin}。数字证书签名具有法律效力，请及时修改密码并妥善保管。',1,1,'2024-05-23 14:47:46','2024-05-23 14:47:55','PIN码短信提示语'),(2,'短信验证码短信提示语','SMS_SEND_VALIDCODE','验证码%{validCode}(网证通短信验证码，请勿泄露)，需要你进行身份验证，2分钟内有效。如非本人操作，请联系单位管理员。',1,1,'2024-05-23 14:47:46','2024-05-23 14:47:55','短信验证码短信提示语'),(3,'证书签发成功通知短信提示语','SMS_SEND_ISSUE_CERT','%{UserName}的数字证书已签发成功，请在应用市场下载“网证通云密钥客户端”完成设备绑定和个人签名采集。如有问题请咨询主管部门。',1,1,'2024-05-23 14:47:46','2024-05-23 14:47:55','证书签发成功通知短信提示语'),(4,'解锁后的用户新口令短信提示语','SMS_SEND_UNLOCK','您的数字证书（用户名为%{UserName}）密钥已解锁，新保护密码为%{UserPin}。数字证书签名具有法律效力，请及时修改密码并妥善保管。',1,1,'2024-05-23 14:47:46','2024-05-23 14:47:55','解锁后的用户新口令短信提示语'),(5,'已录入授权用户数量给授权人短信提示语','SMS_SEND_NOTICE_USER','您的数字证书（用户名为%{UserName}）可授权予[%{Users}]使用。数字证书签名具有法律效力，请谨慎做出授权。',1,1,'2024-05-23 14:47:46','2024-05-23 14:47:55','已录入授权用户数量给授权人短信提示语'),(6,'成功授权后提示证授权人的短信提示语','SMS_SEND_AUTHORIZE','您的数字证书（用户名为%{UserName}）已成功授权予[%{Users}]使用！如非本人操作或需解除授权，请打开网证通云密钥APP进行操作。',1,1,'2024-05-23 14:47:46','2024-05-23 14:47:55','成功授权后提示证授权人的短信提示语'),(7,'被授权口令短信提示语','SMS_SEND_AUTHORIZED_PIN','数字证书（用户名为%{UserName}）已成功授权予您(%{AuthUserName})使用，密码为%{UserPin}。数字证书签名具有法律效力，请及时修改密码并妥善保管。',1,1,'2024-05-23 14:47:46','2024-05-23 14:47:55','被授权口令短信提示语'),(8,'授权修改通知被授权人的短信提示语','SMS_SEND_AUTHORIZED_MODIFY','数字证书（用户名为%{UserName}）已成功授权予您(%{AuthorizedUser})使用，授权次数为%{Number}，授权有效期为%{EndDateStr}。',1,1,'2024-05-23 14:47:46','2024-05-23 14:47:55','授权修改通知被授权人的短信提示语'),(9,'业务平台URL路径前缀','CLOUDKEY.SYSTEM.BPMS.URL_PREFIX','https://bpms.cnca.net/',1,1,'2024-05-23 14:47:46','2024-05-23 14:47:46','业务平台URL路径前缀'),(10,'微信获取token路径','CLOUDKEY.SYSTEM.WECHAT.ACCESS_TOKEN_URL','https://api.weixin.qq.com/cgi-bin/token',1,1,'2024-05-23 14:47:46','2024-05-23 14:47:46','微信获取token路径'),(11,'微信的appid号','CLOUDKEY.SYSTEM.WECHAT.APPID','wx43d29137caead9c8',1,1,'2024-05-23 14:47:46','2024-05-23 14:47:46','微信的appid号'),(12,'微信的secret号','CLOUDKEY.SYSTEM.WECHAT.SECRET','de8d3a363f6a805f50544194bb164bb3',1,1,'2024-05-23 14:47:46','2024-05-23 14:47:46','微信的secret号'),(13,'微信获取未限制路径','CLOUDKEY.SYSTEM.WECHAT.GET_UNLIMITED_URL','https://api.weixin.qq.com/wxa/getwxacodeunlimit',1,1,'2024-05-23 14:47:46','2024-05-23 14:47:46','微信获取未限制路径'),(14,'是否产生带host二维码，默认false','CLOUDKEY.SYSTEM.WECHAT.QRCODE_NOT_HOST','false',1,1,'2024-05-23 14:47:46','2024-05-23 14:47:46','是否产生带host二维码，默认false'),(15,'二维码指定的服务器安全域名，长度限定22个字符','CLOUDKEY.SYSTEM.WECHAT.QRCODE_HOST','test.cnca.net:61443',1,1,'2024-05-23 14:47:46','2024-05-23 14:47:46','二维码指定的服务器安全域名，长度限定22个字符'),(16,'手机二维码签名算法','CLOUDKEY.SYSTEM.MOBILE_PHONE_SIGN_ALGO','4',1,1,'2024-05-23 14:47:46','2024-05-23 14:47:46','手机二维码签名算法'),(17,'二维码前缀','CLOUDKEY.SYSTEM.MOBILE_QRCODE_PREFIX','netcack://',1,1,'2024-05-23 14:47:46','2024-05-23 14:47:46','二维码前缀'),(18,'手机扫码，二维码的服务器地址数据','CLOUDKEY.SYSTEM.MOBILE_QRCODE_DOMAIN','',1,1,'2024-05-23 14:47:46','2024-05-23 14:47:46','手机扫码，二维码的服务器地址数据'),(19,'时间戳地址','CLOUDKEY.SYSTEM.TIMESTAMP_URL','',1,1,'2024-05-23 14:47:47','2024-05-23 14:47:47','时间戳地址'),(20,'时间戳算法','CLOUDKEY.SYSTEM.TIMESTAMP_HASH_ALGO','SHA256',1,1,'2024-05-23 14:47:47','2024-05-23 14:47:47','时间戳算法'),(21,'是否只允许一个用户登录','CLOUDKEY.SYSTEM.SINGLE_SIGN_ON','false',1,1,'2024-05-23 14:47:47','2024-05-23 14:47:47','是否只允许一个用户登录'),(22,'是否允许通过接口录入授权信息','CLOUDKEY.SYSTEM.PERMIT_INVOKE_AUTHORIZE','true',1,1,'2024-05-23 14:47:47','2024-05-23 14:47:47','是否允许通过接口录入授权信息'),(23,'产生短信验证码接口，1通过短信下发，2通过接口返回，3以上均有','CLOUDKEY.SYSTEM.GEN_VALIDCODE','1',1,1,'2024-05-23 14:47:47','2024-05-23 14:47:47','产生短信验证码接口，1通过短信下发，2通过接口返回，3以上均有'),(24,'是否需要验证图形验证码','CLOUDKEY.SYSTEM.NEED_VERIFY_IMG_VALIDCODE','true',1,1,'2024-05-23 14:47:47','2024-05-23 14:47:47','是否需要验证图形验证码'),(27,'业务平台解密管理员pin码路径','CLOUDKEY.SYSTEM.BPMS.DEC_ADMIN_PIN_URL','certbpms/newDecryptSoPin.servlet',1,1,'2024-05-23 14:47:47','2024-05-23 14:47:47','业务平台解密管理员pin码路径'),(28,'业务平台新申请证书路径','CLOUDKEY.SYSTEM.BPMS.REGISTER_CERT_URL','certbpms/RegisterCert.servlet',1,1,'2024-05-23 14:47:47','2024-05-23 14:47:47','业务平台新申请证书路径'),(29,'业务平台查询业务状态路径','CLOUDKEY.SYSTEM.BPMS.REQUEST_SEARCH_URL','certbpms/requestSearch.servlet',1,1,'2024-05-23 14:47:47','2024-05-23 14:47:47','业务平台查询业务状态路径'),(30,'业务平台下载证书路径','CLOUDKEY.SYSTEM.BPMS.DOWNLOAD_CERT_URL','certbpms/certSearch.servlet',1,1,'2024-05-23 14:47:47','2024-05-23 14:47:47','业务平台下载证书路径'),(31,'业务平台注销证书路径','CLOUDKEY.SYSTEM.BPMS.REVOKE_CERT_URL','certbpms/revokedCert.servlet',1,1,'2024-05-23 14:47:47','2024-05-23 14:47:47','业务平台注销证书路径'),(32,'业务平台证书续期路径','CLOUDKEY.SYSTEM.BPMS.RENEWAL_CERT_URL','certbpms/renewalCert.servlet',1,1,'2024-05-23 14:47:47','2024-05-23 14:47:47','业务平台证书续期路径'),(33,'业务平台用户唯一标识扩展id','CLOUDKEY.UID_BPMS_USEREXTFIELDID','0',1,1,'2024-05-23 14:47:47','2024-05-23 14:47:47','业务平台用户唯一标识扩展id'),(34,'加密卡rsa密钥对长度，得安、三未都是加密卡1412，软设备1203，-1表示不检查','CLOUDKEY.SYSTEM.RSA_KEYPAIR_LENGTH','1412',1,1,'2024-05-23 14:47:47','2024-05-23 14:47:47','加密卡rsa密钥对长度，得安、三未都是加密卡1412，软设备1203，-1表示不检查'),(35,'加密卡sm2密钥对长度，得安加密卡292，三未加密卡104，软设备117，-1表示不检查','CLOUDKEY.SYSTEM.SM2_KEYPAIR_LENGTH','292',1,1,'2024-05-23 14:47:47','2024-05-23 14:47:47','加密卡sm2密钥对长度，得安加密卡292，三未加密卡104，软设备117，-1表示不检查'),(36,'批量上传签章图片功能，文件编码','CLOUDKEY.UPLOAD_ZIP_ENCODING','GBK',1,1,'2024-05-23 14:47:47','2024-05-23 14:47:47','批量上传签章图片功能，文件编码'),(37,'OCSP路径','CLOUDKEY.SYSTEM.OCSP_URL','',1,1,'2024-05-23 14:47:47','2024-05-23 14:47:47','OCSP路径'),(38,'电子签名系统与云密钥对接时间戳签名证书','ELESIGN.SYSTEM.SIGNCERT','',1,1,'2020-07-23 16:22:13','2020-07-23 17:22:22','电子签名系统与云密钥对接时间戳签名证书'),(39,'用户密钥对不被使用的失效时间','CLOUDKEY.SYSTEM.USER_KEYPAIR_EXPIRE_TIME','18000',1,1,'2020-07-23 09:22:12','2020-07-23 09:22:12','用户密钥对不被使用的失效时间(单位秒)'),(40,'是否开启电子签名系统扩展功能','CLOUDKEY.ELESIGN.OPEN','true',1,1,'2020-07-23 16:22:13','2020-07-23 16:22:13','该接口未启用'),(41,'是否开启手机消息推送功能','CLOUDKEY.MQTT.OPEN','false',1,1,'2020-07-23 16:22:13','2020-07-23 16:22:13','该接口未启用'),(42,'系统配置的tomcat service名字，默认为tomcat','CLOUDKEY.TOMCAT.SERVICE.NAME','tomcat',1,1,'2024-05-23 14:47:48','2024-05-23 14:47:48','系统配置的tomcat service名字，默认为tomcat'),(43,'部署的服务器ip和tomcat端口，单机部署时配置值为空,集群部署时根据实际部署的ip进行配置，多个用英文逗号隔开，格式为ip:port','CLOUDKEY.SYSTEM.ADDRESS','',1,1,'2024-05-23 14:47:48','2024-05-23 14:47:48','部署的服务器ip和tomcat端口，单机部署时配置值为空,集群部署时根据实际部署的ip进行配置，多个用英文逗号隔开，格式为ip:port'),(44,'三未加密卡操作员账号','CLOUDKEY.SYSTEM.SANWEIOPERATOR',NULL,1,1,'2020-07-23 14:12:06','2020-07-23 14:12:06','三未加密卡操作员账号'),(45,'三未加密卡操作员账号对应的密码','CLOUDKEY.SYSTEM.SANWEIPASSWORD','12345678',1,1,'2020-07-23 08:12:06','2020-07-23 09:12:06','三未加密卡操作员账号对应的密码'),(46,'得安加密卡操作员账号','CLOUDKEY.SYSTEM.SJK1555_OPERATOR','11111111',1,1,'2020-07-23 09:22:06','2020-07-23 09:23:06','加密卡操作员账号'),(47,'得安加密卡操作员账号对应的密码','CLOUDKEY.SYSTEM.SJK1555_PASSWORD','11111111',1,1,'2020-07-23 09:22:12','2020-07-23 09:23:00','加密卡操作员账号对应的密码'),(48,'数据库缓存，添加到缓存后过了多久失效','CLOUDKEY.SYSTEM.DB_INFO_EXPIRE_TIME','120',1,1,'2020-07-23 09:22:12','2020-07-23 09:22:12','数据库缓存，添加到缓存后过了多久失效（单位秒）'),(49,'访问外部接口是否验证证书链','CLOUDKEY.SYSTEM.IS_KEYPAIR_CERT_CHAIN','false',1,1,'2020-07-23 09:22:12','2020-07-23 09:22:12','访问外部接口是否验证证书链'),(50,'插入用户信息校验失效时间、二维码、短信验证码失效时间','CLOUDKEY.SYSTEM.USER_VERIFY_EXPIRE_TIME','2',1,1,'2020-07-23 09:22:12','2020-07-23 09:22:12','用户信息校验失效时间 --二维码，短信验证码失效时间（单位分）'),(51,'下次产生验证码的最短时间间隔','CLOUDKEY.SYSTEM.VALID_CODE_MIN_NEXT_CREATE_TIME','1',1,1,'2020-07-23 09:22:12','2020-07-23 09:22:12','下次产生验证码的最短时间间隔（单位：分）'),(52,'系统首页备案信息','CLOUDKEY.RECORD_INFO','<div style=\"margin:0 auto;padding:10px 0;\"> <span>增值电信业务许可证号：粤B2-20040113号</span> </div> <div style=\"margin:0 auto;padding:10px 0;\"> <span> ICP网站备案号: <a href=\"http://www.beian.miit.gov.cn\">粤B2-20040113号-1</a> </span> </div> <div style=\"margin:0 auto;padding:10px 0;\"> <a target=\"_blank\" href=\"http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=44010402000781\" style=\"display:inline-block;height:20px;line-height:20px;\" > <img src=\"data:image/png;base64,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\" style=\"float:left;margin-right:5px;\" border=\"0\" /> <span>粤公网安备 44010402000781号</span> </a> </div>',1,1,'2020-07-23 09:22:12','2020-07-23 09:22:12','系统首页备案信息'),(53,'电子签名系统与云密钥对接时间戳','ELESIGN.SYSTEM.TIMESTAMP','600000',1,1,'2020-07-23 16:22:12','2020-07-23 17:22:12','电子签名系统与云密钥对接时间戳，单位是毫秒，10分钟'),(55,'MQTT-客户端连接地址','MQ.MQTT.CLIENTURL','ssl://192.168.200.61:1883',1,1,'2020-07-23 16:22:13','2020-07-23 16:22:13','MQTT-客户端连接地址，如果有多个，用逗号隔开'),(56,'MQTT-服务器连接地址','MQ.MQTT.SERVERURL','ssl://192.168.200.61:1883',1,1,'2020-07-23 16:22:13','2020-07-23 16:22:13','MQTT-服务器连接地址，如果有多个，用逗号隔开'),(57,'MQTT-连接服务器默认客户端ID','MQ.MQTT.CLIENT.ID','cloudkey-mqtt-client-id',1,1,'2020-07-23 16:22:13','2020-07-23 16:22:13','MQTT-连接服务器默认客户端ID'),(58,'MQTT-连接服务器有效期','MQ.MQTT.COMPLETION.TIMEOUT','3000',1,1,'2020-07-23 16:22:13','2020-07-23 16:22:13','MQTT-连接服务器有效期'),(59,'消息质量','MQ.MQTT.QOS','0',1,1,'2020-07-23 16:22:13','2020-07-23 16:22:13','消息质量 0-最多一次，即发即弃；1-最少一次，客户端最少接收到一次；2-只一次，每个消息都只被接收到一次'),(60,'是否保留最新消息','MQ.MQTT.RETAINED','true',1,1,'2020-07-23 16:22:13','2020-07-23 16:22:13','是否保留最新消息'),(61,'连接超时时间','MQ.MQTT.CONNECTION.TIMEOUT','30',1,1,'2020-07-23 16:22:13','2020-07-23 16:22:13','连接超时时间（秒）'),(62,'设置心跳时间间隔','MQ.MQTT.KEEPALIVED.INTERVAL','60',1,1,'2020-07-23 16:22:13','2020-07-23 16:22:13','设置心跳时间间隔（秒）'),(63,'最大等待被推送的消息数量','MQ.MQTT.MAXINFLIGHT','100',1,1,'2020-07-23 16:22:13','2020-07-23 16:22:13','最大等待被推送的消息数量'),(64,'订阅主题前缀','CLOUDKEY.MQTT.TOPIC.PREFIX','Cloudkey/Mqtt',1,1,'2020-07-23 16:22:13','2020-07-23 16:22:13','订阅主题前缀，用于区分不同医院或者机构，常用项目名 eg:“cloudkeyjiangmen”'),(65,'消息通知署名','CLOUDKEY.MQTT.MSG.SIGNATURE','医院',1,1,'2020-07-23 16:22:13','2020-07-23 16:22:13','消息通知署名'),(66,'MQTT-服务端账号','MQ.MQTT.USERNAME','cloudkeyservermqtt',1,1,'2020-07-23 16:22:13','2020-07-23 16:22:13','MQTT-服务端账号'),(67,'MQTT-服务端密码','MQ.MQTT.PASSWORD','YMY@GG456',1,1,'2020-07-23 16:22:13','2020-07-23 16:22:13','MQTT-服务端密码'),(68,'MQTT-客户端账号','CLOUDKEY.MQTT.SUBSCRIBER.USERNAME','cloudkeyappmqtt',1,1,'2020-07-23 16:22:13','2020-07-23 16:22:13','MQTT-客户端账号'),(69,'MQTT-客户端密码','CLOUDKEY.MQTT.SUBSCRIBER.PASSWORD','YMY@GG123',1,1,'2020-07-23 16:22:13','2020-07-23 16:22:13','MQTT-客户端密码'),(70,'IPhone证书路径','CLOUDKEY.SYSTEM.APPLE.KEYSTORE.PATH','C:/Users/<USER>/Desktop/test/软证书/Test_Demo_APNs_Dev.p12',1,1,'2020-07-23 16:22:13','2020-07-23 16:22:13','IPhone证书路径'),(71,'IPhone证书密码','CLOUDKEY.SYSTEM.APPLE.KEYSTORE.PWD','123456',1,1,'2020-07-23 16:22:13','2020-07-23 16:22:13','IPhone证书密码'),(72,'IPhone证书类型','CLOUDKEY.SYSTEM.APPLE.KEYSTORE.TYPE','PKCS12',1,1,'2020-07-23 16:22:13','2020-07-23 16:22:13','IPhone证书类型'),(73,'IPhone提示音','CLOUDKEY.SYSTEM.APPLE.SOUND','default',1,1,'2020-07-23 16:22:13','2020-07-23 16:22:13','IPhone提示音'),(74,'IPhone网关url','CLOUDKEY.SYSTEM.APPLE.GATEWAY.DESTINATION','api.push.apple.com',1,1,'2020-07-23 16:22:13','2020-07-23 16:22:13','IPhone网关url'),(75,'IPhone网关端口','CLOUDKEY.SYSTEM.APPLE.GATEWAY.DESTINATION.PORT','443',1,1,'2020-07-23 16:22:13','2020-07-23 16:22:13','IPhone网关端口'),(76,'IPhone回溯url','CLOUDKEY.SYSTEM.APPLE.FEEDBACK.DESTINATION','feedback.push.apple.com',1,1,'2020-07-23 16:22:13','2020-07-23 16:22:13','IPhone回溯url'),(77,'IPhone回溯端口','CLOUDKEY.SYSTEM.APPLE.FEEDBACK.DESTINATION.PORT','2196',1,1,'2020-07-23 16:22:13','2020-07-23 16:22:13','IPhone回溯端口'),(78,'IPhone主题类','CLOUDKEY.SYSTEM.APPLE.TOPIC','net.cnca.cloudkeyclient.enterprise',1,1,'2020-07-23 16:22:13','2020-07-23 16:22:13','IPhone主题类'),(79,'配置app初始化二维码功能是否开启','SERVICE_CONFIG.OPEN','false',1,1,'2024-05-23 14:47:49','2024-05-23 14:47:49','该接口未启用'),(80,'系统配置-服务管理的tab标签是否开启（重启服务功能）','SERVICE_MANAGE.OPEN','false',1,1,'2024-05-23 14:47:49','2024-05-23 14:47:49','该接口未启用'),(81,'系统配置-是否在签名后进行验签操作后才返回','CLOUDKEY.SYSTEM.VERIFY_AFTER_SIGN','true',1,1,'2024-05-23 14:47:50','2024-05-23 14:47:50','系统配置-是否在签名后进行验签操作后才返回'),(82,'自助申请服务是否开启','SERVICE_SELF_REGISTER.OPEN','false',1,1,'2024-05-23 14:47:51','2024-05-23 14:47:51','该接口未启用'),(83,'解锁是否返回加密的新PIN码','CLOUDKEY.SYSTEM.RETURN.UNLOCK_PIN','1',1,1,'2024-05-23 14:47:51','2024-05-23 14:47:51','解锁是否返回加密的新PIN码，1通过短信下发，2通过接口返回，3以上均有'),(85,'是否开启防重放攻击校验','VALID_TIMESTAMP_TOKEN.OPEN','false',1,1,'2024-05-23 14:47:51','2024-05-23 14:47:51','是否开启防重放攻击校验'),(86,'时间戳校验允许的时间差，单位秒','VALID_TIMESTAMP_INTERVAL','1800',1,1,'2024-05-23 14:47:51','2024-05-23 14:47:51','时间戳校验允许的时间差，单位秒'),(87,'随机数校验允许重复的时间范围，单位秒','VALID_TIMESTAMP_TOKEN_EXPIRE_TIME','1800',1,1,'2024-05-23 14:47:51','2024-05-23 14:47:51','随机数校验允许重复的时间范围，单位秒'),(88,'腾讯云-密钥信息secretId','TECENT_CLOUD_SECRETID','AKIDf5GAnMp6E6hoeD5F0bI2XmkRDiw4u37v',1,1,'2024-05-23 14:47:53','2024-05-23 14:47:53','腾讯云-密钥信息secretId'),(89,'腾讯云-密钥信息secretKey','TECENT_CLOUD_SECRETKEY','iV7Ug391wdYGl7g2B5jhR2OiIUBwc4Jp',1,1,'2024-05-23 14:47:53','2024-05-23 14:47:53','腾讯云-密钥信息secretKey'),(90,'腾讯云-人脸识别模块的host','TECENT_CLOUD_FACE_HOST','faceid.tencentcloudapi.com',1,1,'2024-05-23 14:47:53','2024-05-23 14:47:53','腾讯云-人脸识别模块的host'),(91,'腾讯云-人脸识别模块的region','TECENT_CLOUD_FACE_REGION','ap-guangzhou',1,1,'2024-05-23 14:47:53','2024-05-23 14:47:53','腾讯云-人脸识别模块的region'),(92,'腾讯云-人脸识别模块的service','TECENT_CLOUD_FACE_SERVICE','faceid',1,1,'2024-05-23 14:47:53','2024-05-23 14:47:53','腾讯云-人脸识别模块的service'),(93,'腾讯云-人脸识别模块的version','TECENT_CLOUD_FACE_VERSION','2018-03-01',1,1,'2024-05-23 14:47:53','2024-05-23 14:47:53','腾讯云-人脸识别模块的version'),(94,'腾讯云-获取giztoken的action','TECENT_CLOUD_FACE_GETBIZTOKEN_ACTION','GetBizToken',1,1,'2024-05-23 14:47:53','2024-05-23 14:47:53','腾讯云-获取giztoken的action'),(95,'腾讯云-获取人机校验结果的action','TECENT_CLOUD_FACE_GETRESULT_ACTION','GetInteractiveVideoResult',1,1,'2024-05-23 14:47:53','2024-05-23 14:47:53','腾讯云-获取人机校验结果的action'),(96,'业务平台上传附件路径','CLOUDKEY.SYSTEM.BPMS.SUBMIT_REQUEST_FILE_URL','certbpms/submitRequestFile.action',1,1,'2024-05-23 14:47:53','2024-05-23 14:47:53','业务平台上传附件路径'),(97,'审核管理是否开启','APPROVE_MANAGE_OPEN','false',1,1,'2024-05-23 14:47:53','2024-05-23 14:47:53','审核管理是否开启'),(98,'下载证书定时任务循环执行时间，单位秒','DOWNCERT_SCHEDULE_TIME','false',1,1,'2024-05-23 14:47:54','2024-05-23 14:47:54','下载证书定时任务循环执行时间，单位秒'),(99,'纯数字初始pin码长度','CLOUDKEY.SYSTEM.NUMERIC_INITIAL_PIN_LENGTH','-1',1,1,'2024-05-23 14:47:58','2024-05-23 14:47:58','配置值为数字。配置值有效范围为[4,12]，小于有效范围取有效范围最小值，大于有效范围取有效范围最大值。配置值为正数且项目未启用口令规则时，系统使用纯数字初始pin码，pin码长度为配置值。值为负数时，不启用此配置，系统根据项目口令配置生成随机pin码。'),(100,'系统logo图片信息','CLOUDKEY.SYSTEM.LOGO_INFO','',1,1,'2024-05-23 14:47:58','2024-05-23 14:47:58','配置值为图片base64，参考格式：data:image/png;base64,base64值'),(101,'外部系统接口防重放随机数有效时间','CHECK_REPLAY_NONCE_VALID_TIME','300',1,1,'2021-09-13 10:10:18','2021-09-13 10:10:18','第三方系统接口防重放随机数有效时间(单位秒)，只在服务启动时有效(设置到redis的ttl); 之后在管理系统上修改时，只能设置时间戳的比对时间; 可以设置为-1，表示不比对时间戳(如两个系统间时间差值过大时可以设置为-1)，如果启动时值为-1还表示随机数过期时间为默认值300秒。'),(102,'证书预约注销任务配置json','CLOUDKEY.SYSTEM.RESERVE.REVOKE.TASK','{\"cron\": \"0 0 1-6 * * ?\", \"immediately\": 0, \"enable\": 1}',1,1,'2024-05-23 14:48:03','2024-05-23 14:48:03','证书预约注销任务配置json; cron 表示定时任务的cron表达式 用于控制执行时间和频率，immediately 如果为1则马上执行一次定时任务， enable 控制定时任务是否执行'),(103,'定时下载证书任务配置json','CLOUDKEY.SYSTEM.CERT.DOWN.TASK','{\"cron\": \"0 0 * * * ?\", \"immediately\": 0, \"enable\": 0}',1,1,'2024-05-23 14:48:03','2024-05-23 14:48:03','定时下载证书任务配置json; cron 表示定时任务的cron表达式 用于控制执行时间和频率，immediately 如果为1则马上执行一次定时任务， enable 控制定时任务是否执行'),(104,'是否使用短信控制台','CLOUDKEY.SYSTEM.SMS.CONSOLE','0',1,1,'2024-05-23 14:48:04','2024-05-23 14:48:04','控制是否开启短信控制台，1启动，0关闭'),(105,'业务平台事件证书URL路径前缀','CLOUDKEY.SYSTEM.BPMS.EVENTCERT.URL_PREFIX','https://bpms.cnca.net/',1,1,'2024-05-23 14:48:07','2024-05-23 14:48:07','业务平台事件证书URL路径前缀'),(106,'业务平台新申请事件证书路径','CLOUDKEY.SYSTEM.BPMS.REGISTER_EVENT_CERT_URL','register-cert/certbpms/RegisterCert.servlet',1,1,'2024-05-23 14:48:07','2024-05-23 14:48:07','业务平台新申请事件证书路径'),(107,'第三方接口注册用户是否返回加密的初始PIN码','CLOUDKEY.SYSTEM.RETURN.REGISTER_PIN','1',1,1,'2024-05-23 14:48:08','2024-05-23 14:48:08','第三方接口注册用户是否返回加密的初始PIN码(仅当为录入并签发证书时)，1通过短信下发，2通过接口返回，3以上均有'),(108,'手机号码唯一性检测','CLOUDKEY.SYSTEM.UNIQUE.PHONE','false',1,1,'2024-05-23 14:48:09','2024-05-23 14:48:09','创建或修改用户时是否开启手机号码唯一性检测'),(109,'身份证件号码唯一性检测','CLOUDKEY.SYSTEM.UNIQUE.IDENTITY','false',1,1,'2024-05-23 14:48:09','2024-05-23 14:48:09','创建或修改用户时是否开启身份证件号码唯一性检测'),(110,'敏感参数加解密key','CLOUDKEY.PARAMETER.ENCRYPT_KEY','7bb1d5d9442c44213c9f63a528ecdfed',1,1,'2024-05-23 14:48:09','2024-05-23 14:48:09','敏感参数加解密Key HEX编码（编码后长度只允许32字符）'),(111,'用户信息第三方检验开关','CLOUDKEY.USER.CHECK_ENABLE','false',1,1,'2024-05-23 14:48:09','2024-05-23 14:48:09','添加用户时，是否需要调用第三方接口进行检验，true启用，false不启用'),(112,'强制用户密码失效开关','CLOUDKEY.EXIST.PIN.CHECK_ENABLE','false',1,1,'2024-05-23 14:48:09','2024-05-23 14:48:09','当项目配置使用了强密码，是否强行要求用户解锁。true启用，false不启用'),(113,'签名验签服务器地址','CLOUDKEY.SYSTEM.SVS_SERVER_URL','',1,1,'2024-05-23 14:48:10','2024-05-23 14:48:10','签名验签服务器地址,验签时将会调用该地址进行验签'),(114,'验签是否调用签名验签服务','CLOUDKEY.SYSTEM.CALL_SVS','0',1,1,'2024-05-23 14:48:10','2024-05-23 14:48:10','是否调用签名验签服务(1为开启调用, 0为本地验证)'),(115,'归档日志库开始启用时间','CLOUDKEY.SYSTEM.ARCHIVE.AUDIT.LOG.START.TIME','2024-05-23 14:48:11',1,1,'2024-05-23 14:48:11','2024-05-23 14:48:11','归档日志库开始启用时间 不建议修改'),(116,'日志记录请求返回体开关','CLOUDKEY.DATA.LOG_ENABLE','true',1,1,'2024-05-23 14:48:11','2024-05-23 14:48:11','是否记录接口返回值、签名及验签接口中签名原文与签名值，true启用，false不启用'),(117,'日志记录同步异步开关','CLOUDKEY_APP_LOG_SYNC','false',1,1,'2024-05-23 14:48:12','2024-05-23 14:48:12','是否采用同步方式记录日志，true 表示同步记录日志，false异步记录日志'),(118,'是否开启新旧uid共存功能','CLOUDKEY_SYSTEM_UID_ALIAS_OPEN','false',1,1,'2024-05-23 14:48:12','2024-05-23 14:48:12','控制是否启用新旧uid共存功能，true启用，false不启用'),(119,'中大山附一企业微信id','ZDFY-CORP-WECHAT-ID','ww42a71950c4c06c9f',1,1,'2024-05-23 14:48:13','2024-05-23 14:48:13','中大山附一企业微信id，用于中大附一的企业微信登录'),(120,'中大山附一企业微信自建应用id','ZDFY-CORP-WECHAT-AGENT-ID','1000002',1,1,'2024-05-23 14:48:13','2024-05-23 14:48:13','中大山附一企业微信自建应用id，即云密钥系统应用的id，用于中大附一的企业微信登录'),(121,'中大山附一企业微信自建应用secret','ZDFY-CORP-WECHAT-AGENT-SECRET','W08VHsa6-KAEMQqwmraQAYqT_PZy_vUlBY_3ytDcpts',1,1,'2024-05-23 14:48:13','2024-05-23 14:48:13','中大山附一企业微信自建应用id，即云密钥系统应用的secret，用于中大附一的企业微信登录'),(122,'企业微信登录获取accessToken的url','CORP-WECHAT-ACCESS-TOKEN-URL','https://qyapi.weixin.qq.com/cgi-bin/gettoken',1,1,'2024-05-23 14:48:13','2024-05-23 14:48:13','企业微信登录获取accessToken的url'),(123,'企业微信登录获取用户登录信息的url','CORP-WECHAT-GET-USER-INFO-URL','https://qyapi.weixin.qq.com/cgi-bin/auth/getuserinfo',1,1,'2024-05-23 14:48:13','2024-05-23 14:48:13','企业微信登录获取用户登录信息的url'),(124,'企业微信获取code的回调地址','CORP-WECHAT-GET-CODE-REDIRECT-URI','',1,1,'2024-05-23 14:48:13','2024-05-23 14:48:13','企业微信获取code的回调地址'),(125,'云密钥主机地址，需要带上cloudkeyserver','CLOUDKEY.SYSTEM.HOST_URL','https://************:61443/cloudkeyserver',1,1,'2024-05-23 14:48:13','2024-05-23 14:48:13','云密钥主机地址，需要带上cloudkeyserver'),(126,'云密钥企业微信跳转页面host，需要带上cloudkeyserver','CLOUDKEY.SYSTEM.PAGE_URL','https://************:61443/cloudkeyserver',1,1,'2024-05-23 14:48:13','2024-05-23 14:48:13','云密钥企业微信跳转页面host，需要带上cloudkeyserver'),(127,'用户处理开关','CLOUDKEY.SYSTEM.USER_INFO.DESENSITIZATION','false',1,1,'2024-05-23 14:48:14','2024-05-23 14:48:14','用户处理开关 true为开 false为关'),(128,'中大附一企业微信中台appId','ZD-CORP-MID-APP-ID','',1,1,'2024-05-23 14:48:14','2024-05-23 14:48:14','中大山附一企业微信中台appId，用于中大附一的企业微信中台登录'),(129,'中大附一企业微信中台appSecret','ZD-CORP-MID-APP-SECRET','',1,1,'2024-05-23 14:48:14','2024-05-23 14:48:14','中大山附一企业微信中台appSecret，用于中大附一的企业微信中台登录'),(130,'中大附一企业微信中台randomKey','ZD-CORP-MID-RANDOM-KEY','',1,1,'2024-05-23 14:48:14','2024-05-23 14:48:14','中大附一企业微信中台randomKey，用于中大附一的企业微信中台登录'),(131,'中大附一企业微信中台二维码地址','ZD-CORP-MID-QRCODE-URL','https://qwzt.gzsums.net:8090/scg/eagle-appfuse/api/wecomApp/scanQr',1,1,'2024-05-23 14:48:14','2024-05-23 14:48:14','中大山附一企业微信中台二维码地址，用于中大附一的企业微信中台登录'),(132,'中大附一企业微信中台登录获取accessToken的url','ZD-CORP-MID-ACCESS-TOKEN-URL','http://10.168.199.194/scg/eagle-pms/app/auth/getToken',1,1,'2024-05-23 14:48:14','2024-05-23 14:48:14','企业微信登录获取accessToken的url'),(133,'中大附一企业微信中台登录获取用户登录信息的url','ZD-CORP-MID-GET-USER-INFO-URL','http://10.168.199.194/scg/eagle-appfuse/api/wecomApp/getUserInfo',1,1,'2024-05-23 14:48:14','2024-05-23 14:48:14','企业微信登录获取用户登录信息的url'),(134,'中大附一企业微信中台二维码地址','ZD_CORP-MID-QRCODE-URL','https://qwzt.gzsums.net:8090/scg/eagle-appfuse/api/wecomApp/scanQr',1,1,'2024-05-23 14:48:14','2024-05-23 14:48:14','中大山附一企业微信中台二维码地址，用于中大附一的企业微信中台登录'),(135,'业务平台证书密钥更新路径','CLOUDKEY.SYSTEM.BPMS.UPDATE_CERT_URL','certbpms/updateCert4ThirdSys.servlet',1,1,'2024-05-23 14:48:14','2024-05-23 14:48:14','业务平台证书密钥更新路径'),(136,'禁用应用日志开关','CLOUDKEY.APPLICATION.LOG_ENABLE','false',1,1,'2024-05-23 14:48:14','2024-05-23 14:48:14','是否禁用应用日志 true表示禁用应用日志，false表示启用应用日志'),(137,'禁用互联互通日志开关','CLOUDKEY.INTER_CONNECTION.LOG_ENABLE','false',1,1,'2024-05-23 14:48:18','2024-05-23 14:48:18','是否禁用互联互通日志 true表示停止记录日志，false表示允许记录日志'),(138,'禁用第三方系统日志开关','CLOUDKEY.THIRD_SYSTEM.LOG_ENABLE','false',1,1,'2024-05-23 14:48:18','2024-05-23 14:48:18','是否第三方系统日志 true表示停止记录日志，false表示允许记录日志'),(139,'是否开启检验ApplicationId入参','CHECK_APPLICATION_ID','false',1,1,'2024-05-23 14:48:18','2024-05-23 14:48:18','是否所有接口开启检验ApplicationId入参 true表示ApplicationId入参必填，false表示无需必填'),(140,'证书过期预警短信提示语','SMS_SEND_CERT_EXPIRY_WARNING','%{district}共计%{count}份数字证书将于%{day}天内陆续到期，请及时关注并安排续期工作',1,1,'2024-05-23 14:48:18','2024-05-23 14:48:18','证书过期前发送短信通知续期'),(141,'Base64严格模式','CLOUDKEY.SYSTEM.BASE64_STRICT_MODE_ENABLED','true',1,1,'2024-05-23 14:48:18','2024-05-23 14:48:18','是否启用Base64严格模式'),(142,'应用日志保留月份','CLOUDKEY.SYSTEM.APP_LOG_RETAINED_MONTH','-1',1,1,'2024-05-23 14:48:20','2024-05-23 14:48:20','应用日志保留月份 -1 表示永久保存'),(143,'系统名称','CLOUDKEY.SYSTEM.NAME','移动签名系统',1,1,'2025-01-15 15:55:46','2025-01-15 15:55:46','系统名称，重新登陆后生效'),(144,'口令过期时间(天)','CLOUDKEY.SYSTEM.PIN.EXPIRATION.TIME','-1',1,1,'2025-01-15 15:55:58','2025-01-15 15:55:58','口令过期后需重设口令'),(145,'设备过期时间(天)','CLOUDKEY.SYSTEM.DEVICE.EXPIRATION.TIME','-1',1,1,'2025-01-15 15:55:58','2025-01-15 15:55:58','设备过期后需重新激活'),(146,'附属设备个数','CLOUDKEY.SYSTEM.ATTACHED.DEVICE.COUNT','3',1,1,'2025-01-15 15:55:58','2025-01-15 15:55:58','附属设备个数'),(147,'是否开启附属设备功能','CLOUDKEY.SYSTEM.ATTACHED.DEVICE.OPEN','false',1,1,'2025-01-15 15:55:59','2025-01-15 15:55:59','是否开启附属功能, true为开启 false为关闭'),(148,'设备绑定短信提示语','SMS_SEND_DEVICE_BINDING','您的证书（用户名：%{username}）正在申请设备绑定操作：设备类型为%{deviceType}，验证码为%{validCode}，%{expireInterval}分钟内有效，验证码是证书绑定的重要凭证，请妥善保管。',1,1,'2025-01-15 15:55:59','2025-01-15 15:55:59','设备绑定短信提示语'),(149,'电子存证存证参数非空检测开关','CLOUDKEY_EDSS_PARAM_REQUIRED_ENABLE','false',1,1,'2025-01-15 15:56:43','2025-01-15 15:56:43','电子存证存证参数非空检测开关，true 表示要求必填否则异常，false 表示选填。'),(150,'原签名签章接口数据同步到电子存证配置','CLOUDKEY_OLD_SIGNATURE_API_EDSS_STRATEGY_TYPE','1',1,1,'2025-01-15 15:56:43','2025-01-15 15:56:43','原签名签章接口数据同步到电子存证配置，1:表示允许调用并且同步到存证，2：表示接口不允许调用，3：表示允许调用但不同步到存证。'),(151,'延迟初始密码短信至证书下载后发送开关','DELAY_PASSWORD_SMS_AFTER_DOWNLOAD_ENABLE','false',1,1,'2025-01-15 15:56:44','2025-01-15 15:56:44','是否延迟初始密码短信至证书下载后发送 true表示启用，false表示不启用'),(152,'协同签署日志表归档日志库开始启用时间','CLOUDKEY.SYSTEM.BUSINESS.COORD_SIGN_API.LOG.START.TIME','2025-01-15 15:57:01',1,1,'2025-01-15 15:57:01','2025-01-15 15:57:01','协同签署日志表归档日志库开始启用时间 不建议修改'),(153,'协同签名任务默认过期天数','CLOUDKEY.COSIGN.DEFAULT_TASK_EXPIRE_DAY','10',1,1,'2025-01-15 15:57:01','2025-01-15 15:57:01','协同签名任务默认过期天数 默认10天'),(154,'协同签名批量签名的数量限制','CLOUDKEY.COSIGN.BATCH_SIGN_LIMIT_COUNT','30',1,1,'2025-01-15 15:57:01','2025-01-15 15:57:01','协同签名批量签名的数量限制,默认30条。'),(155,'协同签名截至时间提醒天数','CLOUDKEY.COSIGN.TASK_EXPIRE_ALERT_DAY','3',1,1,'2025-01-15 15:57:01','2025-01-15 15:57:01','协同签名即将过期提醒的天数 默认3天内过期的都会告警'),(156,'协同签名默认签名意见','CLOUDKEY.COSIGN.DEFAULT_SIGN_OPINION','同意签名',1,1,'2025-01-15 15:57:01','2025-01-15 15:57:01','协同签名默认签名意见'),(157,'协同签名默认拒绝意见','CLOUDKEY.COSIGN.DEFAULT_REJECT_OPINION','拒绝签名',1,1,'2025-01-15 15:57:01','2025-01-15 15:57:01','协同签名默认拒绝意见'),(158,'禁用协同签名接口日志','CLOUDKEY.COSIGN.API_LOG_ENABLE','0',1,1,'2025-01-15 15:57:02','2025-01-15 15:57:02','是否禁用协同签名接口日志 true表示停止记录日志，false表示允许记录日志'),(159,'系统主题颜色','CLOUDKEY.SYSTEM.THEME','1',1,1,'2025-01-15 15:57:19','2025-01-15 15:57:19','1红 2蓝 3橙 4绿 5紫');
/*!40000 ALTER TABLE `config_key_value` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `config_linkman`
--

DROP TABLE IF EXISTS `config_linkman`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `config_linkman` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `identity_type` int NOT NULL,
  `identity` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `phone` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `email` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `address` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `status` int NOT NULL,
  `gmt_create` datetime NOT NULL,
  `gmt_modified` datetime NOT NULL,
  `district_id` int NOT NULL DEFAULT '1' COMMENT '院区ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `config_linkman`
--

LOCK TABLES `config_linkman` WRITE;
/*!40000 ALTER TABLE `config_linkman` DISABLE KEYS */;
INSERT INTO `config_linkman` VALUES (1,7,'data@bab5e88cea9825ad','张经办','data@69469cf5bb0feee5334ce00375aad3fb','data@a85f63747033b4c8d10c293a325e9af5','data@73cb37c12fb7f29c8318dd904fd3401e',0,'2025-01-15 16:48:13','2025-01-15 16:48:13',1);
/*!40000 ALTER TABLE `config_linkman` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `config_login_rule`
--

DROP TABLE IF EXISTS `config_login_rule`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `config_login_rule` (
  `id` int NOT NULL AUTO_INCREMENT,
  `type` int NOT NULL DEFAULT '1' COMMENT '代表登录错误处理规则。现在用到的是1，代表cycle_time分钟内能尝试多少次；次数大于permit_count次，则锁定用户。',
  `permit_count_each_cycle` int NOT NULL COMMENT '代表cycle_time分钟内能尝试多少次',
  `cycle_time` int NOT NULL COMMENT '每一轮的时间（分钟）',
  `permit_count` int NOT NULL COMMENT '登录错误次数大于permit_count次，则锁定用户',
  `other_condition_json` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '其他情况下用json字符串定义规则',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `gmt_create` datetime NOT NULL,
  `gmt_modified` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `config_login_rule`
--

LOCK TABLES `config_login_rule` WRITE;
/*!40000 ALTER TABLE `config_login_rule` DISABLE KEYS */;
INSERT INTO `config_login_rule` VALUES (1,1,8,60,24,NULL,'初始化登陆规则','2024-05-23 14:47:25','2024-05-23 14:47:25');
/*!40000 ALTER TABLE `config_login_rule` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `config_pin_rule`
--

DROP TABLE IF EXISTS `config_pin_rule`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `config_pin_rule` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '口令规则名称',
  `regex` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '口令校验正则表达式',
  `message` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '口令规则要求提示语',
  `gmt_create` datetime NOT NULL,
  `gmt_modified` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name_UNIQUE` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='PIN 码规则表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `config_pin_rule`
--

LOCK TABLES `config_pin_rule` WRITE;
/*!40000 ALTER TABLE `config_pin_rule` DISABLE KEYS */;
INSERT INTO `config_pin_rule` VALUES (1,'默认强口令规则','^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[^A-Za-z0-9]).{8,}$','英文大写、英文小写、数字、特殊符号各至少一位，总长度至少8位','2024-05-23 14:47:58','2024-05-23 14:47:58');
/*!40000 ALTER TABLE `config_pin_rule` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `config_process`
--

DROP TABLE IF EXISTS `config_process`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `config_process` (
  `id` int NOT NULL AUTO_INCREMENT,
  `process_type` int NOT NULL COMMENT '流程类型',
  `operation_sequence` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '操作顺序',
  `process_platform` int NOT NULL COMMENT '流程对应平台',
  `gmt_create` datetime NOT NULL COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL COMMENT '最近一次更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='流程配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `config_process`
--

LOCK TABLES `config_process` WRITE;
/*!40000 ALTER TABLE `config_process` DISABLE KEYS */;
/*!40000 ALTER TABLE `config_process` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `config_project`
--

DROP TABLE IF EXISTS `config_project`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `config_project` (
  `id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '系统项目ID，按规则随机产生',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `user_login_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户验证登录类型（常量）',
  `bpms_system_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `bpms_project_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务平台配置的模板ID',
  `bpms_business_center_id` int NOT NULL,
  `bpms_cert_template_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '业务平台-证书模板ID',
  `project_uid` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目标识',
  `status` tinyint unsigned NOT NULL DEFAULT '0',
  `cert_user_type` tinyint NOT NULL COMMENT '证书用户类型',
  `cert_algo_type` tinyint NOT NULL COMMENT '证书算法类型',
  `operator_id` int NOT NULL COMMENT '管理员id',
  `operator_modify_id` int DEFAULT NULL,
  `login_rule_id` int NOT NULL,
  `config_linkman_id` bigint DEFAULT NULL COMMENT '关联config_linkman的id',
  `allow_user_make_cert_count` int DEFAULT '1' COMMENT '证书对数。-1—不做限制',
  `default_authorize_count` int NOT NULL DEFAULT '0' COMMENT '默认的授权次数',
  `default_authorize_interval` int NOT NULL DEFAULT '0' COMMENT '默认的授权有效期长度，单位为天',
  `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `gmt_create` datetime NOT NULL,
  `gmt_modified` datetime NOT NULL,
  `user_fingerprint_register_login_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `allow_auto_approve` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否允许自动审核',
  `cert_interval` tinyint NOT NULL DEFAULT '36' COMMENT '证书有效期间隔',
  `cert_interval_unit` tinyint(1) NOT NULL DEFAULT '2' COMMENT '证书有效期间隔单位，1表示天，2表示月',
  `face_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目支持的人脸识别模式',
  `department_id` int DEFAULT NULL,
  `approve_level` tinyint unsigned NOT NULL,
  `allow_auto_review_seal_pic` tinyint(1) NOT NULL,
  `limit_bind_device` tinyint NOT NULL,
  `sms_auth_mode` tinyint(1) NOT NULL COMMENT '被授权人发送短信格式',
  `send_pic_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `auth_check_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `enable_pin_rule` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否开启强密码校验',
  `pin_rule_id` int NOT NULL DEFAULT '0' COMMENT '口令规则id',
  `renewal_cert_interval` int NOT NULL DEFAULT '90' COMMENT '过期前多久可续期证书，单位：天',
  `sms_platform` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '短信平台，如果没有配置，则发送短信时使用阿里云平台短信进行发送',
  `business_info_notify_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '业务消息推送地址(方便在系统上进行配置，和不同项目可以配置不同的推送地址)',
  `business_info_notify_cert` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '业务消息推送加密证书',
  `allow_api_upload_seal_pic` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否允许接口上传签章图片',
  `event_cert_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '事件证书项目标志',
  `occupation_id` int DEFAULT NULL COMMENT '职业信息编号',
  `max_authorize_count` int NOT NULL DEFAULT '0' COMMENT '授权的最大个数 0表示不限制',
  `authorize_check_level` int DEFAULT '0' COMMENT '被授权人检验等级 0 不检验 1检验是否为相同职业',
  `user_push_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '用户信息推送URL',
  `district_id` int NOT NULL DEFAULT '1' COMMENT '院区ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `config_project`
--

LOCK TABLES `config_project` WRITE;
/*!40000 ALTER TABLE `config_project` DISABLE KEYS */;
/*!40000 ALTER TABLE `config_project` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `corp_wechat_user`
--

DROP TABLE IF EXISTS `corp_wechat_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `corp_wechat_user` (
  `id` int NOT NULL AUTO_INCREMENT,
  `corp_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户所属企业的corpid',
  `corp_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户在企业内的UserID，如果该企业与第三方应用没有授权关系时，返回密文UserId，有授权关系时，按照升级后的ID策略返回明文或密文',
  `open_user_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '全局唯一。对于同一个服务商，不同应用获取到企业内同一个成员的open_userid是相同的，最多64个字节。仅第三方应用可获取',
  `business_cert_id` int NOT NULL COMMENT '云密钥用户证书id',
  `enc_key` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `iv` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `gmt_create` datetime(6) NOT NULL COMMENT '创建时间',
  `gmt_modify` datetime(6) NOT NULL COMMENT '最后修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_corp_user` (`corp_id`,`corp_user_id`),
  KEY `idx_business_cert_id` (`business_cert_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='企业微信用户绑定信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `corp_wechat_user`
--

LOCK TABLES `corp_wechat_user` WRITE;
/*!40000 ALTER TABLE `corp_wechat_user` DISABLE KEYS */;
/*!40000 ALTER TABLE `corp_wechat_user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `district`
--

DROP TABLE IF EXISTS `district`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `district` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '院区ID，自增',
  `code` varchar(64) NOT NULL COMMENT '院区编号',
  `name` varchar(64) NOT NULL COMMENT '院区名称',
  `gmt_create` datetime NOT NULL COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `code` (`code`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='院区表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `district`
--

LOCK TABLES `district` WRITE;
/*!40000 ALTER TABLE `district` DISABLE KEYS */;
INSERT INTO `district` VALUES (1,'default','本部院区','2024-05-23 14:48:17','2024-05-23 14:48:17');
/*!40000 ALTER TABLE `district` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `history_info`
--

DROP TABLE IF EXISTS `history_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `history_info` (
  `id` int NOT NULL AUTO_INCREMENT,
  `type` int NOT NULL COMMENT '历史数据表名（类型）',
  `data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '历史数据',
  `status` int NOT NULL COMMENT '状态',
  `gmt_create` datetime NOT NULL,
  `gmt_modified` datetime NOT NULL,
  `origin_id` int NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='历史数据信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `history_info`
--

LOCK TABLES `history_info` WRITE;
/*!40000 ALTER TABLE `history_info` DISABLE KEYS */;
/*!40000 ALTER TABLE `history_info` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `history_user`
--

DROP TABLE IF EXISTS `history_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `history_user` (
  `id` int NOT NULL AUTO_INCREMENT,
  `old_user_id` int NOT NULL,
  `uid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户唯一标识符',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `province` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `city` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '城市',
  `official_residence` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `country_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `gender` tinyint(1) DEFAULT NULL,
  `identity_type` int NOT NULL,
  `identity` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `make_cert_status` int NOT NULL,
  `make_cert_count` int NOT NULL,
  `status` int NOT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `organization_id` int DEFAULT NULL,
  `cloudkey_project_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `gmt_create` datetime NOT NULL,
  `gmt_modified` datetime NOT NULL,
  `cert_user_type` tinyint DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `history_user`
--

LOCK TABLES `history_user` WRITE;
/*!40000 ALTER TABLE `history_user` DISABLE KEYS */;
/*!40000 ALTER TABLE `history_user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `inter_connection_log`
--

DROP TABLE IF EXISTS `inter_connection_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inter_connection_log` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `application_id` varchar(64) DEFAULT NULL COMMENT '应用id',
  `application_name` varchar(64) DEFAULT NULL COMMENT '应用名',
  `operation_type` int NOT NULL COMMENT '操作类型',
  `request_uri` varchar(255) NOT NULL COMMENT '请求uri',
  `project_id` varchar(128) DEFAULT NULL COMMENT '项目id',
  `uid` varchar(255) DEFAULT NULL COMMENT '用户uid',
  `user_name` varchar(255) NOT NULL COMMENT '用户名',
  `authorized_user_id` int DEFAULT NULL COMMENT '被授权用户id 如果非空表示被授权用户进行的操作',
  `response_code` varchar(32) DEFAULT NULL COMMENT '响应状态码',
  `response_message` text COMMENT '响应信息',
  `error_message` text COMMENT '异常信息',
  `request_time` datetime DEFAULT NULL COMMENT '请求时间',
  `response_time` datetime DEFAULT NULL COMMENT '响应时间',
  `gmt_create` datetime NOT NULL COMMENT '日志记录时间',
  PRIMARY KEY (`id`),
  KEY `idx_request_time` (`request_time`) USING BTREE COMMENT '请求时间索引，用于排序'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `inter_connection_log`
--

LOCK TABLES `inter_connection_log` WRITE;
/*!40000 ALTER TABLE `inter_connection_log` DISABLE KEYS */;
/*!40000 ALTER TABLE `inter_connection_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `inter_connection_log_type`
--

DROP TABLE IF EXISTS `inter_connection_log_type`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inter_connection_log_type` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `desc` varchar(255) NOT NULL COMMENT '接口操作类型描述',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `inter_connection_log_type`
--

LOCK TABLES `inter_connection_log_type` WRITE;
/*!40000 ALTER TABLE `inter_connection_log_type` DISABLE KEYS */;
INSERT INTO `inter_connection_log_type` VALUES (1,'登录'),(2,'签名'),(3,'签章');
/*!40000 ALTER TABLE `inter_connection_log_type` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `login_info`
--

DROP TABLE IF EXISTS `login_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `login_info` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_type` int NOT NULL COMMENT '用户类型',
  `cert_id` int NOT NULL COMMENT '证书id',
  `authorized_user_id` int DEFAULT NULL COMMENT '被授权用户id',
  `user_token` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'usertoken',
  `login_uuid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '登陆信息的uuid',
  `application_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '应用表的application_id',
  `status` int NOT NULL COMMENT '登陆信息状态',
  `gmt_login` datetime NOT NULL,
  `device_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '设备id',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_token` (`user_token`),
  UNIQUE KEY `login_uuid` (`login_uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='记录登陆信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `login_info`
--

LOCK TABLES `login_info` WRITE;
/*!40000 ALTER TABLE `login_info` DISABLE KEYS */;
/*!40000 ALTER TABLE `login_info` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mobile_split_key`
--

DROP TABLE IF EXISTS `mobile_split_key`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `mobile_split_key` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '编号',
  `key_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '主密钥Id',
  `status` int DEFAULT NULL COMMENT '主密钥使用状态',
  `gmt_create` datetime DEFAULT NULL COMMENT '创建时间',
  `gmt_modify` datetime DEFAULT NULL COMMENT '修改时间',
  `split_key_encrypt` blob COMMENT '加密后的d2',
  `public_key` blob COMMENT '移动主密钥公钥',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mobile_split_key`
--

LOCK TABLES `mobile_split_key` WRITE;
/*!40000 ALTER TABLE `mobile_split_key` DISABLE KEYS */;
/*!40000 ALTER TABLE `mobile_split_key` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `notification_push_register`
--

DROP TABLE IF EXISTS `notification_push_register`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `notification_push_register` (
  `id` int NOT NULL AUTO_INCREMENT,
  `type` int NOT NULL COMMENT '注册者类型',
  `user_type` int NOT NULL COMMENT '用户类型',
  `cert_id` int NOT NULL COMMENT '证书id',
  `authorized_user_id` int DEFAULT NULL COMMENT '被授权用户id',
  `device_token` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '设备的devicetoken',
  `status` int NOT NULL COMMENT '状态',
  `gmt_create` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='消息推送注册表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `notification_push_register`
--

LOCK TABLES `notification_push_register` WRITE;
/*!40000 ALTER TABLE `notification_push_register` DISABLE KEYS */;
/*!40000 ALTER TABLE `notification_push_register` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `pending_seal_pic`
--

DROP TABLE IF EXISTS `pending_seal_pic`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pending_seal_pic` (
  `id` int NOT NULL AUTO_INCREMENT,
  `cert_id` int NOT NULL,
  `pic_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '签章图片Base64编码文本',
  `pic_filename` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '签章图片文件名',
  `pic_store_type` int DEFAULT NULL COMMENT '签章图片存储类型，1：base64存储，2：文件路径url存储',
  `pic_digest` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `review_status` tinyint NOT NULL,
  `review_memo` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `gmt_create` datetime NOT NULL,
  `gmt_modified` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `cert_id_UNIQUE` (`cert_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='待审核签章图片表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `pending_seal_pic`
--

LOCK TABLES `pending_seal_pic` WRITE;
/*!40000 ALTER TABLE `pending_seal_pic` DISABLE KEYS */;
/*!40000 ALTER TABLE `pending_seal_pic` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `quartz_job_details`
--

DROP TABLE IF EXISTS `quartz_job_details`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `quartz_job_details` (
  `id` int NOT NULL AUTO_INCREMENT,
  `scheduler_name` varchar(128) NOT NULL COMMENT '调度器名',
  `job_group` varchar(128) NOT NULL COMMENT '任务组',
  `job_name` varchar(128) NOT NULL COMMENT '任务名',
  `status` int NOT NULL COMMENT '状态',
  `job_class_name` varchar(255) NOT NULL COMMENT '完成调度任务的类的完全限定类名',
  `description` varchar(255) DEFAULT NULL COMMENT '任务描述',
  `cron` varchar(32) NOT NULL COMMENT 'cron表达式',
  `last_fire_time` datetime DEFAULT NULL COMMENT '上一次执行任务的时间',
  `next_fire_time` datetime NOT NULL COMMENT '下一次执行任务的时间',
  `job_data` text COMMENT '任务数据',
  `gmt_create` datetime NOT NULL COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL COMMENT '最后修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `quartz_job_details`
--

LOCK TABLES `quartz_job_details` WRITE;
/*!40000 ALTER TABLE `quartz_job_details` DISABLE KEYS */;
INSERT INTO `quartz_job_details` VALUES (1,'cloudKeyScheduler','cert_reserve_task','cert_revoke',1,'net.netca.cloudkeyserver.schedule.quartz.job.CertRevokeJob',NULL,'0 0 1-6 * * ?','2025-01-15 16:10:38','2025-01-16 01:00:00','','2024-05-23 14:48:02','2025-01-15 16:10:38'),(3,'cloudKeyScheduler','down_cert','down_cert',1,'net.netca.cloudkeyserver.schedule.quartz.job.DownCertJob',NULL,'0 0 * * * ?','2025-01-15 17:00:00','2025-01-15 18:00:00','','2024-05-23 14:48:03','2025-01-15 17:00:00');
/*!40000 ALTER TABLE `quartz_job_details` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `rel_authorized_user_login_mobile_info`
--

DROP TABLE IF EXISTS `rel_authorized_user_login_mobile_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `rel_authorized_user_login_mobile_info` (
  `id` int NOT NULL AUTO_INCREMENT,
  `login_mobile_info_id` int NOT NULL COMMENT '手机设备信息表id',
  `authorized_user_id` int NOT NULL COMMENT '授权用户id',
  PRIMARY KEY (`id`),
  UNIQUE KEY `login_mobile_info_id` (`login_mobile_info_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='授权用户和手机设备信息关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `rel_authorized_user_login_mobile_info`
--

LOCK TABLES `rel_authorized_user_login_mobile_info` WRITE;
/*!40000 ALTER TABLE `rel_authorized_user_login_mobile_info` DISABLE KEYS */;
/*!40000 ALTER TABLE `rel_authorized_user_login_mobile_info` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `rel_authorized_user_login_separating_key_info`
--

DROP TABLE IF EXISTS `rel_authorized_user_login_separating_key_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `rel_authorized_user_login_separating_key_info` (
  `id` int NOT NULL AUTO_INCREMENT,
  `authorized_user_id` int NOT NULL COMMENT '授权用户id',
  `login_separating_key_info_id` int NOT NULL COMMENT '分割密钥信息表id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='授权用户和分割密钥信息关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `rel_authorized_user_login_separating_key_info`
--

LOCK TABLES `rel_authorized_user_login_separating_key_info` WRITE;
/*!40000 ALTER TABLE `rel_authorized_user_login_separating_key_info` DISABLE KEYS */;
/*!40000 ALTER TABLE `rel_authorized_user_login_separating_key_info` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `rel_cert_login_mobile_info`
--

DROP TABLE IF EXISTS `rel_cert_login_mobile_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `rel_cert_login_mobile_info` (
  `id` int NOT NULL AUTO_INCREMENT,
  `login_mobile_info_id` int NOT NULL COMMENT '手机设备信息表id',
  `cert_id` int NOT NULL COMMENT '证书表id',
  `device_type` int NOT NULL DEFAULT '1' COMMENT '设备类型 0 未知设备 1主设备 2附属设备',
  PRIMARY KEY (`id`),
  KEY `idx_cert_id` (`cert_id`),
  KEY `rel_cert_login_mobile_info_cert_id_index` (`cert_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='证书和手机设备信息关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `rel_cert_login_mobile_info`
--

LOCK TABLES `rel_cert_login_mobile_info` WRITE;
/*!40000 ALTER TABLE `rel_cert_login_mobile_info` DISABLE KEYS */;
/*!40000 ALTER TABLE `rel_cert_login_mobile_info` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `rel_cert_login_separating_key_info`
--

DROP TABLE IF EXISTS `rel_cert_login_separating_key_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `rel_cert_login_separating_key_info` (
  `id` int NOT NULL AUTO_INCREMENT,
  `cert_id` int NOT NULL COMMENT '证书表id',
  `login_separating_key_info_id` int NOT NULL COMMENT '分割密钥信息表id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='证书用户和分割密钥信息关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `rel_cert_login_separating_key_info`
--

LOCK TABLES `rel_cert_login_separating_key_info` WRITE;
/*!40000 ALTER TABLE `rel_cert_login_separating_key_info` DISABLE KEYS */;
/*!40000 ALTER TABLE `rel_cert_login_separating_key_info` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `rel_district_authority_operator`
--

DROP TABLE IF EXISTS `rel_district_authority_operator`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `rel_district_authority_operator` (
  `id` int NOT NULL AUTO_INCREMENT,
  `district_id` int DEFAULT '1' COMMENT '院区ID',
  `operator_id` int DEFAULT '0' COMMENT '操作员用户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='系统用户和院区的关联关系表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `rel_district_authority_operator`
--

LOCK TABLES `rel_district_authority_operator` WRITE;
/*!40000 ALTER TABLE `rel_district_authority_operator` DISABLE KEYS */;
/*!40000 ALTER TABLE `rel_district_authority_operator` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `rel_operator_right`
--

DROP TABLE IF EXISTS `rel_operator_right`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `rel_operator_right` (
  `id` int NOT NULL AUTO_INCREMENT,
  `operator_id` int NOT NULL,
  `right` tinyint unsigned NOT NULL COMMENT '权力',
  `gmt_create` datetime NOT NULL,
  `gmt_modified` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='权限表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `rel_operator_right`
--

LOCK TABLES `rel_operator_right` WRITE;
/*!40000 ALTER TABLE `rel_operator_right` DISABLE KEYS */;
/*!40000 ALTER TABLE `rel_operator_right` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `rel_organization_project`
--

DROP TABLE IF EXISTS `rel_organization_project`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `rel_organization_project` (
  `id` int NOT NULL AUTO_INCREMENT,
  `organization_id` int NOT NULL COMMENT '机构ID',
  `project_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='机构-项目关系表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `rel_organization_project`
--

LOCK TABLES `rel_organization_project` WRITE;
/*!40000 ALTER TABLE `rel_organization_project` DISABLE KEYS */;
/*!40000 ALTER TABLE `rel_organization_project` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `rel_third_system_interface`
--

DROP TABLE IF EXISTS `rel_third_system_interface`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `rel_third_system_interface` (
  `id` int NOT NULL AUTO_INCREMENT,
  `third_system_configure_id` int NOT NULL COMMENT '第三方系统id',
  `third_system_interface_id` int NOT NULL COMMENT '外部接口id',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='第三方系统允许访问的接口关联表，在表中有记录的接口才允许外部系统访问。';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `rel_third_system_interface`
--

LOCK TABLES `rel_third_system_interface` WRITE;
/*!40000 ALTER TABLE `rel_third_system_interface` DISABLE KEYS */;
/*!40000 ALTER TABLE `rel_third_system_interface` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `seal_pic`
--

DROP TABLE IF EXISTS `seal_pic`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `seal_pic` (
  `id` int NOT NULL AUTO_INCREMENT,
  `cert_id` int DEFAULT NULL,
  `pic_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '签章图片Base64编码文本',
  `pic_filename` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '签章图片文件名',
  `pic_store_type` int DEFAULT NULL COMMENT '签章图片存储类型，1：base64存储，2：文件路径url存储',
  `gmt_create` datetime NOT NULL,
  `gmt_modified` datetime NOT NULL,
  `user_id` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `cert_id_UNIQUE` (`cert_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `seal_pic`
--

LOCK TABLES `seal_pic` WRITE;
/*!40000 ALTER TABLE `seal_pic` DISABLE KEYS */;
/*!40000 ALTER TABLE `seal_pic` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `seal_sign_info`
--

DROP TABLE IF EXISTS `seal_sign_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `seal_sign_info` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '编号',
  `request_time` datetime(3) NOT NULL COMMENT '请求时间',
  `response_time` datetime(3) NOT NULL COMMENT '响应时间',
  `pdf_data_sha256` char(64) DEFAULT NULL COMMENT '签章数据sha256摘要',
  `cert_cn` varchar(255) DEFAULT NULL COMMENT '证书主题CN项',
  `cert_sn` varchar(64) DEFAULT NULL COMMENT '证书序列号',
  `project_id` varchar(128) DEFAULT NULL COMMENT '项目id',
  `success` tinyint NOT NULL COMMENT '是否成功',
  `error_message` longtext COMMENT '失败信息',
  `gmt_create` datetime(3) NOT NULL COMMENT '记录创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_project_id` (`project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='签章统计表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `seal_sign_info`
--

LOCK TABLES `seal_sign_info` WRITE;
/*!40000 ALTER TABLE `seal_sign_info` DISABLE KEYS */;
/*!40000 ALTER TABLE `seal_sign_info` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `service_info`
--

DROP TABLE IF EXISTS `service_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `service_info` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务名称',
  `content` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务地址内容',
  `prior_flag` int NOT NULL COMMENT '是否优先展示 1是 0否',
  `gmt_create` datetime NOT NULL,
  `gmt_modified` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='服务配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `service_info`
--

LOCK TABLES `service_info` WRITE;
/*!40000 ALTER TABLE `service_info` DISABLE KEYS */;
/*!40000 ALTER TABLE `service_info` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sms_log`
--

DROP TABLE IF EXISTS `sms_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sms_log` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '(索引)短信接收者手机号',
  `sms_platform` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '短信平台(参考net.netca.common.notice.constants.SupportSmsTypeEnum 的枚举常量名)',
  `notice_request_type` int NOT NULL COMMENT '短信类型，参考NoticeRequestTypeEnum',
  `template_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '短信模板内容',
  `template_param_map` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '短信模板变量map json字符串',
  `is_request_success` int DEFAULT NULL COMMENT '是否请求成功1代表成功0代表失败; 只表示短信平台成功接受了发送短信的请求，不代表短信平台成功发送了短息和用户是否接受到短信',
  `response_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '短信平台响应状态码',
  `response_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '短信平台响应状态码描述',
  `response_message` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '短信平台响应状态报文',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '访问短信平台发生异常时的异常信息',
  `uid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '(索引)短信接收受者-用户uid',
  `authorized_user_uid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '(索引)短信接收者-被授权用户uid',
  `project_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '短信接收者-项目id',
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '短信接收者-用户名',
  `request_time` datetime NOT NULL COMMENT '请求短信平台的时间',
  `response_time` datetime DEFAULT NULL COMMENT '短信平台响应时间',
  PRIMARY KEY (`id`),
  KEY `idx_phone` (`phone`) USING BTREE,
  KEY `idx_uid` (`uid`) USING BTREE,
  KEY `idx_authorized_user_id` (`authorized_user_uid`) USING BTREE,
  KEY `idx_request_time` (`request_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='短信日志记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sms_log`
--

LOCK TABLES `sms_log` WRITE;
/*!40000 ALTER TABLE `sms_log` DISABLE KEYS */;
/*!40000 ALTER TABLE `sms_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `software_update_config`
--

DROP TABLE IF EXISTS `software_update_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `software_update_config` (
  `id` int NOT NULL AUTO_INCREMENT,
  `platform_type` int NOT NULL COMMENT '平台类型',
  `client_version_minimum` varchar(16) NOT NULL COMMENT '最低可用版本号',
  `client_version_latest` varchar(16) NOT NULL COMMENT '最新可用版本号',
  `url` varchar(256) NOT NULL COMMENT 'url',
  `gmt_create` datetime NOT NULL,
  `gmt_modified` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='更新配置';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `software_update_config`
--

LOCK TABLES `software_update_config` WRITE;
/*!40000 ALTER TABLE `software_update_config` DISABLE KEYS */;
INSERT INTO `software_update_config` VALUES (3,0,'-1','-1','-1','2024-05-23 14:47:46','2024-05-23 14:47:46'),(4,1,'-1','-1','-1','2024-05-23 14:47:46','2024-05-23 14:47:46');
/*!40000 ALTER TABLE `software_update_config` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `third_system_configure`
--

DROP TABLE IF EXISTS `third_system_configure`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `third_system_configure` (
  `id` int NOT NULL AUTO_INCREMENT,
  `auth_id` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '权限编号',
  `project_ids` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目编号，json数组',
  `sign_cert_content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '签名证书内容',
  `cert_sn` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '证书序列号',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态，0：禁止，1：正常',
  `gmt_create` datetime NOT NULL COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL COMMENT '最后一次修改时间',
  `district_id` int NOT NULL DEFAULT '1' COMMENT '院区ID',
  `coord_push_url` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='云密钥第三方系统配置';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `third_system_configure`
--

LOCK TABLES `third_system_configure` WRITE;
/*!40000 ALTER TABLE `third_system_configure` DISABLE KEYS */;
/*!40000 ALTER TABLE `third_system_configure` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `third_system_interface`
--

DROP TABLE IF EXISTS `third_system_interface`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `third_system_interface` (
  `id` int NOT NULL,
  `name` varchar(64) NOT NULL COMMENT '接口名',
  `uri` varchar(512) DEFAULT NULL COMMENT '接口地址',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `third_system_interface`
--

LOCK TABLES `third_system_interface` WRITE;
/*!40000 ALTER TABLE `third_system_interface` DISABLE KEYS */;
INSERT INTO `third_system_interface` VALUES (1,'机构信息注册','/cloudkeyserver/system/interface/org/register/1CKV11'),(2,'用户信息注册','/cloudkeyserver/system/interface/user/register/1CKV11'),(3,'用户解锁','/cloudkeyserver/system/interface/user/unlock/2CKV6'),(4,'上传签章图片','/cloudkeyserver/system/interface/seal/certcontent/uploadsealpic/2CKV7'),(5,'证书续期','/cloudkeyserver/system/interface/user/certcontent/renewal/2CKV8'),(6,'证书注销','/cloudkeyserver/system/interface/user/certcontent/revoke/2CKV11'),(7,'证书冻结','/cloudkeyserver/system/interface/user/certcontent/status/freeze/2CKV15'),(8,'证书解冻','/cloudkeyserver/system/interface/user/certcontent/status/unfreeze/2CKV15'),(9,'事件证书签发','/cloudkeyserver/system/interface/eventCert/apply/2CKV23'),(10,'事件证书签发并签名','/cloudkeyserver/system/interface/eventCert/applyAndSign/2CKV23'),(11,'事件证书签名接口','/cloudkeyserver/system/interface/eventCert/sign/2CKV23'),(12,'用户信息修改接口','/cloudkeyserver/system/interface/user/update/2CKV32'),(13,'证书预约取消','/cloudkeyserver/system/interface/user/certcontent/reserve/cancel/2CKV34'),(14,'证书列表查询','/cloudkeyserver/system/interface/cert/list/2CKV34'),(15,'第三方系统日志查询','/cloudkeyserver/system/interface/thirdSystemLog/page'),(5001,'创建/编辑文本协同签署任务接口','/cloudkeyserver/system/interface/coord-sign/textTask/submit'),(5002,'取消协同签署任务接口','/cloudkeyserver/system/interface/coord-sign/task/cancel'),(5003,'查询签署任务接口','/cloudkeyserver/system/interface/coord-sign/task/query'),(5005,'签署者待签任务列表接口','/cloudkeyserver/system/interface/coord-sign/task/unsigned/query'),(5006,'创建/编辑签章协同签署任务接口','/cloudkeyserver/system/interface/coord-sign/fileTask/submit'),(5007,'创建/编辑文本协同签署任务接口（模板方式）','/cloudkeyserver/system/interface/coord-sign/template/textTask/submit'),(5008,'创建/编辑签章协同签署任务接口（模板方式）','/cloudkeyserver/system/interface/coord-sign/template/fileTask/submit'),(5009,'发起协同签署任务接口','/cloudkeyserver/system/interface/coord-sign/task/commit'),(5010,'创建/编辑协同签署模板接口','/cloudkeyserver/system/interface/coord-sign/template/create'),(5011,'查看协同签署模板详情接口','/cloudkeyserver/system/interface/coord-sign/template/query'),(5012,'获取流程图查看令牌接口','/cloudkeyserver/system/interface/coord-sign/diagram');
/*!40000 ALTER TABLE `third_system_interface` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `third_system_log`
--

DROP TABLE IF EXISTS `third_system_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `third_system_log` (
  `id` int NOT NULL AUTO_INCREMENT,
  `auth_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '第三方系统认证id',
  `direction` tinyint(1) NOT NULL COMMENT '调用方向，0作为服务端，1作为客户端',
  `success` tinyint(1) NOT NULL COMMENT '调用结果，0表示成功，-1表示失败',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '服务端，表示被调用的url；客户端，表示目标的url',
  `client_ip_addr` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '客户端请求',
  `client_ip_port` int DEFAULT NULL COMMENT '客户端请求端口',
  `type` smallint NOT NULL COMMENT '操作类型',
  `method_invoker` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '调用方法全类名',
  `request_decode` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '请求参数值的原文',
  `request_signature` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '请求参数值的签名值',
  `request_content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '请求内容',
  `request_start` datetime DEFAULT NULL COMMENT '请求开始时间',
  `request_end` datetime DEFAULT NULL COMMENT '请求结束时间',
  `response_content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '响应内容',
  `memo` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '备注信息',
  `duration` bigint DEFAULT NULL COMMENT '接口耗时时长，单位：毫秒',
  `district_id` int NOT NULL DEFAULT '1' COMMENT '院区ID',
  `attachment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '第三方系统附件信息',
  PRIMARY KEY (`id`),
  KEY `idx_req_time_type` (`request_start`,`type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='与第三方系统通信日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `third_system_log`
--

LOCK TABLES `third_system_log` WRITE;
/*!40000 ALTER TABLE `third_system_log` DISABLE KEYS */;
/*!40000 ALTER TABLE `third_system_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_face_img_data`
--

DROP TABLE IF EXISTS `user_face_img_data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_face_img_data` (
  `id` int NOT NULL AUTO_INCREMENT,
  `img_data` mediumblob NOT NULL COMMENT '人脸图片数据',
  `img_data_hash_algo` int NOT NULL COMMENT '人脸图片摘要算法（中间件常量）',
  `img_data_hash` blob NOT NULL COMMENT '人脸图片数据摘要值',
  `user_face_info_id` int NOT NULL COMMENT 'user_face_info的主键ID',
  `gmt_create` datetime NOT NULL,
  `gmt_modify` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_face_img_data`
--

LOCK TABLES `user_face_img_data` WRITE;
/*!40000 ALTER TABLE `user_face_img_data` DISABLE KEYS */;
/*!40000 ALTER TABLE `user_face_img_data` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_face_info`
--

DROP TABLE IF EXISTS `user_face_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_face_info` (
  `id` int NOT NULL AUTO_INCREMENT,
  `face_type` tinyint(1) NOT NULL,
  `status` tinyint(1) NOT NULL,
  `gmt_create` datetime NOT NULL,
  `gmt_modify` datetime NOT NULL,
  `face_mode` int NOT NULL COMMENT '人脸识别模式',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_face_info`
--

LOCK TABLES `user_face_info` WRITE;
/*!40000 ALTER TABLE `user_face_info` DISABLE KEYS */;
/*!40000 ALTER TABLE `user_face_info` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_login_mobile_info`
--

DROP TABLE IF EXISTS `user_login_mobile_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_login_mobile_info` (
  `id` int NOT NULL AUTO_INCREMENT,
  `device_login_type_bit_value` int NOT NULL COMMENT '支持的设备验证类型--位掩码来表示',
  `device_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '设备ID（手机端产生-唯一）',
  `user_type` tinyint NOT NULL COMMENT '属于证书用户，还是属于被授权用户',
  `user_mobile_publickey_id` int DEFAULT NULL COMMENT '用户指纹表主键ID，可以为空，当device_login_type_bit_value不支持指纹时',
  `user_face_info_id` int DEFAULT NULL COMMENT '人脸信息Id',
  `status` tinyint NOT NULL,
  `device_info` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '设备信息，字符串',
  `gmt_create` datetime NOT NULL,
  `gmt_modify` datetime NOT NULL,
  `device_type` int NOT NULL DEFAULT '1' COMMENT '设备类型 0 未知设备 1主设备 2附属设备',
  `last_active_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后一次活跃时间',
  `device_model` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '未知设备' COMMENT '设备型号',
  PRIMARY KEY (`id`),
  KEY `idx_device_id` (`device_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_login_mobile_info`
--

LOCK TABLES `user_login_mobile_info` WRITE;
/*!40000 ALTER TABLE `user_login_mobile_info` DISABLE KEYS */;
/*!40000 ALTER TABLE `user_login_mobile_info` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_login_separating_key_info`
--

DROP TABLE IF EXISTS `user_login_separating_key_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_login_separating_key_info` (
  `id` int NOT NULL AUTO_INCREMENT,
  `status` tinyint NOT NULL COMMENT '状态 0 正常 2 删除',
  `lock_status` tinyint NOT NULL COMMENT '锁定状态 0 正常 1 锁定',
  `sign_keypair_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '协作签名密钥对ID',
  `user_type` tinyint NOT NULL COMMENT '属于证书用户，还是属于被授权用户',
  `d2` blob NOT NULL COMMENT '分割密钥的d2',
  `gmt_create` datetime NOT NULL,
  `gmt_modify` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_login_separating_key_info_sign_keypair_id` (`sign_keypair_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='分割密钥信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_login_separating_key_info`
--

LOCK TABLES `user_login_separating_key_info` WRITE;
/*!40000 ALTER TABLE `user_login_separating_key_info` DISABLE KEYS */;
/*!40000 ALTER TABLE `user_login_separating_key_info` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_mobile_publickey`
--

DROP TABLE IF EXISTS `user_mobile_publickey`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_mobile_publickey` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '编号',
  `cpu_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `uid` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '手机APP的uid',
  `counter` int DEFAULT NULL,
  `raw` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `fid` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `tee_n` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `tee_v` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `fp_n` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `fp_v` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `type` int NOT NULL,
  `ask_pub_key` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `authkey_pub_key` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `user_id` int DEFAULT NULL,
  `authorized_user_id` int DEFAULT NULL,
  `usage_type` tinyint(1) DEFAULT NULL COMMENT '用途类型：0=不需要验证deviceId；1=指定的deviceId才可以使用',
  `cert_id` int DEFAULT NULL,
  `gmt_create` datetime NOT NULL ON UPDATE CURRENT_TIMESTAMP,
  `gmt_modify` datetime NOT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_mobile_publickey`
--

LOCK TABLES `user_mobile_publickey` WRITE;
/*!40000 ALTER TABLE `user_mobile_publickey` DISABLE KEYS */;
/*!40000 ALTER TABLE `user_mobile_publickey` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_tencent_face_data`
--

DROP TABLE IF EXISTS `user_tencent_face_data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_tencent_face_data` (
  `id` int NOT NULL AUTO_INCREMENT,
  `face_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '人脸照片 base64编码',
  `user_type` tinyint NOT NULL COMMENT '属于证书用户，还是属于被授权用户',
  `cert_id` int DEFAULT NULL COMMENT '证书id',
  `authorized_user_id` int DEFAULT NULL COMMENT '被授权用户id',
  `user_pin` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '经过加密的用户PIN',
  `gmt_create` datetime NOT NULL,
  `gmt_modify` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='人脸照片信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_tencent_face_data`
--

LOCK TABLES `user_tencent_face_data` WRITE;
/*!40000 ALTER TABLE `user_tencent_face_data` DISABLE KEYS */;
/*!40000 ALTER TABLE `user_tencent_face_data` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_uid_alias`
--

DROP TABLE IF EXISTS `user_uid_alias`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_uid_alias` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL COMMENT '用户id',
  `uid_alias` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '新uid',
  `gmt_create` datetime NOT NULL COMMENT '创建时间',
  `gmt_modified` datetime NOT NULL COMMENT '最后修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='uid别名';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_uid_alias`
--

LOCK TABLES `user_uid_alias` WRITE;
/*!40000 ALTER TABLE `user_uid_alias` DISABLE KEYS */;
/*!40000 ALTER TABLE `user_uid_alias` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-01-15 17:34:55
