#!/bin/bash
# MySQL服务卸载脚本
# 负责清理MySQL容器、数据和相关配置

# 使用说明
show_help() {
  echo "用法: $0 <产品名称> [master1|master2] [--remove-data|--keep-data]"
  echo ""
  echo "参数:"
  echo "  <产品名称>        必须，例如：cloudkey"
  echo "  [master1|master2] 可选，指定要卸载的节点，不指定则卸载所有节点"
  echo "  --remove-data     可选，卸载时删除所有数据目录"
  echo "  --keep-data       可选，卸载时保留数据目录（默认）"
  echo ""
  echo "选项:"
  echo "  -h, --help        显示此帮助信息"
  echo ""
  echo "示例:"
  echo "  $0 cloudkey            # 卸载cloudkey产品的所有MySQL节点，保留数据"
  echo "  $0 cloudkey master1    # 只卸载cloudkey产品的master1节点，保留数据"
  echo "  $0 cloudkey --remove-data # 卸载cloudkey产品的所有MySQL节点并删除数据"
  exit 0
}

# 首先检查是否请求帮助
for arg in "$@"; do
  case "$arg" in
    -h|--help)
      show_help
      ;;
  esac
done

# 导入通用函数和路径
source $(dirname "$0")/common.sh || { echo "无法导入common.sh"; exit 1; }

# 设置默认值
DATA_RETENTION="keep"
MASTER1_COMPOSE_FOUND=false
MASTER2_COMPOSE_FOUND=false

# 在文件开头添加 Docker Compose 命令变量
DOCKER_COMPOSE_CMD=""

# 在颜色定义后添加函数
# 确定使用哪个版本的 Docker Compose 命令
determine_docker_compose_cmd() {
    # 首先尝试 Docker Compose V2
    if docker compose version &> /dev/null; then
        DOCKER_COMPOSE_CMD="docker compose"
        return 0
    fi
    
    # 如果 V2 不可用，尝试传统版本
    if command -v docker-compose &> /dev/null; then
        DOCKER_COMPOSE_CMD="docker-compose"
        return 0
    fi
    
    # 如果都不可用
    echo -e "${RED}错误: 未找到可用的 Docker Compose 命令${NC}"
    return 1
}

# 在主要逻辑开始前调用检测函数
if ! determine_docker_compose_cmd; then
    exit 1
fi

# 解析命令行参数
parse_args() {
  # 至少需要一个参数（产品名）
  if [ $# -lt 1 ]; then
    log_error "缺少必须的产品名称参数"
    show_help
  fi

  # 设置产品名
  PRODUCT="$1"
  shift

  # 确保产品名称不是以"-"开头的选项
  if [[ "$PRODUCT" == -* ]]; then
    log_error "无效的产品名称: $PRODUCT"
    show_help
  fi

  # 解析剩余参数
  NODE_ROLE=""
  while [ $# -gt 0 ]; do
    case "$1" in
      master1|master2)
        NODE_ROLE="$1"
        ;;
      --remove-data)
        DATA_RETENTION="remove"
        ;;
      --keep-data)
        DATA_RETENTION="keep"
        ;;
      -h|--help)
        show_help
        ;;
      *)
        log_error "未知参数: $1"
        show_help
        ;;
    esac
    shift
  done

  # 设置节点名称
  if [ -z "$NODE_ROLE" ]; then
    # 卸载所有节点
    NODE_NAME=""
  else
    # 卸载特定节点
    NODE_NAME="${PRODUCT}-mysql-${NODE_ROLE}"
  fi
}

# 检查Compose文件
check_compose_file() {
  # 根据角色确定需要检查的Compose文件
  if [ "$NODE_ROLE" = "master1" ]; then
    COMPOSE_FILE="$COMPOSE_DIR/${PRODUCT}-mysql-master1.yml"
    # 设置变量供后续使用
    MASTER1_COMPOSE_FILE="$COMPOSE_FILE"
    MASTER1_COMPOSE_FOUND=true
    MASTER2_COMPOSE_FOUND=false
  elif [ "$NODE_ROLE" = "master2" ]; then
    COMPOSE_FILE="$COMPOSE_DIR/${PRODUCT}-mysql-master2.yml"
    # 设置变量供后续使用
    MASTER2_COMPOSE_FILE="$COMPOSE_FILE"
    MASTER2_COMPOSE_FOUND=true
    MASTER1_COMPOSE_FOUND=false
  else
    # 检查master1和master2的Compose文件
    MASTER1_COMPOSE_FILE="$COMPOSE_DIR/${PRODUCT}-mysql-master1.yml"
    MASTER2_COMPOSE_FILE="$COMPOSE_DIR/${PRODUCT}-mysql-master2.yml"
    
    if [ ! -f "$MASTER1_COMPOSE_FILE" ] && [ ! -f "$MASTER2_COMPOSE_FILE" ]; then
      log_error "找不到Compose文件: $MASTER1_COMPOSE_FILE 或 $MASTER2_COMPOSE_FILE"
      echo "在 $COMPOSE_DIR 下找到的Compose文件:"
      ls -1 "$COMPOSE_DIR"/*.yml 2>/dev/null || echo "  没有找到任何Compose文件"
      return 1
    fi
    
    # 设置变量供后续使用
    COMPOSE_FILES_FOUND=0
    if [ -f "$MASTER1_COMPOSE_FILE" ]; then
      COMPOSE_FILES_FOUND=$((COMPOSE_FILES_FOUND + 1))
      MASTER1_COMPOSE_FOUND=true
    else
      MASTER1_COMPOSE_FOUND=false
    fi
    
    if [ -f "$MASTER2_COMPOSE_FILE" ]; then
      COMPOSE_FILES_FOUND=$((COMPOSE_FILES_FOUND + 1))
      MASTER2_COMPOSE_FOUND=true
    else
      MASTER2_COMPOSE_FOUND=false
    fi
    
    return 0
  fi
  
  # 如果特定的Compose文件不存在
  if [ ! -f "$COMPOSE_FILE" ]; then
    log_error "找不到Compose文件: $COMPOSE_FILE"
    echo "在 $COMPOSE_DIR 下找到的Compose文件:"
    ls -1 "$COMPOSE_DIR"/*.yml 2>/dev/null || echo "  没有找到任何Compose文件"
    return 1
  fi
  
  log_info "已找到Compose文件: $COMPOSE_FILE"
  return 0
}

# 停止并删除容器
stop_and_remove_containers() {
  log_info "停止并删除MySQL容器..."
  
  # 确保Compose文件存在
  check_compose_file || return 1
  
  if [ -z "$NODE_ROLE" ]; then
    # 卸载所有节点
    log_warning "将停止并删除所有MySQL容器"
    
    if [ "$MASTER1_COMPOSE_FOUND" = "true" ]; then
      log_info "停止并删除主节点1..."
      (cd "$COMPOSE_DIR" && $DOCKER_COMPOSE_CMD -f $(basename "$MASTER1_COMPOSE_FILE") down)
    fi
    
    if [ "$MASTER2_COMPOSE_FOUND" = "true" ]; then
      log_info "停止并删除主节点2..."
      (cd "$COMPOSE_DIR" && $DOCKER_COMPOSE_CMD -f $(basename "$MASTER2_COMPOSE_FILE") down)
    fi
  else
    # 卸载特定节点
    log_warning "将停止并删除节点: $NODE_NAME"
    (cd "$COMPOSE_DIR" && $DOCKER_COMPOSE_CMD -f $(basename "$COMPOSE_FILE") down)
  fi
  
  if [ $? -eq 0 ]; then
    log_success "容器已成功停止并删除"
    return 0
  else
    log_error "停止和删除容器时发生错误"
    return 1
  fi
}

# 删除数据目录
remove_data() {
  if [ "$DATA_RETENTION" != "remove" ]; then
    log_info "保留数据目录: $DATA_DIR"
    return 0
  fi
  
  log_warning "即将删除MySQL数据目录。此操作不可逆，所有数据将永久丢失。"
  
  # 定义目标数据目录
  local target_data_dir=""
  
  if [ -z "$NODE_ROLE" ]; then
    # 删除所有节点的数据
    if [ "$MASTER1_COMPOSE_FOUND" = "true" ] && [ "$MASTER2_COMPOSE_FOUND" = "true" ]; then
      target_data_dir="$DATA_DIR/$PRODUCT"
      log_warning "删除所有节点的数据目录: $target_data_dir"
      
      # 检查目录是否存在
      if [ ! -d "$target_data_dir" ]; then
        log_warning "数据目录不存在: $target_data_dir"
        return 0
      fi
      
      # 分别删除master1和master2目录
      if [ -d "$target_data_dir/master1" ]; then
        log_info "删除master1数据目录..."
        rm -rf "$target_data_dir/master1"
      fi
      
      if [ -d "$target_data_dir/master2" ]; then
        log_info "删除master2数据目录..."
        rm -rf "$target_data_dir/master2"
      fi
      
    elif [ "$MASTER1_COMPOSE_FOUND" = "true" ]; then
      target_data_dir="$DATA_DIR/$PRODUCT/master1"
      log_warning "删除主节点1的数据目录: $target_data_dir"
      
      # 检查目录是否存在
      if [ ! -d "$target_data_dir" ]; then
        log_warning "数据目录不存在: $target_data_dir"
        return 0
      fi
      
      rm -rf "$target_data_dir"
      
    elif [ "$MASTER2_COMPOSE_FOUND" = "true" ]; then
      target_data_dir="$DATA_DIR/$PRODUCT/master2"
      log_warning "删除主节点2的数据目录: $target_data_dir"
      
      # 检查目录是否存在
      if [ ! -d "$target_data_dir" ]; then
        log_warning "数据目录不存在: $target_data_dir"
        return 0
      fi
      
      rm -rf "$target_data_dir"
    fi
  else
    # 删除特定节点的数据
    target_data_dir="$DATA_DIR/$PRODUCT/$NODE_ROLE"
    log_warning "删除节点 $NODE_ROLE 的数据目录: $target_data_dir"
    
    # 检查目录是否存在
    if [ ! -d "$target_data_dir" ]; then
      log_warning "数据目录不存在: $target_data_dir"
      return 0
    fi
    
    # 直接删除该节点的数据目录
    rm -rf "$target_data_dir"
  fi
  
  if [ $? -eq 0 ]; then
    log_success "数据目录已成功删除: $target_data_dir"
    return 0
  else
    log_error "删除数据目录时发生错误，请检查目录权限和路径是否正确: $target_data_dir"
    # 打印当前工作目录和BASE_DIR路径，帮助调试
    echo "当前工作目录: $(pwd)"
    echo "数据目录基础路径: $BASE_DIR"
    echo "完整数据路径: $target_data_dir"
    return 1
  fi
}

# 清理网络和卷
cleanup_resources() {
  log_info "清理未使用的Docker资源..."
  
  # 清理未使用的网络
  docker network prune -f
  
  # 清理未使用的卷
  docker volume prune -f
  
  log_success "未使用的Docker资源已清理"
}

# 主函数
main() {
  # 解析命令行参数
  parse_args "$@"
  
  # 设置产品相关的目录
  DATA_DIR="$BASE_DIR/data"
  
  # 检查BASE_DIR和DATA_DIR是否正确设置
  if [ ! -d "$BASE_DIR" ]; then
    log_error "基础目录不存在: $BASE_DIR"
    echo "当前工作目录: $(pwd)"
    echo "脚本目录: $SCRIPT_DIR"
    exit 1
  fi
  
  if [ ! -d "$DATA_DIR" ]; then
    log_warning "数据目录不存在: $DATA_DIR"
    echo "将尝试创建目录..."
    mkdir -p "$DATA_DIR" || { log_error "无法创建数据目录"; exit 1; }
  fi
  
  # 验证数据目录路径是否与Compose文件中定义的一致
  log_info "使用的数据目录路径: $DATA_DIR/$PRODUCT/master1 (master1节点)"
  log_info "使用的数据目录路径: $DATA_DIR/$PRODUCT/master2 (master2节点)"
  
  # 打印卸载信息
  log_info "MySQL卸载工具 - $PRODUCT"
  log_info "检测当前环境中的节点配置..."
  
  # 检查Compose文件
  check_compose_file || {
    log_error "无法继续卸载操作"
    exit 1
  }
  
  if [ -z "$NODE_ROLE" ]; then
    # 显示所有检测到的节点
    log_info "将卸载以下节点:"
    
    if [ "$MASTER1_COMPOSE_FOUND" = "true" ]; then
      log_info "- 主节点1 (${PRODUCT}-mysql-master1)"
    fi
    
    if [ "$MASTER2_COMPOSE_FOUND" = "true" ]; then
      log_info "- 主节点2 (${PRODUCT}-mysql-master2)"
    fi
  else
    log_info "将卸载节点: $NODE_ROLE (${PRODUCT}-mysql-${NODE_ROLE})"
  fi
  
  if [ "$DATA_RETENTION" = "remove" ]; then
    log_info "数据处理: 删除所有数据"
  else
    log_info "数据处理: 保留数据"
  fi
  
  # 最终确认
  echo -n "确认继续卸载操作? [y/N]: "
  local confirm
  read confirm
  
  if [[ ! "$confirm" =~ ^[Yy]$ ]]; then
    log_info "卸载操作已取消"
    exit 0
  fi
  
  # 执行卸载操作
  log_info "开始卸载MySQL服务..."
  
  # 1. 停止并删除容器
  stop_and_remove_containers || log_error "容器卸载过程中出现错误"
  
  # 2. 清理数据(如果指定)
  remove_data || log_error "数据清理过程中出现错误"
  
  # 3. 清理其他资源
  cleanup_resources
  
  log_success "MySQL卸载完成"
  
  if [ "$DATA_RETENTION" = "keep" ]; then
    log_info "数据目录已保留: $DATA_DIR/$PRODUCT"
    if [ -n "$NODE_ROLE" ]; then
      log_info "保留了节点 $NODE_ROLE 的数据: $DATA_DIR/$PRODUCT/$NODE_ROLE"
    else
      log_info "保留了所有节点的数据"
    fi
  fi
}

# 执行主函数
main "$@" 