-- 工作进程初始化脚本
local healthcheck = require "resty.healthcheck"
local cjson = require "cjson.safe"

-- 创建health checker实例
local checker = healthcheck.new({
    name = "cloudkey_backend",
    shm_name = "healthcheck",
    checks = {
        active = {
            type = "http",
            http_path = "/cloudkeyserver/actuator/health",
            healthy = {
                interval = 5,
                successes = 2
            },
            unhealthy = {
                interval = 2,
                http_failures = 1,
                tcp_failures = 1
            },
            timeout = 1
        }
    }
})

-- 添加目标服务器进行健康检查
local ok, err = checker:add_target("***************", 8082)
if not ok then
    ngx.log(ngx.ERR, "无法添加健康检查目标 ***************:8082: ", err)
end

local ok, err = checker:add_target("***************", 8082)
if not ok then
    ngx.log(ngx.ERR, "无法添加健康检查目标 ***************:8082: ", err)
end

-- 保存checker到package变量中，使其他Lua代码块可以访问
package.loaded.my_checker = checker

-- 将上游服务器列表保存到共享内存，以便其他lua代码块访问
local upstream_servers = {
    { ip = "***************", port = 8082 },
    { ip = "***************", port = 8082 }
}
ngx.shared.healthcheck:set("upstreams", cjson.encode(upstream_servers)) 