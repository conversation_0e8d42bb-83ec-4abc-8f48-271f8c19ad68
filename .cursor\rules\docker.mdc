---
description: 此规则用于管理Docker镜像和配置。   包括镜像构建、存储和配置管理。
globs: registry/images/**/*.tar, **/docker-compose.yml, **/Dockerfile, setup/docker/**/*
alwaysApply: false
---
---
description: 此规则用于管理Docker镜像和配置。   包括镜像构建、存储和配置管理。
globs: registry/images/**/*.tar, **/docker-compose.yml, **/Dockerfile, setup/docker/**/*
---
# Docker相关规则
description: |
  此规则用于管理Docker镜像和配置。
  包括镜像构建、存储和配置管理。

references:
  - "@docs/PROJECT_RULES.md"

rules:
  - 镜像文件管理：
    - 必须按架构分类存储（x86_64/aarch64）
    - 文件名必须包含版本信息
    - 必须包含 SHA256 校验值
  - 镜像构建规范：
    - 基础镜像必须指定具体版本号
    - 多阶段构建时必须使用有意义的阶段名
    - 必须使用 .dockerignore 排除不需要的文件
  - Compose文件规范：
    - 必须指定版本号
    - 服务名称必须有意义且符合产品规范
    - 必须配置资源限制
    - 必须配置健康检查
  - 存储管理：
    - 数据卷必须使用命名卷
    - 配置文件必须使用配置卷
    - 日志必须输出到宿主机