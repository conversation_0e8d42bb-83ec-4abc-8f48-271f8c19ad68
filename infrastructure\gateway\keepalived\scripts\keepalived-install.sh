#!/bin/bash
# Keepalived 安装脚本 - 专注于基础软件安装
# 使用方法: ./keepalived-install.sh [--src|--rpm]

set -e

# 脚本基础配置
SCRIPT_DIR=$(dirname "$(readlink -f "$0")")
BASE_DIR=$(dirname "${SCRIPT_DIR}")
SRC_DIR="${BASE_DIR}/src"
RPM_DIR="${BASE_DIR}/rpm/packages"
CONFIG_DIR="/etc/keepalived"
KEEPALIVED_VERSION="2.3.2"

# 默认参数
INSTALL_METHOD="auto"

# 日志函数
log_info() { echo -e "\033[0;32m[INFO] $1\033[0m"; }
log_warn() { echo -e "\033[0;33m[WARN] $1\033[0m"; }
log_error() { echo -e "\033[0;31m[ERROR] $1\033[0m" >&2; }

# 显示使用帮助
show_usage() {
    cat << EOF
Keepalived 安装脚本（仅负责基础软件安装）
使用方法: $0 [选项]

安装方式选项:
  --src       使用源码方式安装
  --rpm       使用RPM包方式安装
  --auto      自动选择最合适的安装方式(默认)

其他选项:
  -h, --help  显示此帮助信息
EOF
}

# 检查运行环境
check_environment() {
    # 检查root权限
    if [ "$(id -u)" -ne 0 ]; then
        log_error "必须以root用户运行此脚本"
        exit 1
    fi

    # 检测操作系统
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS=$(echo $ID | tr '[:upper:]' '[:lower:]')
        VERSION_ID=$VERSION_ID
        log_info "检测到操作系统: $ID $VERSION_ID"
    else
        log_error "无法确定操作系统类型"
        exit 1
    fi
}

# 创建必要的目录
create_directories() {
    local dirs=(
        "$CONFIG_DIR"
        "$CONFIG_DIR/scripts"
        "/etc/sysconfig"
        "/var/log/keepalived"
    )

    for dir in "${dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            log_info "创建目录: $dir"
            mkdir -p "$dir"
        fi
    done
}

# 配置系统服务
configure_systemd_service() {
    log_info "配置 systemd 服务..."
    
    # 复制服务文件
    cp -f "${BASE_DIR}/config/keepalived.service" "/etc/systemd/system/"
    
    # 创建默认配置文件
    cat > "/etc/sysconfig/keepalived" << EOF
# Keepalived 配置选项
KEEPALIVED_OPTIONS="-f ${CONFIG_DIR}/keepalived.conf"
EOF

    # 重新加载 systemd
    systemctl daemon-reload
    
    # 启用服务自启动
    systemctl enable keepalived
    
    log_info "systemd 服务配置完成"
}

# 配置系统参数
configure_system() {
    log_info "配置系统参数..."
    
    # 配置sysctl参数
    cat > /etc/sysctl.d/99-keepalived.conf << EOF
# Keepalived 必要的系统参数
net.ipv4.ip_nonlocal_bind = 1
net.ipv4.ip_forward = 1
EOF

    # 应用sysctl配置
    sysctl -p /etc/sysctl.d/99-keepalived.conf
    
    log_info "系统参数配置完成"
}

# 配置防火墙
configure_firewall() {
    log_info "配置防火墙规则..."
    
    # 检查是否安装了firewalld
    if command -v firewall-cmd >/dev/null 2>&1; then
        log_info "检测到firewalld服务"
        
        # 检查firewalld是否运行
        if systemctl is-active --quiet firewalld; then
            log_info "配置firewalld规则"
            firewall-cmd --permanent --add-protocol=vrrp || 
                firewall-cmd --permanent --direct --add-rule ipv4 filter INPUT 0 -p vrrp -j ACCEPT
            
            # 重新加载防火墙配置
            firewall-cmd --reload
        fi
    # 检查是否安装了iptables
    elif command -v iptables >/dev/null 2>&1; then
        log_info "检测到iptables服务"
        
        # 检查是否已经存在VRRP规则
        if ! iptables -C INPUT -p vrrp -j ACCEPT 2>/dev/null; then
            log_info "添加iptables VRRP规则"
            iptables -I INPUT -p vrrp -j ACCEPT
            
            # 保存iptables规则
            if command -v iptables-save >/dev/null 2>&1; then
                if [ -d "/etc/sysconfig" ]; then
                    iptables-save > /etc/sysconfig/iptables
                elif [ -d "/etc/iptables" ]; then
                    iptables-save > /etc/iptables/rules.v4
                fi
            fi
        fi
    fi
    
    # 检查nftables
    if command -v nft >/dev/null 2>&1; then
        log_info "检测到nftables服务"
        
        # 检查是否已经存在VRRP规则
        if ! nft list ruleset | grep -q "vrrp"; then
            log_info "添加nftables VRRP规则"
            nft add rule ip filter input ip protocol vrrp accept 2>/dev/null || log_warn "添加nftables规则失败"
        fi
    fi
    
    log_info "防火墙配置完成"
}

# 配置Keepalived日志
configure_logging() {
    log_info "配置 Keepalived 日志..."
    
    # 配置rsyslog
    cat > /etc/rsyslog.d/30-keepalived.conf << EOF
# Keepalived日志配置
if (\$programname == 'Keepalived') then {
    /var/log/keepalived/keepalived.log
    stop
}
if (\$programname == 'Keepalived_vrrp') then {
    /var/log/keepalived/vrrp.log
    stop
}
if (\$programname == 'Keepalived_healthcheckers') then {
    /var/log/keepalived/healthchecks.log
    stop
}
EOF

    # 配置logrotate
    cat > /etc/logrotate.d/keepalived << EOF
/var/log/keepalived/*.log {
    daily
    rotate 7
    missingok
    compress
    notifempty
    create 0644 root root
    postrotate
        systemctl restart rsyslog >/dev/null 2>&1 || true
    endscript
}
EOF

    # 重启rsyslog服务
    systemctl restart rsyslog
    
    log_info "日志配置完成"
}

# 安装依赖工具
install_dependencies() {
    log_info "安装依赖工具..."
    
    # 安装Redis工具
    if [ -f "${RPM_DIR}/openeuler/redis-7.2.7-1.oe2203sp4.x86_64.rpm" ]; then
        log_info "安装Redis客户端工具..."
        rpm -Uvh --nodeps --replacepkgs "${RPM_DIR}/openeuler/redis-7.2.7-1.oe2203sp4.x86_64.rpm" || log_warn "Redis安装失败"
        
        # 禁用Redis服务（因为我们只需要redis-cli工具）
        if systemctl is-active --quiet redis; then
            systemctl stop redis
        fi
        if systemctl is-enabled --quiet redis; then
            systemctl disable redis
        fi
    fi
    
    # 安装MySQL客户端工具
    install_mysql_client
    
    log_info "依赖工具安装完成"
}

# 安装MySQL客户端工具
install_mysql_client() {
    log_info "安装MySQL客户端工具..."
    
    local mysql_rpms=(
        "${RPM_DIR}/openeuler/mysql-common-8.0.41-1.oe2203sp4.x86_64.rpm"
        "${RPM_DIR}/openeuler/mysql-config-8.0.41-1.oe2203sp4.x86_64.rpm"
        "${RPM_DIR}/openeuler/mysql-8.0.41-1.oe2203sp4.x86_64.rpm"
        "${RPM_DIR}/openeuler/mariadb-connector-c-3.1.13-4.oe2203sp4.x86_64.rpm"
    )
    
    for rpm_file in "${mysql_rpms[@]}"; do
        if [ -f "$rpm_file" ]; then
            log_info "安装 $(basename "$rpm_file")..."
            rpm -Uvh --nodeps --replacepkgs "$rpm_file" || log_warn "$(basename "$rpm_file") 安装失败"
        fi
    done
}

# 源码安装
install_from_source() {
    log_info "开始源码安装..."
    
    # 检查编译环境
    local build_tools=("gcc" "make" "autoconf" "automake")
    for tool in "${build_tools[@]}"; do
        if ! command -v $tool &>/dev/null; then
            log_error "未找到必要的编译工具: $tool"
            exit 1
        fi
    done
    
    # 解压源码
    local temp_dir=$(mktemp -d)
    cd $temp_dir
    tar xzf "${SRC_DIR}/keepalived-${KEEPALIVED_VERSION}.tar.gz"
    cd "keepalived-${KEEPALIVED_VERSION}"
    
    # 配置和编译
    ./configure \
        --prefix=/usr/local/keepalived \
        --exec-prefix=/usr/local/keepalived \
        --sysconfdir=${CONFIG_DIR} \
        --localstatedir=/var \
        --with-systemdsystemunitdir=/etc/systemd/system
    
    make -j $(nproc)
    make install
    
    # 清理
    cd
    rm -rf $temp_dir
    
    log_info "源码安装完成"
}

# RPM包安装
install_from_rpm() {
    log_info "开始RPM包安装..."
    
    # 查找所有RPM包
    local rpm_files=$(find "$RPM_DIR" -name "keepalived*.rpm")
    if [ -z "$rpm_files" ]; then
        log_error "未找到Keepalived RPM包"
        exit 1
    fi
    
    # 安装所有包
    log_info "安装Keepalived RPM包..."
    rpm -Uvh --replacepkgs --nodeps $rpm_files || log_warn "RPM安装可能有问题，但将继续执行..."
    
    log_info "RPM包安装完成"
}

# 验证安装
validate_installation() {
    log_info "验证Keepalived安装..."
    
    if ! command -v keepalived >/dev/null 2>&1; then
        log_error "Keepalived命令不可用，安装可能失败"
        return 1
    fi
    
    keepalived_version=$(keepalived --version 2>&1 | head -n 1)
    log_info "安装的Keepalived版本: $keepalived_version"
    
    return 0
}

# 主函数
main() {
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --src)
                INSTALL_METHOD="src"
                shift
                ;;
            --rpm)
                INSTALL_METHOD="rpm"
                shift
                ;;
            --auto)
                INSTALL_METHOD="auto"
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                log_error "无效的参数: '$1'"
                show_usage
                exit 1
                ;;
        esac
    done
    
    log_info "开始安装 Keepalived"
    
    # 环境检查和准备
    check_environment
    create_directories
    
    # 安装依赖工具
    install_dependencies
    
    # 执行Keepalived安装
    case $INSTALL_METHOD in
        src)
            install_from_source
            ;;
        rpm)
            install_from_rpm
            ;;
        auto)
            # 根据系统自动选择安装方式
            if find "$RPM_DIR" -name "keepalived*.rpm" | grep -q ""; then
                install_from_rpm
            else
                install_from_source
            fi
            ;;
    esac
    
    # 配置系统环境
    configure_system
    configure_firewall
    configure_logging
    configure_systemd_service
    
    # 验证安装
    if validate_installation; then
        log_info "Keepalived 安装成功！"
        log_info "现在可以使用 deploy.sh 部署特定产品配置"
    else
        log_error "Keepalived 安装验证失败"
        exit 1
    fi
}

# 执行主函数
main "$@" 