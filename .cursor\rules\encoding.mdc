---
description: 此规则用于管理项目中的文件编码。   确保所有文本文件使用统一的UTF-8编码。
globs: **/*.md,**/*.sh,**/*.conf,**/*.yml,**/*.yaml,**/*.json,**/*.xml,**/*.txt,**/Dockerfile,dir-structure
alwaysApply: false
---
---
description: 此规则用于管理项目中的文件编码。   确保所有文本文件使用统一的UTF-8编码。
globs: **/*.md,**/*.sh,**/*.conf,**/*.yml,**/*.yaml,**/*.json,**/*.xml,**/*.txt,**/Dockerfile,dir-structure
---
# 编码规范规则
description: |
  此规则用于管理项目中的文件编码。
  确保所有文本文件使用统一的UTF-8编码。

references:
  - "@docs/PROJECT_RULES.md"

rules:
  - 文件编码规范：
    - 所有文本文件必须使用UTF-8编码
    - 必须使用Unix风格的换行符(LF)
    - 禁止使用带BOM的UTF-8
  - Git配置要求：
    - core.autocrlf必须设置为false
    - core.safecrlf必须设置为true
    - core.quotepath必须设置为false
    - i18n.commitEncoding必须设置为utf-8
    - i18n.logOutputEncoding必须设置为utf-8
  - 编辑器配置：
    - 必须配置.editorconfig文件
    - 必须设置默认编码为UTF-8
    - 必须设置默认换行符为LF
  - 提交规范：
    - commit message必须使用UTF-8编码
    - 文件名必须使用UTF-8编码
    - 注释必须使用UTF-8编码 