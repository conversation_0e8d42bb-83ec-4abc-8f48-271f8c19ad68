#!/bin/bash
# ===============================================================
# 脚本名称: restore-docker.sh
# 描述: 从备份恢复Docker安装
# ===============================================================

set -e

# 全局常量
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly BASE_DIR="$(cd "${SCRIPT_DIR}/../../.." && pwd)"
readonly LIB_DIR="${BASE_DIR}/docker/scripts/lib"

# 导入依赖库
source "${LIB_DIR}/common.sh"
source "${LIB_DIR}/logger.sh"

# 默认日志配置
LOG_LEVEL="INFO"
LOG_FILE="logs/restore-docker.log"

# 初始化日志系统
init_logger "$LOG_LEVEL" "$LOG_FILE"

# 检测文件格式并修复（如果需要）
if [ -f "$0" ] && grep -q $'\r' "$0"; then
    log_info "检测到 Windows 换行符，正在修复..."
    TMP_FILE=$(mktemp)
    tr -d '\r' < "$0" > "$TMP_FILE"
    cat "$TMP_FILE" > "$0"
    rm -f "$TMP_FILE"
    log_info "文件格式已修复，重新执行脚本..."
    exec bash "$0" "$@"
fi

# 显示帮助信息
show_help() {
    cat << EOF
用法: $(basename "$0") [选项] BACKUP_DIR

描述:
  从指定的备份目录恢复Docker安装。
  
参数:
  BACKUP_DIR             备份目录路径

选项:
  -h, --help             显示帮助信息
  -f, --force            强制恢复，不提示确认
  --skip-stop            不停止现有Docker服务
  --log-level LEVEL      指定日志级别 (DEBUG, INFO, WARN, ERROR)

示例:
  # 从指定备份恢复
  $(basename "$0") /var/backup/docker/20231210_120000
  
  # 强制恢复，不提示确认
  $(basename "$0") -f /var/backup/docker/20231210_120000
EOF
}

# 检测当前Docker版本
detect_current_docker_version() {
    if command -v docker >/dev/null 2>&1; then
        local version=$(docker --version | grep -oP "Docker version \K[0-9]+\.[0-9]+\.[0-9]+" 2>/dev/null || echo "")
        if [ -n "$version" ]; then
            echo "$version"
        else
            echo "未知"
        fi
    else
        echo "未安装"
    fi
}

# 恢复Docker
restore_docker() {
    local backup_dir="$1"
    local force="$2"
    local skip_stop="$3"
    
    if [ ! -d "$backup_dir" ]; then
        log_error "备份目录不存在: $backup_dir"
        exit 1
    fi
    
    log_info "从备份目录恢复Docker: $backup_dir"
    
    # 检查备份目录是否有效
    if [ ! -f "$backup_dir/VERSION" ] && [ ! -d "$backup_dir/docker" ] && [ ! -f "$backup_dir/docker.service" ]; then
        log_error "无效的备份目录，未找到必要的备份文件"
        log_error "请确保备份目录包含Docker配置或二进制文件"
        exit 1
    fi
    
    # 如果存在，读取备份的版本信息
    local backup_version="未知"
    if [ -f "$backup_dir/VERSION" ]; then
        backup_version=$(cat "$backup_dir/VERSION")
    fi
    
    # 检查当前Docker版本
    local current_version=$(detect_current_docker_version)
    
    # 显示恢复信息
    log_info "============================================"
    log_info "Docker 恢复信息"
    log_info "============================================"
    log_info "当前Docker版本: $current_version"
    log_info "备份Docker版本: $backup_version"
    log_info "备份目录: $backup_dir"
    
    # 如果备份摘要存在，显示内容
    if [ -f "$backup_dir/BACKUP_SUMMARY.txt" ]; then
        log_info "备份摘要:"
        cat "$backup_dir/BACKUP_SUMMARY.txt"
    fi
    
    # 如果不是强制模式，提示确认
    if [ "$force" != "true" ]; then
        read -p "确定要恢复此备份吗? [y/N] " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "用户取消恢复"
            exit 0
        fi
    fi
    
    # 停止现有Docker服务
    if [ "$skip_stop" != "true" ]; then
        log_info "停止Docker服务..."
        systemctl stop docker containerd 2>/dev/null || true
    else
        log_warn "跳过停止Docker服务（根据用户要求）"
        log_warn "恢复可能不完整或导致冲突"
    fi
    
    # 恢复二进制文件
    log_info "恢复Docker二进制文件..."
    local bins_restored=()
    
    for bin in docker containerd containerd-shim containerd-shim-runc-v2 ctr runc docker-compose; do
        if [ -f "$backup_dir/$bin" ]; then
            log_info "恢复二进制文件: $bin"
            cp "$backup_dir/$bin" "/usr/bin/$bin" && chmod +x "/usr/bin/$bin" && bins_restored+=("$bin") || log_error "无法恢复 $bin"
        fi
    done
    
    # 恢复配置文件
    if [ -d "$backup_dir/docker" ]; then
        log_info "恢复Docker配置目录..."
        # 备份当前配置（如果存在）
        if [ -d "/etc/docker" ]; then
            log_info "备份当前Docker配置..."
            cp -r "/etc/docker" "/etc/docker.bak.$(date +%Y%m%d_%H%M%S)" || log_warn "无法备份当前Docker配置"
        fi
        
        # 恢复备份的配置
        cp -r "$backup_dir/docker" /etc/ || log_error "无法恢复Docker配置目录"
    else
        log_warn "备份中没有Docker配置目录"
    fi
    
    # 恢复服务文件
    log_info "恢复Docker服务文件..."
    local services_restored=()
    
    for service in docker.service docker.socket containerd.service; do
        if [ -f "$backup_dir/$service" ]; then
            log_info "恢复服务文件: $service"
            cp "$backup_dir/$service" "/usr/lib/systemd/system/$service" && services_restored+=("$service") || log_error "无法恢复 $service"
        fi
    done
    
    # 如果存在docker-compose.yml，恢复它
    if [ -f "$backup_dir/docker-compose.yml" ]; then
        log_info "恢复docker-compose.yml文件..."
        cp "$backup_dir/docker-compose.yml" "/docker-compose.yml" || log_warn "无法恢复docker-compose.yml"
    fi
    
    # 重新加载systemd
    log_info "重新加载systemd配置..."
    systemctl daemon-reload || log_warn "无法重新加载systemd配置"
    
    # 启动服务
    log_info "启动Docker服务..."
    systemctl start containerd 2>/dev/null || log_warn "启动containerd服务失败"
    systemctl start docker 2>/dev/null || log_error "启动Docker服务失败"
    
    # 验证恢复
    log_info "验证Docker恢复..."
    sleep 2  # 等待服务完全启动
    
    if command -v docker >/dev/null 2>&1; then
        if docker --version >/dev/null 2>&1; then
            local restored_version=$(detect_current_docker_version)
            log_info "Docker恢复成功，当前版本: $restored_version"
        else
            log_error "Docker恢复后无法正常运行"
            log_error "请检查Docker服务状态: systemctl status docker"
            exit 1
        fi
    else
        log_error "Docker恢复失败，找不到docker命令"
        exit 1
    fi
    
    # 创建恢复摘要
    log_info "创建恢复摘要..."
    local summary_file="$backup_dir/RESTORE_SUMMARY.txt"
    
    cat > "$summary_file" << EOF
========================================
Docker 恢复摘要
========================================
恢复时间: $(date)
恢复前Docker版本: $current_version
恢复后Docker版本: $(detect_current_docker_version)
备份源目录: $backup_dir

恢复的二进制文件:
$(for bin in "${bins_restored[@]}"; do echo "- $bin"; done)

恢复的服务文件:
$(for service in "${services_restored[@]}"; do echo "- $service"; done)

恢复的配置目录:
$(if [ -d "$backup_dir/docker" ]; then echo "- /etc/docker"; else echo "- 无"; fi)

Docker服务状态:
$(systemctl status docker --no-pager | head -n 3)

恢复完成时间: $(date)
========================================
EOF
    
    log_info "Docker恢复完成"
    log_info "恢复摘要已保存到: $summary_file"
}

# 解析命令行参数
parse_args() {
    FORCE=false
    SKIP_STOP=false
    BACKUP_DIR=""
    
    while [[ $# -gt 0 ]]; do
        case "$1" in
            -h|--help)
                show_help
                exit 0
                ;;
            -f|--force)
                FORCE=true
                shift
                ;;
            --skip-stop)
                SKIP_STOP=true
                shift
                ;;
            --log-level)
                LOG_LEVEL="$2"
                init_logger "$LOG_LEVEL" "$LOG_FILE"
                shift 2
                ;;
            *)
                if [[ -z "$BACKUP_DIR" ]]; then
                    BACKUP_DIR="$1"
                    shift
                else
                    echo "未知选项或多个备份目录参数: $1" >&2
                    show_help
                    exit 1
                fi
                ;;
        esac
    done
    
    if [[ -z "$BACKUP_DIR" ]]; then
        echo "错误: 未指定备份目录" >&2
        show_help
        exit 1
    fi
}

# 主函数
main() {
    log_info "开始Docker恢复流程..."

    check_root
    parse_args "$@"
    restore_docker "$BACKUP_DIR" "$FORCE" "$SKIP_STOP"

    log_info "Docker恢复流程完成"
}

# 执行主函数
main "$@" 