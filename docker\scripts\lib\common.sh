#!/bin/bash

# 颜色输出函数
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查root权限
check_root() {
    if [ "$(id -u)" != "0" ]; then
        log_error "此脚本必须以root权限运行"
        exit 1
    fi
}

# 检查文件是否存在且可读
check_file_readable() {
    local file="$1"
    if [ ! -r "$file" ]; then
        log_error "文件不存在或无法读取: $file"
        return 1
    fi
    return 0
}

# 检查目录是否存在且可写
check_dir_writable() {
    local dir="$1"
    if [ ! -w "$dir" ]; then
        log_error "目录不存在或无法写入: $dir"
        return 1
    fi
    return 0
}

# 创建目录（如果不存在）
ensure_dir() {
    local dir="$1"
    if [ ! -d "$dir" ]; then
        mkdir -p "$dir" || {
            log_error "无法创建目录: $dir"
            return 1
        }
    fi
    return 0
}

# 检查命令是否存在
check_command() {
    local cmd="$1"
    if ! command -v "$cmd" >/dev/null 2>&1; then
        log_error "命令未找到: $cmd"
        return 1
    fi
    return 0
}

# 检查服务是否运行
check_service_running() {
    local service="$1"
    if ! systemctl is-active "$service" >/dev/null 2>&1; then
        log_error "服务未运行: $service"
        return 1
    fi
    return 0
}

# 安全地执行命令
safe_run() {
    local cmd="$1"
    local error_msg="${2:-命令执行失败}"
    
    if ! eval "$cmd"; then
        log_error "$error_msg"
        return 1
    fi
    return 0
}
