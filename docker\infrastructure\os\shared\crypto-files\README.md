# NETCA CRYPTO组件

本目录存放NETCA CRYPTO组件安装包，用于在操作系统基础镜像中安装密码服务组件。

## 目录结构

```
crypto-files/
├── 1.0/                 # NETCA CRYPTO 1.0版本
│   ├── amd64/           # x86_64架构
│   │   └── NETCA_CRYPTO_linux32_64/  # 安装包目录
│   │       ├── setup.sh  # 安装脚本
│   │       ├── ...       # 其他安装文件
│   └── arm64/           # ARM架构
└── 2.0/                 # NETCA CRYPTO 2.0版本(如有)
```

## 使用方法

1. 将NETCA CRYPTO安装包放置在对应版本和架构的目录中
2. 在构建操作系统镜像时会自动复制并安装这些文件

## 注意事项

- 安装包必须包含setup.sh脚本，该脚本负责安装NETCA CRYPTO组件
- 安装目录默认为/usr/lib64
- 安装日志保存在/var/log/netca_install.log 