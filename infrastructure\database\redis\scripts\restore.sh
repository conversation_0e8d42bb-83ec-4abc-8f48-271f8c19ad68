#!/bin/bash
#
# Redis 恢复脚本
# 用于从备份中恢复Redis数据
#

# 设置错误时退出
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # 无颜色

# 脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
BASE_DIR="$(dirname "$SCRIPT_DIR")"

# 参数解析
PRODUCT_NAME=${1:-"cloudkey"}
BACKUP_FILE=$2

# 在文件开头添加 Docker Compose 命令变量
DOCKER_COMPOSE_CMD=""

# 在颜色定义后添加函数
# 确定使用哪个版本的 Docker Compose 命令
determine_docker_compose_cmd() {
    # 首先尝试 Docker Compose V2
    if docker compose version &> /dev/null; then
        DOCKER_COMPOSE_CMD="docker compose"
        return 0
    fi
    
    # 如果 V2 不可用，尝试传统版本
    if command -v docker-compose &> /dev/null; then
        DOCKER_COMPOSE_CMD="docker-compose"
        return 0
    fi
    
    # 如果都不可用
    echo -e "${RED}错误: 未找到可用的 Docker Compose 命令${NC}"
    return 1
}

# 在主要逻辑开始前调用检测函数
if ! determine_docker_compose_cmd; then
    exit 1
fi

# 检查参数
if [[ "$PRODUCT_NAME" == "--help" || "$PRODUCT_NAME" == "-h" || -z "$BACKUP_FILE" ]]; then
  echo "用法: $0 [产品名称] <备份文件路径>"
  echo "产品名称: cloudkey, eseal, quicksign (默认: cloudkey)"
  echo "备份文件路径: Redis备份文件的完整路径 (.tar.gz格式)"
  exit 0
fi

# 检查备份文件是否存在
if [ ! -f "$BACKUP_FILE" ]; then
  echo -e "${RED}错误: 备份文件不存在: $BACKUP_FILE${NC}"
  exit 1
fi

# 创建日志目录
mkdir -p "$BASE_DIR/logs/$PRODUCT_NAME"
LOG_FILE="$BASE_DIR/logs/$PRODUCT_NAME/restore_$(date "+%Y%m%d_%H%M%S").log"

# 日志函数
log() {
  echo -e "$1" | tee -a "$LOG_FILE"
}

# 函数: 初始化恢复环境
initialize() {
  log "${BLUE}[1/5] 初始化恢复环境...${NC}"
  
  # 创建临时目录
  TEMP_DIR=$(mktemp -d)
  log "${YELLOW}创建临时目录: $TEMP_DIR${NC}"
  
  # 解压备份文件到临时目录
  log "${YELLOW}解压备份文件...${NC}"
  tar -xzf "$BACKUP_FILE" -C "$TEMP_DIR"
  
  # 检查备份类型
  if [ -f "$TEMP_DIR/backup_info.txt" ]; then
    BACKUP_INFO=$(cat "$TEMP_DIR/backup_info.txt")
    log "${YELLOW}备份信息:${NC}"
    log "$(echo "$BACKUP_INFO" | sed 's/^/  /')"
  else
    log "${YELLOW}警告: 备份信息文件不存在，无法确定备份类型${NC}"
  fi
  
  log "${GREEN}初始化完成${NC}"
}

# 函数: 检查Redis服务状态
check_redis_status() {
  log "${BLUE}[2/5] 检查Redis服务状态...${NC}"
  
  # 检查主节点状态
  if ! docker ps | grep -q "$PRODUCT_NAME-redis-master"; then
    log "${RED}错误: Redis主节点未运行，无法执行恢复${NC}"
    exit 1
  fi
  
  # 获取Redis密码
  if [ -f "$BASE_DIR/secrets/redis_password.txt" ]; then
    REDIS_PASSWORD=$(cat "$BASE_DIR/secrets/redis_password.txt")
  else
    log "${RED}错误: 找不到密码文件 $BASE_DIR/secrets/redis_password.txt${NC}"
    exit 1
  fi
  
  log "${GREEN}Redis服务正在运行，可以执行恢复${NC}"
}

# 函数: 确认恢复操作
confirm_restore() {
  log "${BLUE}[3/5] 确认恢复操作...${NC}"
  
  log "${RED}警告: 恢复操作将覆盖当前Redis数据，此操作不可逆!${NC}"
  log "${YELLOW}是否确定要从以下备份文件恢复数据:${NC}"
  log "${GREEN}  $BACKUP_FILE${NC}"
  
  read -p "请输入 'yes' 确认或其他任意键取消: " CONFIRM
  
  if [ "$CONFIRM" != "yes" ]; then
    log "${YELLOW}恢复操作已取消${NC}"
    exit 0
  fi
  
  log "${GREEN}已确认恢复操作${NC}"
}

# 函数: 执行恢复
perform_restore() {
  log "${BLUE}[4/5] 执行恢复操作...${NC}"
  
  # 停止Redis服务
  log "${YELLOW}停止Redis服务...${NC}"
  $DOCKER_COMPOSE_CMD -f "$BASE_DIR/compose/$PRODUCT_NAME-redis-master.yml" down
  if [ -f "$BASE_DIR/compose/$PRODUCT_NAME-redis-slave.yml" ]; then
    $DOCKER_COMPOSE_CMD -f "$BASE_DIR/compose/$PRODUCT_NAME-redis-slave.yml" down
  fi
  
  # 检查恢复数据类型
  if [ -d "$TEMP_DIR/master_data" ]; then
    # 全量备份恢复
    log "${YELLOW}从全量备份恢复...${NC}"
    
    # 备份当前数据目录
    if [ -d "$BASE_DIR/data/$PRODUCT_NAME/master" ]; then
      log "${YELLOW}备份当前数据...${NC}"
      mv "$BASE_DIR/data/$PRODUCT_NAME/master" "$BASE_DIR/data/$PRODUCT_NAME/master.bak.$(date "+%Y%m%d%H%M%S")"
    fi
    
    # 创建新的数据目录
    mkdir -p "$BASE_DIR/data/$PRODUCT_NAME/master"
    
    # 复制恢复数据
    log "${YELLOW}复制恢复数据...${NC}"
    cp -r "$TEMP_DIR/master_data"/* "$BASE_DIR/data/$PRODUCT_NAME/master/"
    
  elif [ -f "$TEMP_DIR/snapshot/dump.rdb" ]; then
    # 快照备份恢复
    log "${YELLOW}从快照备份恢复...${NC}"
    
    # 确保数据目录存在
    mkdir -p "$BASE_DIR/data/$PRODUCT_NAME/master"
    
    # 复制RDB文件
    log "${YELLOW}复制RDB文件...${NC}"
    cp "$TEMP_DIR/snapshot/dump.rdb" "$BASE_DIR/data/$PRODUCT_NAME/master/dump.rdb"
    
  else
    log "${RED}错误: 无法识别的备份类型或备份文件已损坏${NC}"
    exit 1
  fi
  
  # 设置权限
  log "${YELLOW}设置数据目录权限...${NC}"
  chmod -R 755 "$BASE_DIR/data/$PRODUCT_NAME/master"
  
  # 重启Redis服务
  log "${YELLOW}启动Redis服务...${NC}"
  $DOCKER_COMPOSE_CMD -f "$BASE_DIR/compose/$PRODUCT_NAME-redis-master.yml" up -d
  if [ -f "$BASE_DIR/compose/$PRODUCT_NAME-redis-slave.yml" ]; then
    # 等待主节点完全启动
    sleep 5
    $DOCKER_COMPOSE_CMD -f "$BASE_DIR/compose/$PRODUCT_NAME-redis-slave.yml" up -d
  fi
  
  # 等待服务启动
  log "${YELLOW}等待Redis服务启动...${NC}"
  sleep 5
  
  log "${GREEN}恢复操作完成${NC}"
}

# 函数: 验证恢复结果
verify_restore() {
  log "${BLUE}[5/5] 验证恢复结果...${NC}"
  
  # 检查服务状态
  if ! docker ps | grep -q "$PRODUCT_NAME-redis-master"; then
    log "${RED}错误: Redis主节点未成功启动${NC}"
    exit 1
  fi
  
  # 尝试连接Redis
  if ! docker exec -i "$PRODUCT_NAME-redis-master" redis-cli -a "$REDIS_PASSWORD" ping | grep -q "PONG"; then
    log "${RED}错误: 无法连接Redis服务${NC}"
    exit 1
  fi
  
  # 检查从节点状态
  if docker ps | grep -q "$PRODUCT_NAME-redis-slave"; then
    log "${YELLOW}检查从节点同步状态...${NC}"
    sleep 5 # 等待同步
    
    REPLICATION_INFO=$(docker exec -i "$PRODUCT_NAME-redis-slave" redis-cli -a "$REDIS_PASSWORD" info replication)
    if echo "$REPLICATION_INFO" | grep -q "master_link_status:up"; then
      log "${GREEN}从节点同步正常${NC}"
    else
      log "${YELLOW}警告: 从节点同步异常，可能需要手动重启从节点${NC}"
    fi
  fi
  
  log "${GREEN}恢复验证通过${NC}"
}

# 函数: 清理
cleanup() {
  log "${YELLOW}清理临时文件...${NC}"
  if [ -d "$TEMP_DIR" ]; then
    rm -rf "$TEMP_DIR"
  fi
}

# 函数: 显示恢复摘要
show_restore_summary() {
  log ""
  log "${BLUE}====================================${NC}"
  log "${GREEN}Redis恢复完成!${NC}"
  log "${BLUE}====================================${NC}"
  log "${YELLOW}产品名称: ${GREEN}$PRODUCT_NAME${NC}"
  log "${YELLOW}备份文件: ${GREEN}$BACKUP_FILE${NC}"
  log "${YELLOW}恢复时间: ${GREEN}$(date "+%Y-%m-%d %H:%M:%S")${NC}"
  log "${YELLOW}日志文件: ${GREEN}$LOG_FILE${NC}"
  log "${BLUE}====================================${NC}"
  log ""
  log "${YELLOW}建议执行监控脚本检查服务状态:${NC}"
  log "${GREEN}$SCRIPT_DIR/monitor.sh $PRODUCT_NAME${NC}"
  log "${BLUE}====================================${NC}"
}

# 主函数
main() {
  log "${BLUE}====================================${NC}"
  log "${BLUE}       Redis 恢复工具              ${NC}"
  log "${BLUE}====================================${NC}"
  log "${YELLOW}开始时间: ${GREEN}$(date "+%Y-%m-%d %H:%M:%S")${NC}"
  log "${YELLOW}产品名称: ${GREEN}$PRODUCT_NAME${NC}"
  log "${YELLOW}备份文件: ${GREEN}$BACKUP_FILE${NC}"
  log ""
  
  initialize
  check_redis_status
  confirm_restore
  perform_restore
  verify_restore
  cleanup
  show_restore_summary
}

# 捕获中断信号，执行清理
trap cleanup EXIT

# 执行主函数
main 