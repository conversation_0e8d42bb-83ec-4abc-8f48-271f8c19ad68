#!/bin/bash
# MySQL健康检查脚本模板
# 由generate_config.sh自动替换变量

MYSQL_HOST="127.0.0.1"
MYSQL_PORT="13326"
MYSQL_USER="quickSign"
MYSQL_PASSWORD="quickSignNetca2006_"
MYSQL_DATABASE="quick_sign"
PRODUCT_NAME="quicksign"
PRODUCT_DISPLAY_NAME="速签通系统"

LOG_FILE="/var/log/keepalived-mysql-${PRODUCT_NAME}-check.log"

# 记录日志
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> ${LOG_FILE}
}

# 检查MySQL服务是否正常
check_mysql() {
    local result
    
    result=$(mysql -h ${MYSQL_HOST} -P ${MYSQL_PORT} -u ${MYSQL_USER} -p${MYSQL_PASSWORD} ${MYSQL_DATABASE} -e "SELECT 1;" 2>&1)
    
    if [[ "$result" == *"1"* ]]; then
        log "${PRODUCT_DISPLAY_NAME} MySQL服务状态正常"
        return 0
    else
        log "${PRODUCT_DISPLAY_NAME} MySQL服务异常: $result"
        return 1
    fi
}

# 执行检查
check_mysql
exit $? 