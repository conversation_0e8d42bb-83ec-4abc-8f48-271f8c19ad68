#!/bin/bash
#
# Keepalived 状态检查脚本
# 用于检查 Keepalived 的运行状态、VIP分配等信息
# 使用方法: ./keepalived-health-check.sh
#

set -e

# 脚本所在目录
SCRIPT_DIR=$(dirname "$(readlink -f "$0")")
# 基础目录
BASE_DIR=$(dirname "${SCRIPT_DIR}")
# 配置目录
CONFIG_DIR="${BASE_DIR}/config"
# 公共配置目录
COMMON_DIR="${CONFIG_DIR}/common"
# 环境变量目录
ENV_DIR="${COMMON_DIR}/env"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
VIRTUAL_IP="***************"

# 分隔线
print_separator() {
    echo -e "${BLUE}=========================================${NC}"
}

# 显示标题
print_title() {
    print_separator
    echo -e "${GREEN}$1${NC}"
    print_separator
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo "选项:"
    echo "  -p, --product PRODUCT   指定产品 (cloudkey, eseal, quicksign)"
    echo "  -a, --all               显示所有产品状态"
    echo "  -h, --help              显示帮助信息"
    echo
    echo "示例:"
    echo "  $0 -p cloudkey          显示云密钥系统HA状态"
    echo "  $0 -a                   显示所有产品HA状态"
}

# 获取虚拟IP状态
check_vip_status() {
    local product="$1"
    local env_file="${ENV_DIR}/${product}.env"
    local vip=""
    local interface=""
    
    # 读取虚拟IP和网络接口配置
    if [ -f "${env_file}" ]; then
        vip=$(grep "VIRTUAL_IP" "${env_file}" | cut -d= -f2 | tr -d '"' | tr -d "'")
        interface=$(grep "VIRTUAL_INTERFACE" "${env_file}" | cut -d= -f2 | tr -d '"' | tr -d "'")
    else
        echo "错误: 找不到 ${product} 的环境变量配置文件: ${env_file}"
        return 1
    fi
    
    # 检查虚拟IP是否在本节点上
    if ip addr show ${interface} | grep -q "${vip}"; then
        echo "状态: 主节点 (VIP活跃)"
    else
        echo "状态: 备节点 (VIP未激活)"
    fi
}

# 检查Keepalived服务状态
check_service_status() {
    local status=$(systemctl is-active keepalived)
    
    echo "Keepalived服务状态: ${status}"
    if [ "${status}" != "active" ]; then
        echo "警告: Keepalived服务未运行!"
        return 1
    fi
    
    return 0
}

# 检查单个产品状态
check_product_status() {
    local product="$1"
    local product_display=""
    local env_file="${ENV_DIR}/${product}.env"
    
    # 获取产品显示名称
    if [ -f "${env_file}" ]; then
        product_display=$(grep "PRODUCT_DISPLAY_NAME" "${env_file}" | cut -d= -f2 | tr -d '"' | tr -d "'")
    else
        product_display="${product}"
    fi
    
    echo "产品: ${product_display}"
    check_service_status
    check_vip_status "${product}"
    
    # 显示最新的日志信息
    echo "最近日志:"
    journalctl -u keepalived --no-pager -n 5
    
    echo
}

# 检查进程信息
check_process_info() {
    print_title "Keepalived 进程信息"
    
    PS_OUTPUT=$(ps -ef | grep keepalived | grep -v grep)
    if [ -n "$PS_OUTPUT" ]; then
        echo -e "${GREEN}找到Keepalived进程:${NC}"
        echo "$PS_OUTPUT"
    else
        echo -e "${RED}未找到Keepalived进程${NC}"
    fi
    
    echo
}

# 检查VRRP通信
check_vrrp_communication() {
    print_title "VRRP通信状态"
    
    if command -v tcpdump &> /dev/null; then
        echo -e "${YELLOW}捕获VRRP通信(5秒)...${NC}"
        timeout 5 tcpdump -n -i any vrrp -c 5 2>/dev/null
        if [ $? -eq 124 ]; then
            echo -e "${YELLOW}5秒内未捕获到VRRP通信${NC}"
        fi
    else
        echo -e "${YELLOW}tcpdump未安装，无法检查VRRP通信${NC}"
    fi
    
    echo
}

# 检查Redis服务状态
check_redis_status() {
    print_title "Redis服务状态"
    
    if command -v redis-cli &> /dev/null; then
        REDIS_INFO=$(redis-cli -h 127.0.0.1 -p 16379 -a "CK_Redis@2006" info replication 2>/dev/null)
        if [ $? -eq 0 ]; then
            REDIS_ROLE=$(echo "$REDIS_INFO" | grep "^role:" | cut -d ":" -f2 | tr -d '[:space:]')
            echo -e "${GREEN}Redis服务运行中:${NC}"
            echo "角色: $REDIS_ROLE"
            echo "$REDIS_INFO" | grep -E "^(role|master_host|master_port|connected_slaves)"
        else
            echo -e "${RED}无法连接到Redis服务${NC}"
        fi
    else
        echo -e "${YELLOW}redis-cli未安装，无法检查Redis状态${NC}"
    fi
    
    echo
}

# 查看Keepalived日志
check_logs() {
    print_title "Keepalived 最近日志"
    
    if [ -f "/var/log/keepalived/keepalived.log" ]; then
        tail -n 20 /var/log/keepalived/keepalived.log
    elif [ -f "/var/log/messages" ]; then
        grep -i keepalived /var/log/messages | tail -n 20
    else
        echo -e "${YELLOW}未找到Keepalived日志文件${NC}"
    fi
    
    echo
}

# 检查配置文件
check_config() {
    print_title "Keepalived 配置检查"
    
    CONFIG_FILE="/etc/keepalived/keepalived.conf"
    if [ -f "$CONFIG_FILE" ]; then
        ROLE=$(grep -E "state (MASTER|BACKUP)" "$CONFIG_FILE" | awk '{print $2}')
        INTERFACE=$(grep "interface" "$CONFIG_FILE" | head -1 | awk '{print $2}')
        PRIORITY=$(grep "priority" "$CONFIG_FILE" | head -1 | awk '{print $2}')
        
        echo -e "${GREEN}配置信息:${NC}"
        echo "角色(预期): $ROLE"
        echo "网络接口: $INTERFACE"
        echo "优先级: $PRIORITY"
        
        # 检查配置是否有语法错误
        /usr/sbin/keepalived -t -f "$CONFIG_FILE" &>/dev/null
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}配置文件语法检查通过${NC}"
        else
            echo -e "${RED}配置文件存在语法错误:${NC}"
            /usr/sbin/keepalived -t -f "$CONFIG_FILE"
        fi
    else
        echo -e "${RED}找不到配置文件: $CONFIG_FILE${NC}"
    fi
    
    echo
}

# 主处理逻辑
main() {
    local check_all=false
    local product=""
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case "$1" in
            -p|--product)
                product="$2"
                shift 2
                ;;
            -a|--all)
                check_all=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                echo "错误: 未知选项 $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查参数
    if [ "$check_all" = false ] && [ -z "$product" ]; then
        echo "错误: 请指定要检查的产品或使用 -a 选项检查所有产品"
        show_help
        exit 1
    fi
    
    echo "Keepalived状态检查"
    echo "==================="
    echo
    
    # 检查指定产品或所有产品
    if [ "$check_all" = true ]; then
        # 获取所有环境变量文件对应的产品
        for env_file in "${ENV_DIR}"/*.env; do
            if [ -f "${env_file}" ]; then
                product_name=$(basename "${env_file}" .env)
                check_product_status "${product_name}"
            fi
        done
    else
        check_product_status "${product}"
    fi
}

# 执行主函数
main "$@" 