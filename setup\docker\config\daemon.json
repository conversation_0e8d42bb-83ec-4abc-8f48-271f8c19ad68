{"data-root": "/var/lib/docker", "insecure-registries": ["registry.local:5000", "**************:1443"], "registry-mirrors": ["http://registry.local:5000", "https://**************:1443"], "log-driver": "json-file", "log-opts": {"max-size": "100m", "max-file": "3"}, "bip": "***********/24", "default-address-pools": [{"base": "***********/24", "size": 24}, {"base": "***********/24", "size": 24}, {"base": "***********/24", "size": 24}, {"base": "***********/24", "size": 24}, {"base": "***********/24", "size": 24}, {"base": "***********/24", "size": 24}, {"base": "***********/24", "size": 24}, {"base": "***********/24", "size": 24}], "exec-opts": ["native.cgroupdriver=systemd"], "storage-driver": "overlay2"}