#!/bin/bash

# 设置错误时退出
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
    echo "用法: $0 [-f|--force] [-h|--help] [-o|--os <os_name>]"
    echo "选项:"
    echo "  -f, --force    强制重新下载所有包"
    echo "  -h, --help     显示此帮助信息"
    echo "  -o, --os       指定目标操作系统(ubuntu/openeuler)，默认使用当前系统"
    echo
    echo "此脚本用于下载RPM包及其依赖。"
    echo "根据系统类型自动选择下载方式："
    echo "  - 在openEuler系统上使用dnf下载"
    echo "  - 在Ubuntu及其他系统上使用wget下载"
}

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 清理临时文件和锁
cleanup() {
    local lock_file="$1"
    if [ -f "$lock_file" ]; then
        rm -f "$lock_file"
    fi
}

# 检查是否有其他实例在运行
check_lock() {
    local lock_file="$1"
    if [ -f "$lock_file" ]; then
        local pid=$(cat "$lock_file")
        if ps -p "$pid" > /dev/null 2>&1; then
            log_error "另一个下载实例(PID: $pid)正在运行"
            exit 1
        else
            log_warn "发现过期的锁文件，正在清理..."
            rm -f "$lock_file"
        fi
    fi
    echo $$ > "$lock_file"
}

# 验证下载的包完整性
verify_rpm() {
    local rpm_file="$1"
    if ! rpm -K "$rpm_file" > /dev/null 2>&1; then
        log_error "包 $rpm_file 验证失败"
        return 1
    fi
    return 0
}

# 检查目录权限
check_directory_permissions() {
    local dir="$1"
    if [ ! -w "$dir" ]; then
        log_error "目录 $dir 没有写入权限"
        exit 1
    fi
}

# 检查包是否已存在且完整
check_package_exists() {
    local pkg="$1"
    local dir="$2"
    local force_download="$3"
    
    if [ "$force_download" = "true" ]; then
        return 1
    fi
    
    # 确保路径有效
    if [ ! -d "$dir" ]; then
        log_error "目录不存在: $dir"
        return 1
    fi
    
    # 检查完整路径
    local full_path="${dir}/${pkg}"
    if [ -f "$full_path" ]; then
        log_info "检查包完整性: $pkg"
        if verify_rpm "$full_path"; then
            return 0  # 包存在且完整
        else
            log_warn "包 ${pkg} 已存在但验证失败，将重新下载"
            rm -f "$full_path"
        fi
    fi
    return 1  # 包不存在或需要重新下载
}

# 使用wget下载单个包
download_single_rpm() {
    local url="$1"
    local output_dir="$2"
    local max_retries=3
    local retry_count=0
    
    # 确保输出目录存在
    if [ ! -d "$output_dir" ]; then
        log_error "下载目录不存在: $output_dir"
        return 1
    fi
    
    # 获取文件名
    local filename=$(basename "$url")
    local output_file="${output_dir}/${filename}"
    
    while [ $retry_count -lt $max_retries ]; do
        log_info "尝试下载: $url 到 $output_dir (尝试 $((retry_count+1))/$max_retries)"
        
        if wget -c "$url" -O "$output_file" 2>/dev/null; then
            if verify_rpm "$output_file"; then
                log_info "下载成功并验证通过: $filename"
                return 0
            else
                log_warn "包验证失败: $filename"
                rm -f "$output_file"
            fi
        else
            log_warn "wget下载失败: $url"
        fi
        
        retry_count=$((retry_count + 1))
        if [ $retry_count -lt $max_retries ]; then
            log_warn "等待 $max_retries 秒后重试..."
            sleep $max_retries
        fi
    done
    
    log_error "达到最大重试次数，下载失败: $url"
    return 1
}

# 使用wget直接下载RPM包
download_with_wget() {
    local target_dir="$1"
    local os_name="$2"
    local arch="$3"
    local force_download="$4"
    
    # 生成并创建下载目录
    local package_dir="${target_dir}/packages/${os_name}/${arch}"
    
    if [ ! -d "$package_dir" ]; then
        mkdir -p "$package_dir" || {
            log_error "创建下载目录失败: $package_dir"
            return 1
        }
    fi
    
    if [ ! -w "$package_dir" ]; then
        log_error "下载目录没有写入权限: $package_dir"
        return 1
    fi
    
    log_info "使用下载目录: $package_dir"
    
    # 切换到下载目录
    cd "$package_dir" || {
        log_error "无法进入目录: $package_dir"
        return 1
    }
    
    # 根据不同的操作系统选择不同的镜像源和包列表
    local MIRROR=""
    local RPMS=()
    
    if [ "$os_name" = "openeuler" ]; then
        # OpenEuler镜像源URL
        MIRROR="https://repo.openeuler.org/openEuler-22.03-LTS-SP4/everything/x86_64/Packages"
        
        # OpenEuler工具包列表
        local COMMON_RPMS=(
            "glibc-2.34-166.oe2203sp4.x86_64.rpm"
            "glibc-common-2.34-166.oe2203sp4.x86_64.rpm"
            "glibc-devel-2.34-166.oe2203sp4.x86_64.rpm"
            "libgcc-10.3.1-67.oe2203sp4.x86_64.rpm"
            "libgomp-10.3.1-67.oe2203sp4.x86_64.rpm"
            "gcc-10.3.1-67.oe2203sp4.x86_64.rpm"
            "cpp-10.3.1-67.oe2203sp4.x86_64.rpm"
            "binutils-2.37-30.oe2203sp4.x86_64.rpm"
            "make-4.3-5.oe2203sp4.x86_64.rpm"
            "cmake-3.22.0-9.oe2203sp4.x86_64.rpm"
            "cmake-data-3.22.0-9.oe2203sp4.noarch.rpm"
            "cmake-filesystem-3.22.0-9.oe2203sp4.x86_64.rpm"
            "automake-1.16.5-9.oe2203sp4.noarch.rpm"
            "autoconf-2.71-7.oe2203sp4.noarch.rpm"
            "libtool-2.4.7-2.oe2203sp4.x86_64.rpm"
            "gdb-11.1-9.oe2203sp4.x86_64.rpm"
            "gdb-headless-11.1-9.oe2203sp4.x86_64.rpm"
            "which-2.21-16.oe2203sp4.x86_64.rpm"
            "wget-1.21.2-6.oe2203sp4.x86_64.rpm"
            "curl-7.79.1-36.oe2203sp4.x86_64.rpm"
            "libcurl-7.79.1-36.oe2203sp4.x86_64.rpm"
            "tar-1.34-5.oe2203sp4.x86_64.rpm"
            "gzip-1.12-1.oe2203sp4.x86_64.rpm"
            "net-tools-2.10-3.oe2203sp4.x86_64.rpm"
            "lsof-4.96.4-4.oe2203sp4.x86_64.rpm"
            "procps-ng-4.0.2-14.oe2203sp4.x86_64.rpm"
            "patch-2.7.6-14.oe2203sp4.x86_64.rpm"
            "vim-enhanced-9.0-32.oe2203sp4.x86_64.rpm"
            "vim-common-9.0-32.oe2203sp4.x86_64.rpm"
            "vim-filesystem-9.0-32.oe2203sp4.noarch.rpm"
            "jq-1.6-3.oe2203sp4.x86_64.rpm"
            "jsoncpp-1.9.5-5.oe2203sp4.x86_64.rpm"
        )
        
        # OpenEuler特有的包列表
        local OPENEULER_RPMS=(
            "kmod-29-8.oe2203sp4.x86_64.rpm"
            "kernel-headers-5.10.0-***********.oe2203sp4.x86_64.rpm"
            "kernel-devel-5.10.0-***********.oe2203sp4.x86_64.rpm"
            "libxcrypt-devel-4.4.26-5.oe2203sp4.x86_64.rpm"
            "libmpc-1.2.0-7.oe2203sp4.x86_64.rpm"
            "libipt-2.0.5-2.oe2203sp4.x86_64.rpm"
            "gpm-libs-1.20.7-27.oe2203sp4.x86_64.rpm"
            "oniguruma-6.9.6-1.oe2203sp4.x86_64.rpm"
            "pkgconf-1.8.0-3.oe2203sp4.x86_64.rpm"
            "cmake-rpm-macros-3.22.0-9.oe2203sp4.noarch.rpm"
            "gcc-gdb-plugin-10.3.1-67.oe2203sp4.x86_64.rpm"
            "babeltrace-1.5.8-3.oe2203sp4.x86_64.rpm"
        )
        
        RPMS=("${COMMON_RPMS[@]}" "${OPENEULER_RPMS[@]}")
        log_info "下载完整的OpenEuler RPM包列表"
    else
        # Ubuntu的下载方式
        log_info "Ubuntu系统不直接使用wget下载RPM包，将使用apt-get下载deb包"
        
        # 使用apt-get下载Ubuntu包
        local ubuntu_packages=(
            "build-essential"
            "cmake"
            "automake"
            "autoconf"
            "libtool"
            "gdb"
            "wget"
            "curl"
            "tar"
            "gzip"
            "net-tools"
            "lsof"
            "procps"
            "patch"
            "vim"
            "jq"
            "libc6-dev"
            "libstdc++-10-dev"
            "libgcc-10-dev"
        )
        
        local total_success=0
        local total_failed=0
        
        # 更新本地包信息，但不安装
        log_info "更新本地包索引..."
        apt-get update -qq
        
        # 获取所有依赖
        log_info "分析包依赖关系..."
        local all_packages=()
        for pkg in "${ubuntu_packages[@]}"; do
            log_info "正在分析 ${pkg} 的依赖..."
            # 获取包的所有依赖，并添加到下载列表
            local deps=$(apt-cache depends --recurse --no-recommends --no-suggests \
                  --no-conflicts --no-breaks --no-replaces --no-enhances \
                  --no-pre-depends ${pkg} | grep "^\w" | sort -u)
            
            if [ -n "$deps" ]; then
                for dep in $deps; do
                    all_packages+=("$dep")
                done
            fi
            # 添加包本身
            all_packages+=("$pkg")
        done
        
        # 去重
        all_packages=($(printf "%s\n" "${all_packages[@]}" | sort -u))
        log_info "将下载 ${#all_packages[@]} 个包（包含所有依赖）"
        
        # 下载所有包
        for pkg in "${all_packages[@]}"; do
            if [ "$force_download" = "true" ] || [ ! -f "${package_dir}/${pkg}"_*.deb ]; then
                log_info "正在下载 ${pkg}..."
                if apt-get download "${pkg}" > /dev/null 2>&1; then
                    total_success=$((total_success + 1))
                    log_info "成功下载 ${pkg}"
                else
                    log_error "下载 ${pkg} 失败"
                    total_failed=$((total_failed + 1))
                fi
            else
                log_info "包 ${pkg} 已存在，跳过下载"
                total_success=$((total_success + 1))
            fi
        done
        
        log_info "Ubuntu包下载完成！成功: $total_success, 失败: $total_failed"
        [ $total_failed -eq 0 ] && return 0 || return 1
    fi
    
    # 如果不是ubuntu，继续下载OpenEuler的RPM包
    local total_pkgs=${#RPMS[@]}
    local success_count=0
    local failed_pkgs=()
    
    log_info "开始下载包，共计 ${total_pkgs} 个..."
    
    for rpm in "${RPMS[@]}"; do
        if ! check_package_exists "$rpm" "$package_dir" "$force_download"; then
            log_info "正在下载: $rpm"
            if download_single_rpm "$MIRROR/$rpm" "$package_dir"; then
                success_count=$((success_count + 1))
                log_info "成功下载: $rpm ($success_count/$total_pkgs)"
            else
                failed_pkgs+=("$rpm")
                log_error "下载失败: $rpm"
            fi
        else
            success_count=$((success_count + 1))
            log_info "包已存在且验证通过: $rpm ($success_count/$total_pkgs)"
        fi
    done
    
    # 报告下载结果
    log_info "下载完成！成功: $success_count/$total_pkgs"
    if [ ${#failed_pkgs[@]} -gt 0 ]; then
        log_warn "以下包下载失败:"
        printf '%s\n' "${failed_pkgs[@]}"
        return 1
    fi
    return 0
}

# 使用dnf下载RPM包及其依赖
download_with_dnf() {
    local target_dir="$1"
    local os_name="$2"
    local arch="$3"
    local force_download="$4"
    
    # 生成并创建下载目录
    local package_dir="${target_dir}/packages/${os_name}/${arch}"
    
    if [ ! -d "$package_dir" ]; then
        mkdir -p "$package_dir" || {
            log_error "创建下载目录失败: $package_dir"
            return 1
        }
    fi
    
    if [ ! -w "$package_dir" ]; then
        log_error "下载目录没有写入权限: $package_dir"
        return 1
    fi
    
    log_info "使用下载目录: $package_dir"
    
    # 切换到下载目录
    cd "$package_dir" || {
        log_error "无法进入目录: $package_dir"
        return 1
    }
    
    # 基础工具包列表
    local packages=(
        "jq" "wget" "curl" "tar" "gzip" "which" "net-tools"
        "lsof" "procps-ng" "vim" "gcc" "make" "gdb" "libtool"
        "patch" "cmake" "pkgconfig" "automake" "autoconf"
    )
    
    local total_success=0
    local total_failed=0
    
    for pkg in "${packages[@]}"; do
        if ! check_package_exists "${pkg}" "$package_dir" "$force_download"; then
            log_info "正在下载 ${pkg} 及其依赖..."
            if dnf download --resolve --arch="${arch}" "${pkg}" > /dev/null 2>&1; then
                total_success=$((total_success + 1))
                log_info "成功下载 ${pkg}"
            else
                total_failed=$((total_failed + 1))
                log_error "下载 ${pkg} 失败"
            fi
        else
            total_success=$((total_success + 1))
            log_info "包 ${pkg} 已存在且验证通过"
        fi
    done
    
    # 报告下载结果
    log_info "DNF下载完成！成功: $total_success, 失败: $total_failed"
    [ $total_failed -eq 0 ] && return 0 || return 1
}

# 获取当前系统架构
get_current_arch() {
    local arch=$(uname -m)
    case ${arch} in
        x86_64)
            echo "x86_64"
            ;;
        aarch64)
            echo "aarch64"
            ;;
        *)
            log_error "不支持的架构: ${arch}"
            exit 1
            ;;
    esac
}

# 获取当前操作系统
get_current_os() {
    if [ -f /etc/openEuler-release ]; then
        echo "openeuler"
    elif [ -f /etc/os-release ]; then
        . /etc/os-release
        case ${ID} in
            ubuntu)
                echo "ubuntu"
                ;;
            openeuler)
                echo "openeuler"
                ;;
            *)
                # 对于其他系统，返回ubuntu作为默认值
                log_warn "未识别的操作系统: ${ID}，将使用ubuntu作为目标系统"
                echo "ubuntu"
                ;;
        esac
    else
        log_warn "无法检测操作系统类型，将使用ubuntu作为目标系统"
        echo "ubuntu"
    fi
}

# 检查必要的命令
check_commands() {
    local os_type="$1"
    local commands=("mkdir" "curl")
    
    if [ "$os_type" = "openeuler" ]; then
        commands+=("dnf")
    else
        commands+=("wget")
    fi
    
    for cmd in "${commands[@]}"; do
        if ! command -v "$cmd" >/dev/null 2>&1; then
            log_error "命令 '$cmd' 未找到，请先安装。"
            exit 1
        fi
    done
}

# 主函数
main() {
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    local base_dir="$(dirname "${script_dir}")"
    local lock_file="/tmp/download-rpms.lock"
    local force_download="false"
    local target_os=""
    local exit_code=0
    
    # 设置退出时的清理操作
    trap 'cleanup "$lock_file"; exit $exit_code' EXIT
    trap 'cleanup "$lock_file"; exit 1' INT TERM
    
    # 检查是否有其他实例在运行
    check_lock "$lock_file"
    
    # 自动检测系统类型
    local detected_os=$(get_current_os)
    log_info "检测到当前系统: $detected_os"
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -f|--force)
                force_download="true"
                log_info "启用强制重新下载模式"
                shift
                ;;
            -o|--os)
                target_os="$2"
                log_info "指定目标操作系统: $target_os"
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 如果没有指定目标操作系统，使用检测到的系统
    if [ -z "$target_os" ]; then
        target_os="$detected_os"
        log_info "使用自动检测的操作系统: $target_os"
    else
        log_info "使用指定的目标操作系统: $target_os"
    fi
    
    # 获取当前系统架构
    local current_arch=$(get_current_arch)
    log_info "当前系统架构: ${current_arch}"
    
    # 检查是否拥有所需工具
    if [ "$detected_os" = "openeuler" ]; then
        if command -v dnf >/dev/null 2>&1; then
            log_info "在openEuler系统上使用dnf下载模式"
            check_commands "openeuler"
            download_with_dnf "${base_dir}" "${target_os}" "${current_arch}" "${force_download}"
            exit_code=$?
        else
            log_warn "在openEuler系统上未找到dnf命令，将使用wget下载模式"
            check_commands "other"
            download_with_wget "${base_dir}" "${target_os}" "${current_arch}" "${force_download}"
            exit_code=$?
        fi
    else
        log_info "在${detected_os}系统上使用wget下载模式"
        check_commands "other"
        download_with_wget "${base_dir}" "${target_os}" "${current_arch}" "${force_download}"
        exit_code=$?
    fi
    
    if [ $exit_code -eq 0 ]; then
        log_info "所有包下载完成！文件保存在 packages/${target_os}/${current_arch}/ 目录下"
    else
        log_error "下载过程中出现错误，请检查日志"
    fi
    
    return $exit_code
}

# 执行主函数
main "$@" 