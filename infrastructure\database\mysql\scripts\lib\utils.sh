#!/bin/bash
# MySQL 工具函数库
# 包含通用工具函数，如日志记录、前提条件检查等

# 引入配置
source "$(dirname "${BASH_SOURCE[0]}")/config.sh"

# 日志函数
log_info() {
  echo -e "${BLUE}[信息]${NC} $1"
}

log_success() {
  echo -e "${GREEN}[成功]${NC} $1"
}

log_warning() {
  echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
  echo -e "${RED}[错误]${NC} $1"
}

# 输入提示函数
log_prompt() {
  echo -en "${PURPLE}[输入]${NC} $1"
}

# 产品名称验证函数
validate_product_name() {
  local product="$1"
  
  # 检查名称有效性
  if [ -z "$product" ]; then
    log_error "产品名称不能为空"
    return 1
  fi
  
  # 确认是支持的产品
  case "$product" in
    cloudkey|eseal|quicksign)
      return 0
      ;;
    *)
      log_error "无效的产品名称: $product"
      echo "支持的产品: cloudkey, eseal, quicksign"
      return 1
      ;;
  esac
}

# 检查Compose文件是否存在
check_compose_files() {
  local product="$1"
  local master1_compose_file=$(get_master1_compose_file "$product")
  local master2_compose_file=$(get_master2_compose_file "$product")
  
  if [ ! -f "$master1_compose_file" ] || [ ! -f "$master2_compose_file" ]; then
    log_error "找不到Compose文件: $master1_compose_file 或 $master2_compose_file"
    echo "可用的Compose文件:"
    ls -1 "$COMPOSE_DIR"/*.yml 2>/dev/null || echo "  没有找到任何Compose文件"
    return 1
  fi
  
  return 0
}

# 检查容器状态
is_container_running() {
  local container_name="$1"
  
  if docker ps --filter "name=$container_name" --format "{{.Names}}" | grep -q "$container_name"; then
    return 0  # 容器运行中
  else
    return 1  # 容器未运行
  fi
}

# 选择节点函数
select_node() {
  local product="$1"
  local master1=$(get_master1_name "$product")
  local master2=$(get_master2_name "$product")
  
  log_info "选择要操作的节点:"
  log_info "1) $master1"
  log_info "2) $master2"
  log_prompt "请选择 [1-2]: "
  local node_choice
  read node_choice
  
  case $node_choice in
    1)
      echo "$master1"
      ;;
    2)
      echo "$master2"
      ;;
    *)
      log_error "无效的选择"
      return 1
      ;;
  esac
}

# 获取密码函数
get_mysql_root_password() {
  local password_file=$(get_mysql_root_password_file)
  if [ -f "$password_file" ]; then
    cat "$password_file"
  else
    log_error "密码文件不存在: $password_file"
    return 1
  fi
}

# 确认操作函数
confirm_action() {
  local message="$1"
  
  echo -n "$message [y/N]: "
  local confirm
  read confirm
  
  if [[ "$confirm" =~ ^[Yy]$ ]]; then
    return 0
  else
    return 1
  fi
}

# 等待服务启动
wait_for_service() {
  local seconds="${1:-10}"
  log_info "等待服务启动完成..."
  sleep "$seconds"
} 