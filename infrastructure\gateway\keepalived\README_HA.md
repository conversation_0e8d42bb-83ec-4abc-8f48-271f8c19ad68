# Keepalived 高可用架构说明

本文档详细描述了基于 Keepalived 的高可用架构设计及实现原理，主要用于 Redis 和 Nginx 服务的高可用保障。

## 高可用架构设计

### 整体架构图

```
                         +---------------------+
                         |    虚拟IP (VIP)     |
                         | ***************/24 |
                         +----------+----------+
                                    |
                                    | 漂移
                                    v
        +------------------------+     +------------------------+
        |       主节点           |     |        备节点          |
        | ***************       |     | ***************       |
        |                       |     |                       |
        | +-------------------+ |     | +-------------------+ |
        | |   Keepalived     | |     | |   Keepalived      | |
        | | (MASTER, prio:110)| |     | | (BACKUP, prio:100)| |
        | +-------------------+ |     | +-------------------+ |
        |           |           |     |           |           |
        |           v           |     |           v           |
        | +-------------------+ |     | +-------------------+ |
        | |     Redis         | |     | |     Redis         | |
        | |     (master)      | |     | |     (slave)       | |
        | +-------------------+ |     | +-------------------+ |
        |           |           |     |           |           |
        |           v           |     |           v           |
        | +-------------------+ |     | +-------------------+ |
        | |     Nginx         | |     | |     Nginx         | |
        | +-------------------+ |     | +-------------------+ |
        +------------------------+     +------------------------+
```

### 主要组件

1. **Keepalived**
   - 两个节点上运行 Keepalived 服务
   - 主节点配置为 MASTER 角色，优先级为 110
   - 备节点配置为 BACKUP 角色，优先级为 100
   - 通过 VRRP 协议实现虚拟 IP 的管理

2. **Redis**
   - 主节点上配置为 master 角色
   - 备节点上配置为 slave 角色，从主节点同步数据
   - Keepalived 通过脚本自动管理主从角色切换

3. **Nginx**
   - 两个节点上均运行 Nginx 服务
   - Keepalived 负责监控 Nginx 健康状态
   - 仅拥有虚拟 IP 的节点会处理客户端请求

## 高可用实现机制

### 1. 心跳检测

- 采用 VRRP 协议进行心跳检测
- 通告间隔：1秒
- 通过单播方式通信（`unicast_peer`）
- 使用密码认证确保安全（`auth_pass`）

### 2. 健康检查

- **Redis 健康检查**
  - 脚本: `/etc/keepalived/scripts/check_redis.sh`
  - 检查频率: 每 3 秒一次
  - 检查内容: Redis 服务状态和角色
  - 失败条件: 连续 2 次检查失败
  
- **Nginx 健康检查**
  - 脚本: `/etc/keepalived/scripts/check_nginx.sh`
  - 检查频率: 每 3 秒一次
  - 检查内容: Nginx 进程状态和 HTTP 响应
  - 失败条件: 连续 2 次检查失败

### 3. 故障转移流程

1. **检测到主节点故障**
   - Redis/Nginx 服务异常
   - 网络连接中断
   - 系统崩溃

2. **备节点接管**
   - 备节点 Keepalived 提升为 MASTER 状态
   - 备节点获取虚拟 IP
   - 通知脚本执行 Redis 主从角色切换
   - 备节点 Redis 提升为 master 角色

3. **主节点恢复**
   - 由于启用了 `nopreempt` 选项
   - 原主节点恢复后不会自动抢回虚拟 IP
   - 原主节点 Redis 变为 slave 角色

### 4. Redis 主从切换

- 通过 `notify_*.sh` 脚本自动完成 Redis 主从切换
- 主节点变为 MASTER 时，执行 `SLAVEOF NO ONE` 命令
- 主节点变为 BACKUP 时，执行 `SLAVEOF <master_ip> <port>` 命令
- 切换日志记录在 `/var/log/keepalived/state_change.log`

## 优化与限制

### 优化措施

1. **优化内核参数**
   - `net.ipv4.ip_nonlocal_bind = 1`: 允许绑定非本地 IP
   - `net.ipv4.conf.all.arp_announce = 2`: 优化 ARP 通告行为
   - `net.ipv4.conf.all.arp_ignore = 1`: 避免 ARP 响应冲突

2. **备份与恢复**
   - 定期自动备份配置文件
   - 备份保留策略: 保留最近 7 个备份
   - 自动清理旧备份文件

### 已知限制

1. **脑裂问题**
   - 网络分区可能导致双主状态
   - 解决方案: 适当增加 fall 值，提高容错能力

2. **网络限制**
   - 多子网部署需要使用单播通信
   - 某些网络环境可能阻止 VRRP 通信

3. **存储一致性**
   - Redis 主从复制是异步的
   - 极端情况下可能有少量数据不一致

## 故障场景分析

### 场景一: Redis 服务故障

1. **主节点 Redis 故障**
   - Keepalived 检测到 Redis 故障
   - 主节点状态降级，失去虚拟 IP
   - 备节点接管虚拟 IP，Redis 提升为 master
   - 主节点 Redis 恢复后变为 slave 角色

2. **备节点 Redis 故障**
   - 备节点优先级降低
   - 主节点保持 MASTER 状态
   - 备节点 Redis 恢复后，会自动恢复为 slave 角色

### 场景二: Nginx 服务故障

1. **主节点 Nginx 故障**
   - Keepalived 检测到 Nginx 故障
   - 主节点状态降级，失去虚拟 IP
   - 备节点接管虚拟 IP
   - Redis 自动切换主从角色

2. **两节点 Nginx 同时故障**
   - 两个节点优先级都会降低
   - 优先级高的节点保持虚拟 IP
   - 需要手动干预恢复 Nginx 服务

### 场景三: 节点完全故障

1. **主节点完全故障**
   - 备节点检测不到主节点心跳
   - 备节点接管虚拟 IP
   - Redis 角色自动切换

2. **备节点完全故障**
   - 主节点保持虚拟 IP
   - 无需特殊处理，继续提供服务

## 运维管理建议

1. **监控建议**
   - 监控虚拟 IP 分配
   - 监控 Redis 主从状态
   - 监控 Keepalived 日志
   - 定期检查配置一致性

2. **定期维护**
   - 定期备份配置文件
   - 定期检查日志文件
   - 定期测试故障转移
   - 定期更新和升级

3. **测试方法**
   - 模拟 Redis 故障: `systemctl stop redis`
   - 模拟 Nginx 故障: `systemctl stop nginx`
   - 模拟网络故障: `iptables -A INPUT -p 112 -j DROP`
   - 模拟系统故障: `systemctl stop keepalived` 