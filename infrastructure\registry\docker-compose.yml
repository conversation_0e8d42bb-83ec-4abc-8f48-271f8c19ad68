version: '3.7'

services:
  registry:
    image: registry:2
    container_name: registry-local
    restart: always
    environment:
      - REGISTRY_HTTP_ADDR=0.0.0.0:5000
      - REGISTRY_STORAGE_DELETE_ENABLED=true
      - REGISTRY_STORAGE_FILESYSTEM_ROOTDIRECTORY=/var/lib/registry
    volumes:
      - ./data:/var/lib/registry
      - ./config/config.yml:/etc/docker/registry/config.yml:ro
    networks:
      registry_net:
        ipv4_address: **********
    ports:
      - "5000:5000"

networks:
  registry_net:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: **********/16 