[mysqld]
# 禁用 DNS 解析，设置了该参数，那么在创建用户及授予权限时，HOST 列必须是IP而不能是主机名。
skip-name-resolve

# 设置最大连接数
max_connections=800

# 增加允许的数据包大小，默认为4M或16M
max_allowed_packet=64M

#开启bin log 功能
log-bin=mysql-bin

# 要和另一个主机的 id 不同
server-id=1
#不需要同步的库
binlog-ignore-db=mysql
binlog-ignore-db=information_schema
binlog-ignore-db=performance_schema
binlog-ignore-db=sys
# 如果键的生成策略是自增，还需要进行以下设置，注意要和另一台主机不同
auto_increment_increment=2
auto_increment_offset=1
replica-skip-errors=1062,1053,1146,1032
# binlog 记录内容的方式，记录被操作的每一行
# binlog_format = ROW 已弃用，但目前没有替代参数，暂时保留

# 对于binlog_format = ROW模式时，FULL模式可以用于误操作后的flashBack。
#如果设置为MINIMAL，则会减少记录日志的内容，只记录受影响的列，但对于部分update无法flashBack
binlog_row_image = FULL

# 开启GTID复制模式
gtid_mode = on

# 强制gtid一致性，开启后对于create table ... select ...或 CREATE TEMPORARY TABLE 将不被支持
enforce_gtid_consistency = 1

############-- binlog设置 结束 --#############


#--###################-- 慢SQL日志记录 开始 --################################
# 是否启用慢查询日志，1为启用，0为禁用  
slow_query_log = 1

# 记录系统时区
log_timestamps = SYSTEM
default_time_zone = '+08:00'

# 指定慢查询日志文件的路径和名字
slow_query_log_file = /var/lib/mysql/slow.log

# 慢查询执行的秒数，必须达到此值可被记录
long_query_time = 1

# 记录执行缓慢的管理SQL，如alter table,analyze table, check table, create index, drop index, optimize table, repair table等。  
log_slow_admin_statements = 0

# 将没有使用索引的语句记录到慢查询日志  
log_queries_not_using_indexes = 0

# 作为从库时生效, 从库复制中如何有慢sql也将被记录
# 对于ROW格式binlog，不管执行时间有没有超过阈值，都不会写入到从库的慢查询日志
log_slow_replica_statements = 1
#--#####################-- 慢SQL日志记录 结束 --##########################

#--####RedoLog日志 和 binlog日志的写磁盘频率设置 BEGIN ######################
# 设置 binlog 在事务提交之前，binlog 会刷盘
sync_binlog=1

#控制 redolog 写磁盘频率 默认为1
innodb_flush_log_at_trx_commit = 1

#该选项让从库写入哪些来自于主库的更新，并把这些更新写入bin-log文件，一台服务器即做主库又做从库必须开启
log_replica_updates=true

#作为从库时生效, 中继日志relay-log可以自我修复
relay_log_recovery = 1

#--####RedoLog日志 和 binlog日志的写磁盘频率设置 END ##########

#--#########################-- innodb性能设置 开始 --############################
# 设置 buffer pool 的大小
innodb_buffer_pool_size= 2g
# InnoDB 缓冲池划分为的区域数
#innodb_buffer_pool_instances=8
# 设置 redo log buffer 的大小，默认是 16M，一般够用 
innodb_log_buffer_size=16M
# O_DIRECT减少操作系统级别VFS的缓存和Innodb本身的buffer缓存之间的冲突
innodb_flush_method=O_DIRECT
#是否开启在线回收（收缩）undo log日志文件，支持动态设置，默认开启
innodb_undo_log_truncate = 1

#当超过这个阀值（默认是1G），会触发truncate回收（收缩）动作，truncate后空间缩小到10M
innodb_max_undo_log_size = 4G

# 设置 redo log 容量大小 (替代原来的 innodb_log_file_size 和 innodb_log_files_in_group)
innodb_redo_log_capacity=4G

#--####################-- innodb性能设置 结束 --#############################

# 默认认证插件设置
default_authentication_plugin=caching_sha2_password