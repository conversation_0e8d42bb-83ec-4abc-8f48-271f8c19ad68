#!/bin/bash

# 设置错误时退出
set -e

# 加载公共函数
source ../../lib/common.sh
source ../../lib/build-utils.sh

# 基础目录
BASE_DIR="../../../infrastructure/jdk"
VERSIONS_FILE="$BASE_DIR/versions.json"

# 检查必要文件
check_required_files() {
    if [ ! -f "$VERSIONS_FILE" ]; then
        error "版本配置文件不存在: $VERSIONS_FILE"
        exit 1
    }
}

# 显示版本信息
show_versions() {
    info "支持的JDK版本："
    echo "----------------------------------------"
    echo "默认版本：$(jq -r '.default_version' "$VERSIONS_FILE")"
    echo "----------------------------------------"
    
    # 遍历所有版本
    jq -r '.versions | to_entries[] | .key as $version | .value | 
        "JDK \($version):",
        "  基础镜像: \(.base_image)",
        "  变体: \(.variants | join(", "))",
        "  支持架构: \(.supported_architectures | join(", "))"' "$VERSIONS_FILE"
    
    echo "----------------------------------------"
    echo "加密组件版本：$(jq -r '.crypto_files_version' "$VERSIONS_FILE")"
    echo "构建配置："
    jq -r '.build_defaults | to_entries[] | "  \(.key): \(.value | if type == "array" then join(", ") else . end)"' "$VERSIONS_FILE"
}

# 添加新版本
add_version() {
    local version=$1
    local base_image=$2
    
    if [ -z "$version" ] || [ -z "$base_image" ]; then
        error "用法: $0 add <version> <base_image>"
        exit 1
    }

    info "添加新版本 JDK $version"

    # 检查版本是否已存在
    if jq -e ".versions.\"$version\"" "$VERSIONS_FILE" > /dev/null; then
        error "版本 $version 已存在"
        exit 1
    }

    # 添加新版本
    local temp_file=$(mktemp)
    jq --arg version "$version" \
       --arg base_image "$base_image" \
       '.versions += {($version): {
           "base_image": $base_image,
           "variants": ["temurin-jammy"],
           "default_variant": "temurin-jammy",
           "supported_architectures": ["x86_64", "aarch64"]
         }}' "$VERSIONS_FILE" > "$temp_file"
    mv "$temp_file" "$VERSIONS_FILE"

    success "版本 $version 添加成功"
}

# 删除版本
remove_version() {
    local version=$1
    
    if [ -z "$version" ]; then
        error "用法: $0 remove <version>"
        exit 1
    }

    info "删除版本 JDK $version"

    # 检查版本是否存在
    if ! jq -e ".versions.\"$version\"" "$VERSIONS_FILE" > /dev/null; then
        error "版本 $version 不存在"
        exit 1
    }

    # 检查是否为默认版本
    if [ "$(jq -r '.default_version' "$VERSIONS_FILE")" = "$version" ]; then
        error "无法删除默认版本"
        exit 1
    }

    # 删除版本
    local temp_file=$(mktemp)
    jq --arg version "$version" 'del(.versions[$version])' "$VERSIONS_FILE" > "$temp_file"
    mv "$temp_file" "$VERSIONS_FILE"

    success "版本 $version 删除成功"
}

# 设置默认版本
set_default() {
    local version=$1
    
    if [ -z "$version" ]; then
        error "用法: $0 set-default <version>"
        exit 1
    }

    info "设置默认版本为 JDK $version"

    # 检查版本是否存在
    if ! jq -e ".versions.\"$version\"" "$VERSIONS_FILE" > /dev/null; then
        error "版本 $version 不存在"
        exit 1
    }

    # 设置默认版本
    local temp_file=$(mktemp)
    jq --arg version "$version" '.default_version = $version' "$VERSIONS_FILE" > "$temp_file"
    mv "$temp_file" "$VERSIONS_FILE"

    success "默认版本已设置为 $version"
}

# 显示帮助信息
show_help() {
    echo "用法: $0 <命令> [参数...]"
    echo
    echo "命令:"
    echo "  list                显示所有版本信息"
    echo "  add <ver> <img>     添加新版本"
    echo "  remove <ver>        删除指定版本"
    echo "  set-default <ver>   设置默认版本"
    echo "  help               显示此帮助信息"
}

# 主函数
main() {
    # 检查必要的命令
    check_command "jq"

    # 检查必要文件
    check_required_files

    # 切换到脚本所在目录
    cd "$(dirname "$0")"

    # 处理命令
    case "$1" in
        list|"")
            show_versions
            ;;
        add)
            add_version "$2" "$3"
            ;;
        remove)
            remove_version "$2"
            ;;
        set-default)
            set_default "$2"
            ;;
        help|-h|--help)
            show_help
            ;;
        *)
            error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@" 