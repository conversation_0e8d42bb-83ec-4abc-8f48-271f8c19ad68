#!/bin/bash
# Redis健康检查脚本模板
# 由generate_config.sh自动替换变量
# 用于Keepalived监控Redis服务状态

# Redis连接参数
REDIS_HOST="{{REDIS_HOST}}"
REDIS_PORT="{{REDIS_PORT}}"
REDIS_PASSWORD="{{REDIS_PASSWORD}}"
REDIS_DB="{{REDIS_DB}}"
PRODUCT_NAME="{{PRODUCT_NAME}}"
PRODUCT_DISPLAY_NAME="{{PRODUCT_DISPLAY_NAME}}"

# 日志配置
LOG_DIR="/var/log/keepalived"
mkdir -p "$LOG_DIR"
LOG_FILE="${LOG_DIR}/keepalived-redis-${PRODUCT_NAME}-check.log"

# 故障状态文件
FAILURE_STATE_FILE="/tmp/redis_check_${PRODUCT_NAME}_failures"
# 时间戳记录文件
TIMESTAMP_FILE="/tmp/redis_check_${PRODUCT_NAME}_timestamp"

# 清除多久以前的失败记录（秒）
FAILURE_TTL=30

# 连续失败阈值
FAILURE_THRESHOLD=2     # 连续失败阈值，达到此值后退出并触发keepalived故障转移

# 记录日志的函数，同时输出到系统日志和文件
log_message() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    # 输出到文件
    echo "[$timestamp] [$level] [${PRODUCT_DISPLAY_NAME}] $message" >> "$LOG_FILE"

    # 输出到系统日志
    logger -t "check_redis_${PRODUCT_NAME}" "[$level] $message"
}

# 读取失败计数
get_failure_count() {
    if [ -f "$FAILURE_STATE_FILE" ]; then
        local last_timestamp=0
        if [ -f "$TIMESTAMP_FILE" ]; then
            last_timestamp=$(cat "$TIMESTAMP_FILE")
        fi

        local current_time=$(date +%s)
        local time_diff=$((current_time - last_timestamp))

        # 如果超过故障保留期，重置计数器
        if [ "$time_diff" -gt "$FAILURE_TTL" ]; then
            echo 0 > "$FAILURE_STATE_FILE"
            log_message "INFO" "超过故障保留期($FAILURE_TTL秒)，失败计数重置"
            echo 0
        else
            cat "$FAILURE_STATE_FILE"
        fi
    else
        echo 0
    fi
}

# 更新失败计数
update_failure_count() {
    local count=$1
    echo "$count" > "$FAILURE_STATE_FILE"
    date +%s > "$TIMESTAMP_FILE"
}

# 记录脚本开始执行
log_message "INFO" "开始执行Redis检查脚本"

# 确保redis-cli存在
REDIS_CLI="/usr/bin/redis-cli"
if [ ! -f "$REDIS_CLI" ]; then
    log_message "ERROR" "Redis客户端不存在: $REDIS_CLI"
    # 尝试查找redis-cli的位置
    REDIS_CLI_ALT=$(which redis-cli 2>/dev/null)
    if [ -n "$REDIS_CLI_ALT" ]; then
        log_message "INFO" "找到替代的Redis客户端: $REDIS_CLI_ALT"
        REDIS_CLI="$REDIS_CLI_ALT"
    else
        log_message "ERROR" "系统中未找到redis-cli"
        exit 1
    fi
fi

# 检查Redis连接的函数
check_redis_connection() {
    log_message "INFO" "尝试连接Redis: $REDIS_HOST:$REDIS_PORT"
    
    if [ -n "${REDIS_PASSWORD}" ]; then
        RESULT=$($REDIS_CLI -h ${REDIS_HOST} -p ${REDIS_PORT} -a "${REDIS_PASSWORD}" --no-auth-warning ping 2>&1)
    else
        RESULT=$($REDIS_CLI -h ${REDIS_HOST} -p ${REDIS_PORT} ping 2>&1)
    fi
    
    EXIT_CODE=$?

    # 记录命令执行结果
    log_message "DEBUG" "命令退出码: $EXIT_CODE, 结果: $RESULT"

    # 判断结果是否为PONG
    if [ "$EXIT_CODE" -eq 0 ] && [ "$RESULT" = "PONG" ]; then
        return 0  # 连接成功
    else
        return 1  # 连接失败
    fi
}

# 主检查逻辑
# 读取之前的失败次数
failures=$(get_failure_count)
log_message "INFO" "当前累积失败次数: $failures/$FAILURE_THRESHOLD"

# 检查Redis连接
if check_redis_connection; then
    log_message "INFO" "${PRODUCT_DISPLAY_NAME} Redis服务状态正常，重置失败计数"
    update_failure_count 0
    exit 0
else
    # 更新并记录失败次数
    failures=$((failures + 1))
    update_failure_count $failures
    log_message "ERROR" "${PRODUCT_DISPLAY_NAME} Redis服务检查失败，当前累积失败次数: $failures/$FAILURE_THRESHOLD"

    # 如果达到失败阈值，则退出并触发keepalived故障转移
    if [ "$failures" -ge "$FAILURE_THRESHOLD" ]; then
        log_message "ERROR" "达到失败阈值，触发keepalived故障转移"
        # 重要：返回非0退出码会使keepalived认为健康检查失败，从而触发故障转移
        exit 1
    fi

    # 未达到阈值但检查失败，也返回非0退出码
    exit 1
fi