# CloudKey 应用

CloudKey 是医疗产品云密钥系统，本文档描述了项目的目录结构和部署说明。

## 目录结构

```
products/cloudkey/
├── config/           # 所有配置文件
├── logs/             # 统一的日志目录
│   ├── app/          # 应用日志
│   ├── audit/        # 审计日志
│   ├── metrics/      # 指标日志
│   └── tomcat/       # Tomcat系统日志
├── scripts/          # 部署和维护脚本
│   ├── cloudkey-start.sh    # 启动服务（包含环境准备）
│   ├── cloudkey-stop.sh     # 停止服务
│   ├── cloudkey-restart.sh  # 重启服务
│   └── cloudkey-status.sh   # 状态检查
├── tomcat/           # Tomcat相关文件
│   ├── bin/          # 脚本文件
│   └── webapps/      # 应用文件（存放cloudkeyserver.war）
└── docker-compose.yml # 容器编排配置
```

## 环境说明

- 开发环境：Windows
- 部署环境：Linux
- JDK版本：8172u
- 应用服务器：Tomcat 9.0.100

## 部署与运维指南

### 基本部署流程

1. 将 `cloudkeyserver.war` 文件直接放置在 `tomcat/webapps` 目录下
2. 启动服务 `./scripts/cloudkey-start.sh`

### 管理服务

```bash
# 启动服务
./scripts/cloudkey-start.sh

# 停止服务（保留容器和数据）
./scripts/cloudkey-stop.sh

# 重启服务
./scripts/cloudkey-restart.sh

# 查看服务状态
./scripts/cloudkey-status.sh

# 完全卸载服务（移除容器和网络，保留数据）
./scripts/cloudkey-uninstall.sh

# 完全卸载服务并删除所有数据
./scripts/cloudkey-uninstall.sh --volumes
```

## 日志说明

所有日志统一存放在`logs`目录下，按功能分为不同子目录：
- `app`: 应用业务日志
- `audit`: 安全审计日志
- `metrics`: 性能监控指标
- `tomcat`: Tomcat服务器日志

## 配置说明

- Tomcat配置: `config/server.xml`
- 应用配置: `config/cloudkey-config.properties`
- 日志配置: `config/logback.xml`

## 故障排查

1. 检查服务状态：`./scripts/cloudkey-status.sh`
2. 查看容器日志：`docker-compose logs -f cloudkey`
3. 检查应用日志：`tail -f logs/app/*.log`
4. 检查Tomcat日志：`tail -f logs/tomcat/*.log`

## 常见问题

- **问题**: 服务无法启动
  **解决**: 检查WAR包是否存在于tomcat/webapps目录，查看日志确认错误信息

- **问题**: 访问应用出现404错误
  **解决**: 确认WAR包是否正确部署，应通过 http://localhost:8082/cloudkeyserver/ 访问应用

- **问题**: 应用响应缓慢
  **解决**: 使用`cloudkey-status.sh`检查资源使用情况，考虑调整容器资源限制

- **问题**: 权限错误
  **解决**: 确保 logs 和 config 目录有适当的写入权限（777），容器使用 root 用户运行

## 升级流程

1. 停止服务：`./scripts/cloudkey-stop.sh`
2. 备份旧版WAR包（可选）：`cp tomcat/webapps/cloudkeyserver.war tomcat/webapps/cloudkeyserver.war.bak`
3. 替换WAR包：将新版`cloudkeyserver.war`复制到`tomcat/webapps`目录
4. 启动服务：`./scripts/cloudkey-start.sh`