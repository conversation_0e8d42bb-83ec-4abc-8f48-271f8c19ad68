# EditorConfig is awesome: https://EditorConfig.org

# 顶层配置文件
root = true

# 所有文件的默认设置
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true

# Shell 脚本
[*.sh]
charset = utf-8
end_of_line = lf
indent_style = space
indent_size = 4

# Markdown 文件
[*.md]
trim_trailing_whitespace = false

# YAML 文件
[*.{yml,yaml}]
indent_style = space
indent_size = 2

# JSON 文件
[*.json]
indent_style = space
indent_size = 4

# 配置文件
[*.{conf,service,socket}]
indent_style = space
indent_size = 4

# Dockerfile
[Dockerfile]
indent_size = 4              # Dockerfile使用4空格缩进 