# JDK安装包目录

此目录用于存放预下载的JDK安装包，用于构建Docker镜像时离线安装JDK。

## 目录结构

```
jdk/
├── 8/                       # JDK 8版本目录
│   └── jdk1.8.0_172.tar.gz  # JDK 8 u172安装包
└── README.md                # 本说明文件
```

## 使用说明

1. 将对应版本的JDK安装包放置在对应的版本目录下
2. 确保JDK安装包文件名与`versions.json`和`build-jdk.sh`脚本中定义的文件名一致
3. 运行`build-jdk.sh`脚本构建JDK基础镜像

## 当前支持的版本

- JDK 8: jdk1.8.0_172.tar.gz

## 镜像标签格式

构建的Docker镜像标签格式如下：
```
<registry>/<namespace>/<repository>:<jdk-major>u<update-number>[-b<build-number>]-jdk-<variant>
```

例如：
```
192.168.200.39:1443/btit/infra/jdk:8u172-jdk-jammy
```

如果在versions.json中配置了build_number，则会生成带build号的标签：
```
192.168.200.39:1443/btit/infra/jdk:8u172-b01-jdk-jammy
```

## 注意事项

- 确保JDK安装包是官方原版，未经修改
- 安装包必须是`.tar.gz`格式
- 请遵循Oracle JDK的许可协议 