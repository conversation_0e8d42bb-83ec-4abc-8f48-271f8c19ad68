#!/bin/bash
#
# Redis部署脚本
#
# 用法: deploy-redis.sh [产品名称] [部署类型] [主节点IP]
#   产品名称: 如cloudkey
#   部署类型: master或slave
#   主节点IP: 仅在部署从节点时需要

# 基本配置
DEPLOY_TYPE=${2:-"master"}
MASTER_HOST=${3:-""}

# 设置错误时退出
set -e

# 脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
BASE_DIR="$(dirname "$SCRIPT_DIR")"
CONFIG_DIR="$BASE_DIR/config"
ENV_FILE="$CONFIG_DIR/.env"
COMPOSE_DIR="$BASE_DIR/compose"
ENV_DIR="$BASE_DIR/env"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # 无颜色

# 引入公共库函数
source "$SCRIPT_DIR/common_lib.sh"

# 产品名称（默认cloudkey）
PRODUCT_NAME=${1:-"cloudkey"}

# 环境变量设置
export COMPOSE_PROJECT_NAME="$PRODUCT_NAME-redis"

# 产品环境变量文件路径
PRODUCT_ENV_FILE="$ENV_DIR/$PRODUCT_NAME.env"

# 默认Redis配置
GENERATED_PASSWORD=$(openssl rand -base64 12)
REDIS_PASSWORD=$GENERATED_PASSWORD

# 检查并加载产品特定的环境变量文件
if [ -f "$PRODUCT_ENV_FILE" ]; then
  echo -e "${GREEN}加载产品环境配置: $PRODUCT_ENV_FILE${NC}"
  source "$PRODUCT_ENV_FILE"
  echo -e "${GREEN}已加载产品配置: REDIS_PORT=$REDIS_PORT, REDIS_PASSWORD=******${NC}"
else
  echo -e "${YELLOW}警告: 未找到产品环境配置文件: $PRODUCT_ENV_FILE${NC}"
  echo -e "${YELLOW}使用默认配置: REDIS_PORT=$REDIS_PORT${NC}"
fi

# 函数: 显示使用方法
show_usage() {
  echo "Redis部署工具"
  echo "用法: $0 [产品名称] [部署类型] [主节点IP]"
  echo "  产品名称: 如cloudkey、eseal等，默认为cloudkey"
  echo "  部署类型: master或slave，默认为master"
  echo "  主节点IP: 当部署类型为slave时，需要提供主节点的IP地址"
  echo
  echo "示例:"
  echo "  $0                      # 部署cloudkey的Redis主节点"
  echo "  $0 cloudkey master       # 部署cloudkey的Redis主节点"
  echo "  $0 cloudkey slave *************  # 部署cloudkey的Redis从节点，主节点IP为*************"
  exit 1
}

# 函数: 检查环境变量文件
check_env_file() {
  echo -e "${BLUE}[1/5] 检查环境变量配置...${NC}"
  
  # 创建配置目录
  mkdir -p "$CONFIG_DIR"
  
  # 检查是否已存在环境变量文件
  if [ -f "$ENV_FILE" ]; then
    echo -e "${GREEN}发现现有环境变量文件: $ENV_FILE${NC}"
    
    # 如果产品环境文件中已定义REDIS_PASSWORD，则优先使用
    if [ -n "$REDIS_PASSWORD" ] && [ "$REDIS_PASSWORD" != "$GENERATED_PASSWORD" ]; then
      echo -e "${GREEN}使用产品配置中的密码${NC}"
    else
      # 读取已有的REDIS_PASSWORD
      ENV_REDIS_PASSWORD=$(grep "REDIS_PASSWORD=" "$ENV_FILE" | cut -d= -f2)
      if [ -z "$ENV_REDIS_PASSWORD" ]; then
        echo -e "${YELLOW}现有环境变量文件中未找到Redis密码${NC}"
        if [ -z "$REDIS_PASSWORD" ] || [ "$REDIS_PASSWORD" = "$GENERATED_PASSWORD" ]; then
          echo -e "${YELLOW}将生成新密码${NC}"
          REDIS_PASSWORD=$GENERATED_PASSWORD
        fi
        # 更新环境变量文件
        sed -i '/REDIS_PASSWORD=/d' "$ENV_FILE" 2>/dev/null || true
        echo "REDIS_PASSWORD=$REDIS_PASSWORD" >> "$ENV_FILE"
      else
        echo -e "${GREEN}使用现有的Redis密码${NC}"
        REDIS_PASSWORD=$ENV_REDIS_PASSWORD
      fi
    fi
  else
    echo -e "${YELLOW}未找到环境变量文件，创建新文件: $ENV_FILE${NC}"
    # 创建新的环境变量文件
    echo "# Redis环境配置" > "$ENV_FILE"
    echo "REDIS_PASSWORD=$REDIS_PASSWORD" >> "$ENV_FILE"
    echo "REDIS_PORT=$REDIS_PORT" >> "$ENV_FILE"
  fi
  
  # 确保密码保存在安全的位置
  mkdir -p "$BASE_DIR/secrets"
  echo "$REDIS_PASSWORD" > "$BASE_DIR/secrets/redis_password.txt"
  chmod 600 "$BASE_DIR/secrets/redis_password.txt"
  
  echo -e "${GREEN}环境变量配置完成${NC}"
  echo -e "${GREEN}- 产品名称: $PRODUCT_NAME${NC}"
  echo -e "${GREEN}- Redis端口: $REDIS_PORT${NC}"
  return 0
}

# 函数: 检查Docker和Docker Compose是否已安装
check_dependencies() {
  echo -e "${BLUE}[2/5] 检查依赖环境...${NC}"
  
  # 使用公共库中的函数检查Docker环境
  check_docker_environment || exit 1
  
  # 继续检查其他依赖
  if ! command -v openssl &> /dev/null; then
    echo -e "${RED}错误: openssl未安装，请先安装openssl${NC}"
    exit 1
  fi
  
  echo -e "${GREEN}依赖检查通过${NC}"
  return 0
}

# 函数: 创建目录结构
create_directories() {
  echo -e "${BLUE}[3/5] 创建目录结构...${NC}"
  
  # 创建各类必要目录
  mkdir -p "$BASE_DIR/data/$PRODUCT_NAME/master" \
           "$BASE_DIR/data/$PRODUCT_NAME/slave" \
           "$BASE_DIR/logs/$PRODUCT_NAME/master" \
           "$BASE_DIR/logs/$PRODUCT_NAME/slave" \
           "$BASE_DIR/backups/$PRODUCT_NAME"
  
  # 设置正确的权限，确保容器内的Redis可以写入
  # Redis默认在容器内以UID/GID 999:999运行
  echo -e "${YELLOW}设置目录权限...${NC}"
  chown -R 999:999 "$BASE_DIR/logs/$PRODUCT_NAME"
  chmod -R 755 "$BASE_DIR/logs/$PRODUCT_NAME"
  
  chown -R 999:999 "$BASE_DIR/data/$PRODUCT_NAME"
  chmod -R 755 "$BASE_DIR/data/$PRODUCT_NAME"
  
  echo -e "${GREEN}目录结构创建完成${NC}"
  return 0
}

# 函数: 检查compose文件
check_compose_files() {
  echo -e "${BLUE}[4/5] 检查服务配置文件...${NC}"
  
  # 检查主节点配置文件
  MASTER_COMPOSE_FILE="$COMPOSE_DIR/$PRODUCT_NAME-redis-master.yml"
  if [[ "$DEPLOY_TYPE" == "master" && ! -f "$MASTER_COMPOSE_FILE" ]]; then
    echo -e "${RED}错误: 找不到主节点配置文件: $MASTER_COMPOSE_FILE${NC}"
    exit 1
  fi
  
  # 检查从节点配置文件
  SLAVE_COMPOSE_FILE="$COMPOSE_DIR/$PRODUCT_NAME-redis-slave.yml"
  if [[ "$DEPLOY_TYPE" == "slave" && ! -f "$SLAVE_COMPOSE_FILE" ]]; then
    echo -e "${RED}错误: 找不到从节点配置文件: $SLAVE_COMPOSE_FILE${NC}"
    exit 1
  fi
  
  echo -e "${GREEN}配置文件检查通过${NC}"
  return 0
}

# 函数: 启动Redis服务
start_redis() {
  echo -e "${BLUE}[5/5] 启动Redis服务...${NC}"
  
  # 获取正确的docker compose命令
  DOCKER_COMPOSE_CMD=$(get_docker_compose_cmd)
  
  # 调试信息，显示当前环境变量
  echo -e "${YELLOW}使用配置:${NC}"
  echo -e "  ${YELLOW}产品名称: ${GREEN}$PRODUCT_NAME${NC}"
  echo -e "  ${YELLOW}Redis端口: ${GREEN}$REDIS_PORT${NC}"
  echo -e "  ${YELLOW}环境文件: ${GREEN}$PRODUCT_ENV_FILE${NC}"
  
  # 确保已设置密码环境变量
  if [ -f "$BASE_DIR/secrets/redis_password.txt" ]; then
    REDIS_PASSWORD=$(cat "$BASE_DIR/secrets/redis_password.txt")
    export REDIS_PASSWORD
  else
    echo -e "${RED}错误: 找不到密码文件 $BASE_DIR/secrets/redis_password.txt${NC}"
    exit 1
  fi
  
  # 设置必要的环境变量
  export PRODUCT_NAME
  export REDIS_PORT
  
  if [[ "$DEPLOY_TYPE" == "slave" && -n "$MASTER_HOST" ]]; then
    export REDIS_MASTER_HOST=$MASTER_HOST
    export REDIS_MASTER_PORT=$REDIS_PORT
  fi
  
  if [[ "$DEPLOY_TYPE" == "master" ]]; then
    echo -e "${YELLOW}启动Redis主节点...${NC}"
    
    # 使用Docker Compose启动主节点，明确指定环境文件
    if ! $DOCKER_COMPOSE_CMD --env-file "$PRODUCT_ENV_FILE" -f "$COMPOSE_DIR/$PRODUCT_NAME-redis-master.yml" up -d; then
      echo -e "${RED}启动Redis主节点失败${NC}"
      $DOCKER_COMPOSE_CMD --env-file "$PRODUCT_ENV_FILE" -f "$COMPOSE_DIR/$PRODUCT_NAME-redis-master.yml" logs
      exit 1
    fi
    
    # 等待服务启动
    echo -e "${YELLOW}等待Redis主节点启动...${NC}"
    for i in {1..60}; do
      if docker ps | grep -q "$PRODUCT_NAME-redis-master"; then
        echo -e "${GREEN}Redis主节点已启动!${NC}"
        break
      fi
      if [ $i -eq 60 ]; then
        echo -e "${RED}Redis主节点启动超时${NC}"
        exit 1
      fi
      echo -n "."
      sleep 1
    done
    echo
    
    # 验证密码设置
    echo -e "${YELLOW}验证Redis密码配置...${NC}"
    sleep 2 # 给服务一点时间完全初始化
    
    # 验证Redis连接
    if echo "PING" | docker exec -i "$PRODUCT_NAME-redis-master" redis-cli -a "$REDIS_PASSWORD" | grep -q "PONG"; then
      echo -e "${GREEN}密码配置验证通过${NC}"
    else
      echo -e "${RED}密码配置验证失败，请检查Redis配置${NC}"
    fi
  else
    if [ -z "$MASTER_HOST" ]; then
      echo -e "${RED}错误: 部署从节点时必须提供主节点IP${NC}"
      show_usage
    fi
    
    echo -e "${YELLOW}启动Redis从节点...${NC}"
    
    # 使用Docker Compose启动从节点，明确指定环境文件
    if ! $DOCKER_COMPOSE_CMD --env-file "$PRODUCT_ENV_FILE" -f "$COMPOSE_DIR/$PRODUCT_NAME-redis-slave.yml" up -d; then
      echo -e "${RED}启动Redis从节点失败${NC}"
      $DOCKER_COMPOSE_CMD --env-file "$PRODUCT_ENV_FILE" -f "$COMPOSE_DIR/$PRODUCT_NAME-redis-slave.yml" logs
      exit 1
    fi
    
    # 等待服务启动
    echo -e "${YELLOW}等待Redis从节点启动...${NC}"
    for i in {1..30}; do
      if docker ps | grep -q "$PRODUCT_NAME-redis-slave"; then
        echo -e "${GREEN}Redis从节点已启动!${NC}"
        break
      fi
      if [ $i -eq 30 ]; then
        echo -e "${RED}Redis从节点启动超时${NC}"
        exit 1
      fi
      echo -n "."
      sleep 1
    done
    echo
    
    # 验证主从连接
    echo -e "${YELLOW}验证主从连接...${NC}"
    sleep 5 # 给服务一点时间建立主从连接
    
    # 从节点可以连接Redis服务
    if echo "PING" | docker exec -i "$PRODUCT_NAME-redis-slave" redis-cli -a "$REDIS_PASSWORD" | grep -q "PONG"; then
      echo -e "${GREEN}从节点可以连接Redis服务${NC}"
    else
      echo -e "${RED}从节点连接Redis服务失败${NC}"
    fi
    
    # 检查主从复制状态
    if docker exec -i "$PRODUCT_NAME-redis-slave" redis-cli -a "$REDIS_PASSWORD" info replication | grep -q "master_link_status:up"; then
      echo -e "${GREEN}主从复制状态正常${NC}"
    else
      echo -e "${RED}主从复制状态异常，请检查网络和配置${NC}"
      echo -e "${YELLOW}详细状态:${NC}"
      docker exec "$PRODUCT_NAME-redis-slave" redis-cli -a "$REDIS_PASSWORD" info replication
    fi
  fi
}

# 函数: 检查并开放防火墙端口
setup_firewall() {
  echo -e "${BLUE}[检查] 检查并开放防火墙端口...${NC}"
  
  # 使用公共库函数开放端口
  open_firewall_port "$REDIS_PORT"
}

# 函数: 显示部署信息
show_deployment_info() {
  # 获取主机IP
  HOST_IP=$(hostname -I | awk '{print $1}')
  
  echo -e "${BLUE}====================================${NC}"
  echo -e "${GREEN}Redis服务部署完成!${NC}"
  echo -e "${BLUE}====================================${NC}"
  echo -e "${YELLOW}产品名称: ${GREEN}$PRODUCT_NAME${NC}"
  
  if [[ "$DEPLOY_TYPE" == "master" ]]; then
    echo -e "${YELLOW}部署类型: ${GREEN}主节点${NC}"
    echo -e "${YELLOW}主节点信息:${NC}"
    echo -e "  ${YELLOW}主机IP: ${GREEN}$HOST_IP${NC}"
    echo -e "  ${YELLOW}Redis端口: ${GREEN}$REDIS_PORT${NC}"
  else
    echo -e "${YELLOW}部署类型: ${GREEN}从节点${NC}"
    echo -e "${YELLOW}从节点信息:${NC}"
    echo -e "  ${YELLOW}主节点IP: ${GREEN}$MASTER_HOST${NC}"
    echo -e "  ${YELLOW}主节点端口: ${GREEN}$REDIS_MASTER_PORT${NC}"
    echo -e "  ${YELLOW}从节点IP: ${GREEN}$HOST_IP${NC}"
    echo -e "  ${YELLOW}从节点端口: ${GREEN}$REDIS_PORT${NC}"
  fi
  
  echo -e "${BLUE}-----------------------------------${NC}"
  echo -e "${YELLOW}配置文件:${NC}"
  echo -e "  ${GREEN}$COMPOSE_DIR/$PRODUCT_NAME-redis-master.yml${NC}"
  echo -e "  ${GREEN}$COMPOSE_DIR/$PRODUCT_NAME-redis-slave.yml${NC}"
  
  echo -e "${YELLOW}数据目录:${NC}"
  echo -e "  ${GREEN}$BASE_DIR/data/$PRODUCT_NAME/${NC}"
  
  echo -e "${YELLOW}日志目录:${NC}"
  echo -e "  ${GREEN}$BASE_DIR/logs/$PRODUCT_NAME/${NC}"
  
  echo -e "${YELLOW}Redis密码:${NC}"
  echo -e "  ${GREEN}已保存在 $BASE_DIR/secrets/redis_password.txt${NC}"
  
  echo -e "${BLUE}-----------------------------------${NC}"
  echo -e "${YELLOW}服务已成功启动！${NC}"
  echo -e "${YELLOW}使用以下命令管理服务:${NC}"
  echo -e "  ${GREEN}启动: ${BLUE}$SCRIPT_DIR/service-distributed.sh $PRODUCT_NAME $DEPLOY_TYPE start${NC}"
  echo -e "  ${GREEN}停止: ${BLUE}$SCRIPT_DIR/service-distributed.sh $PRODUCT_NAME $DEPLOY_TYPE stop${NC}"
  echo -e "  ${GREEN}重启: ${BLUE}$SCRIPT_DIR/service-distributed.sh $PRODUCT_NAME $DEPLOY_TYPE restart${NC}"
  echo -e "  ${GREEN}状态: ${BLUE}$SCRIPT_DIR/service-distributed.sh $PRODUCT_NAME $DEPLOY_TYPE status${NC}"
  echo -e "${BLUE}====================================${NC}"
}

# 主函数
main() {
  # 列出可用的产品配置
  echo -e "${BLUE}可用的产品配置:${NC}"
  if [ -d "$ENV_DIR" ]; then
    FOUND_CONFIGS=false
    for env_file in "$ENV_DIR"/*.env; do
      if [ -f "$env_file" ]; then
        FOUND_CONFIGS=true
        product=$(basename "$env_file" .env)
        echo -e "  ${GREEN}- $product${NC}"
      fi
    done
    if [ "$FOUND_CONFIGS" = false ]; then
      echo -e "  ${YELLOW}未找到任何产品配置文件${NC}"
    fi
  else
    echo -e "  ${YELLOW}产品配置目录不存在: $ENV_DIR${NC}"
  fi
  echo

  # 检查部署类型
  if [[ "$DEPLOY_TYPE" != "master" && "$DEPLOY_TYPE" != "slave" ]]; then
    echo -e "${RED}错误: 部署类型必须是 'master' 或 'slave'${NC}"
    show_usage
  fi
  
  # 如果是从节点，检查是否提供了主节点IP
  if [[ "$DEPLOY_TYPE" == "slave" && -z "$MASTER_HOST" ]]; then
    echo -e "${RED}错误: 部署从节点时必须提供主节点IP${NC}"
    show_usage
  fi
  
  echo -e "${BLUE}====================================${NC}"
  echo -e "${BLUE}  Redis部署工具                   ${NC}"
  echo -e "${BLUE}====================================${NC}"
  echo -e "${YELLOW}产品名称: ${GREEN}$PRODUCT_NAME${NC}"
  echo -e "${YELLOW}部署类型: ${GREEN}$DEPLOY_TYPE${NC}"
  if [[ "$DEPLOY_TYPE" == "slave" ]]; then
    echo -e "${YELLOW}主节点IP: ${GREEN}$MASTER_HOST${NC}"
  fi
  echo
  
  # 执行部署流程
  check_dependencies
  create_directories
  check_env_file
  check_compose_files
  start_redis
  setup_firewall
  show_deployment_info
}

# 执行主函数
main 