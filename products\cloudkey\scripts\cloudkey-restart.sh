#!/bin/bash
#
# CloudKey 重启脚本
# 用于重启 CloudKey 服务
#

# 引入公共库函数
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${SCRIPT_DIR}/common_lib.sh"

# 显示帮助信息
show_help() {
  cat << EOF
用法: $(basename "$0") [选项]

CloudKey 重启脚本，用于重启 CloudKey 服务容器。

选项:
  -h, --help    显示此帮助信息并退出

功能:
  - 停止正在运行的服务
  - 启动服务
  - 验证服务是否成功重启
  
示例:
  $(basename "$0")             重启服务
  $(basename "$0") --help      显示帮助信息

相关脚本:
  cloudkey-start.sh    启动服务
  cloudkey-stop.sh     停止服务
  cloudkey-status.sh   查看服务状态
EOF
}

# 处理命令行参数
for arg in "$@"; do
  case $arg in
    -h|--help)
      show_help
      exit 0
      ;;
    *)
      echo -e "${RED}未知参数: $arg${NC}"
      echo -e "${YELLOW}使用 -h 或 --help 查看帮助信息${NC}"
      exit 1
      ;;
  esac
done

set -e

# 检查Docker环境
check_docker_environment || exit 1

# 获取Docker Compose命令
DOCKER_COMPOSE_CMD=$(get_docker_compose_cmd)

# 项目根目录（上级目录）
PROJECT_DIR="$(cd "${SCRIPT_DIR}/.." && pwd)"

# 检查服务状态
cd "${PROJECT_DIR}"
CONTAINER_ID=$($DOCKER_COMPOSE_CMD ps -q cloudkey 2>/dev/null)
if [ -z "${CONTAINER_ID}" ]; then
  echo -e "${YELLOW}CloudKey 服务未创建，将进行首次启动...${NC}"
  "${SCRIPT_DIR}/cloudkey-start.sh"
  exit $?
fi

# 重启服务
echo -e "${BLUE}正在重启 CloudKey 服务...${NC}"
$DOCKER_COMPOSE_CMD restart cloudkey

# 检查重启状态
sleep 5
if $DOCKER_COMPOSE_CMD ps | grep -q "cloudkey.*Up"; then
  echo -e "${GREEN}CloudKey 服务重启成功！${NC}"
  echo -e "${GREEN}可以通过以下地址访问: http://localhost:8082/cloudkey/${NC}"
  echo -e "${BLUE}查看日志: $DOCKER_COMPOSE_CMD logs -f cloudkey${NC}"
else
  echo -e "${YELLOW}警告: CloudKey 服务可能未正常重启，尝试停止后重新启动...${NC}"
  "${SCRIPT_DIR}/cloudkey-stop.sh"
  sleep 2
  "${SCRIPT_DIR}/cloudkey-start.sh"
fi 