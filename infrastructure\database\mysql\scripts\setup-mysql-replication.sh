#!/bin/bash
# MySQL复制配置脚本 - 支持主主互备和主从复制
# 2023-10-01
# 版本: 4.2.0

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # 无颜色

# 日志函数
log_info() {
  echo -e "${BLUE}[信息]${NC} $1"
}

log_success() {
  echo -e "${GREEN}[成功]${NC} $1"
}

log_warning() {
  echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
  echo -e "${RED}[错误]${NC} $1"
}

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
MYSQL_DIR="$(dirname "$SCRIPT_DIR")"

# 显示使用说明
show_usage() {
  echo "用法: $0 <命令> [产品名] [主节点IP] [复制模式]"
  echo ""
  echo "命令:"
  echo "  setup-slave       - 将当前节点配置为从节点，连接到指定的主节点"
  echo "  help              - 显示帮助信息"
  echo ""
  echo "参数:"
  echo "  产品名           - 要配置的产品名称 (默认: cloudkey)"
  echo "                    可选值: cloudkey, eseal, quicksign"
  echo "  主节点IP         - 主节点的IP地址"
  echo "  复制模式         - 复制模式 (默认: gtid)"
  echo "                    可选值: gtid, binlog"
  echo ""
  echo "复制模式说明:"
  echo "  1. 主从复制: 在从节点上执行本脚本，指定主节点IP"
  echo "     $0 setup-slave cloudkey *************"
  echo ""
  echo "  2. 主主复制: 在两个节点上分别执行本脚本，互相指定对方为主节点"
  echo "     # 在节点A上执行:"
  echo "     $0 setup-slave cloudkey *************"
  echo ""
  echo "     # 在节点B上执行:"
  echo "     $0 setup-slave cloudkey *************"
  echo ""
  echo "  3. 选择复制模式:"
  echo "     # 使用GTID模式（默认，推荐）:"
  echo "     $0 setup-slave cloudkey ************* gtid"
  echo ""
  echo "     # 使用传统binlog位置模式:"
  echo "     $0 setup-slave cloudkey ************* binlog"
  echo ""
  echo "     注意: GTID模式需要MySQL服务器已开启GTID功能"
  echo "           (gtid_mode=ON, enforce_gtid_consistency=ON)"
  echo ""
  echo "     注意: 主主复制需要在两个节点上设置不同的自增ID配置"
  echo "     - 节点A: auto_increment_increment=2, auto_increment_offset=1"
  echo "     - 节点B: auto_increment_increment=2, auto_increment_offset=2"
  echo ""
}

# 处理帮助参数
if [ "$1" = "--help" ] || [ "$1" = "-h" ] || [ "$1" = "help" ] || [ -z "$1" ]; then
  show_usage
  exit 0
fi

# 获取命令和参数
COMMAND="$1"
PRODUCT="${2:-cloudkey}"
MASTER_IP="${3:-}"
REPLICATION_MODE="${4:-gtid}"  # 默认使用GTID模式

# 验证复制模式
if [ "$REPLICATION_MODE" != "gtid" ] && [ "$REPLICATION_MODE" != "binlog" ]; then
  log_error "无效的复制模式: $REPLICATION_MODE"
  log_info "可用选项: gtid, binlog"
  show_usage
  exit 1
fi

# 根据执行环境确定密码文件路径
if [ -f "/run/secrets/replication_password" ]; then
  # Docker环境
  REPL_PASSWORD_FILE=${REPL_PASSWORD_FILE:-"/run/secrets/replication_password"}
  ROOT_PASSWORD_FILE=${ROOT_PASSWORD_FILE:-"/run/secrets/mysql_root_password"}
else
  # 宿主机环境
  REPL_PASSWORD_FILE="${MYSQL_DIR}/secrets/replication_password.txt"
  ROOT_PASSWORD_FILE="${MYSQL_DIR}/secrets/mysql_root_password.txt"
fi

# 检查密码文件是否存在
if [ ! -f "$REPL_PASSWORD_FILE" ]; then
  log_error "复制用户密码文件不存在: $REPL_PASSWORD_FILE"
  exit 1
fi

if [ ! -f "$ROOT_PASSWORD_FILE" ]; then
  log_error "Root密码文件不存在: $ROOT_PASSWORD_FILE"
  exit 1
fi

# 获取密码
REPL_PASSWORD=$(cat $REPL_PASSWORD_FILE)
ROOT_PASSWORD=$(cat $ROOT_PASSWORD_FILE)

# 环境变量配置
REPL_USER=${REPL_USER:-"replication_user"}
MYSQL_PORT=${MYSQL_PORT:-3306}

# 根据产品分配端口和容器名
case "$PRODUCT" in
  "cloudkey")
    MYSQL_PORT=13306
    # 尝试自动检测当前容器名
    if docker ps | grep -q "${PRODUCT}-mysql-master1"; then
      THIS_CONTAINER="${PRODUCT}-mysql-master1"
    else
      THIS_CONTAINER="${PRODUCT}-mysql-master2"
    fi
    ;;
  "eseal")
    MYSQL_PORT=13316
    if docker ps | grep -q "${PRODUCT}-mysql-master1"; then
      THIS_CONTAINER="${PRODUCT}-mysql-master1"
    else
      THIS_CONTAINER="${PRODUCT}-mysql-master2"
    fi
    ;;
  "quicksign")
    MYSQL_PORT=13326
    if docker ps | grep -q "${PRODUCT}-mysql-master1"; then
      THIS_CONTAINER="${PRODUCT}-mysql-master1"
    else
      THIS_CONTAINER="${PRODUCT}-mysql-master2"
    fi
    ;;
  *)
    MYSQL_PORT=13306
    if docker ps | grep -q "${PRODUCT}-mysql-master1"; then
      THIS_CONTAINER="${PRODUCT}-mysql-master1"
    else
      THIS_CONTAINER="${PRODUCT}-mysql-master2"
    fi
    log_warning "未知产品: $PRODUCT，使用默认端口 $MYSQL_PORT"
    ;;
esac

log_info "检测到当前容器: $THIS_CONTAINER"
log_info "复制模式: ${REPLICATION_MODE}"

# 测试远程MySQL连接
test_mysql_connection() {
  local host="$1"
  local port="$2"
  local user="$3"
  local password="$4"
  
  log_info "测试到 $host:$port 的MySQL连接..."
  
  # 使用docker exec执行MySQL命令
  docker exec -i "$THIS_CONTAINER" mysql -h"$host" -P"$port" -u"$user" -p"$password" -e "SELECT 1" &>/dev/null
  
  if [ $? -eq 0 ]; then
    log_success "成功连接到 $host:$port"
    return 0
  else
    log_error "无法连接到 $host:$port"
    log_info "请确保:"
    log_info "1. 远程MySQL服务器已启动"
    log_info "2. 远程MySQL服务器允许远程连接"
    log_info "3. 防火墙允许连接到端口 $port"
    log_info "4. 提供的用户名和密码正确"
    return 1
  fi
}

# 获取binlog位置（仅用于binlog复制模式）
get_binlog_position() {
  local host="$1"
  local port="$2"
  
  log_info "获取 $host:$port 的binlog位置..."
  
  # 使用临时文件避免日志信息干扰输出结果
  local tmp_file="/tmp/binlog_position.txt"
  
  # 使用docker exec获取binlog信息并将结果保存到临时文件
  docker exec -i "$THIS_CONTAINER" mysql -h"$host" -P"$port" -u"root" -p"$ROOT_PASSWORD" -e "SHOW MASTER STATUS\G" > "$tmp_file" 2>/dev/null
  
  # 从文件中提取文件名和位置
  local file=$(grep "File:" "$tmp_file" | awk '{print $2}')
  local position=$(grep "Position:" "$tmp_file" | awk '{print $2}')
  
  # 删除临时文件
  rm -f "$tmp_file"
  
  # 检查是否获取成功
  if [ -z "$file" ] || [ -z "$position" ]; then
    log_error "无法获取binlog位置"
    return 1
  fi
  
  log_info "Binlog文件: $file, 位置: $position"
  
  # 返回binlog信息
  echo "$file $position"
}

# 检查GTID支持
check_gtid_support() {
  local host="$1"
  local port="$2"
  
  log_info "检查 $host:$port 的GTID支持..."
  
  # 使用临时文件避免日志信息干扰输出结果
  local tmp_file="/tmp/gtid_support.txt"
  
  # 检查GTID模式是否开启
  docker exec -i "$THIS_CONTAINER" mysql -h"$host" -P"$port" -u"root" -p"$ROOT_PASSWORD" -e "SHOW GLOBAL VARIABLES LIKE 'gtid_mode';" > "$tmp_file" 2>/dev/null
  
  local gtid_mode=$(grep "gtid_mode" "$tmp_file" | awk '{print $2}')
  
  # 删除临时文件
  rm -f "$tmp_file"
  
  # 检查GTID模式
  if [ "$gtid_mode" = "ON" ]; then
    log_success "GTID模式已启用"
    return 0
  else
    log_warning "GTID模式未启用或无法检测 (检测到: $gtid_mode)"
    if [ "$REPLICATION_MODE" = "gtid" ]; then
      log_error "无法使用GTID复制模式，请先启用GTID或切换到binlog复制模式"
      log_info "要启用GTID，请在my.cnf中添加以下配置："
      log_info "gtid_mode=ON"
      log_info "enforce_gtid_consistency=ON"
      log_info "log_bin=mysql-bin"
      log_info "server_id=<唯一ID>"
      return 1
    else
      return 0
    fi
  fi
}

# 配置从节点
setup_slave_replication() {
  local master_ip="$1"
  local port="$2"
  local mode="$3"
  
  log_info "配置从节点复制，连接到主节点 $master_ip:$port..."
  log_info "复制模式: $mode"
  
  # 测试到主节点的连接
  test_mysql_connection "$master_ip" "$port" "root" "$ROOT_PASSWORD"
  if [ $? -ne 0 ]; then
    log_error "无法连接到主节点，中止复制配置"
    return 1
  fi
  
  # 如果是GTID模式，检查GTID支持
  if [ "$mode" = "gtid" ]; then
    check_gtid_support "$master_ip" "$port"
    if [ $? -ne 0 ]; then
      log_error "主节点不支持GTID，中止复制配置"
      return 1
    fi
    
    check_gtid_support "127.0.0.1" "3306"
    if [ $? -ne 0 ]; then
      log_error "从节点不支持GTID，中止复制配置"
      return 1
    fi
  fi
  
  # SQL命令前缀
  local sql="STOP SLAVE;
CHANGE MASTER TO
  MASTER_HOST='$master_ip',
  MASTER_PORT=$port,
  MASTER_USER='$REPL_USER',
  MASTER_PASSWORD='$REPL_PASSWORD'"
  
  # 根据复制模式添加不同参数
  if [ "$mode" = "gtid" ]; then
    # GTID模式
    sql="$sql,
  MASTER_AUTO_POSITION=1;"
  else
    # 传统binlog位置模式
    # 获取主节点的binlog位置
    local binlog_info=$(get_binlog_position "$master_ip" "$port")
    if [ $? -ne 0 ]; then
      log_error "无法获取主节点binlog位置，中止复制配置"
      return 1
    fi
    
    # 解析binlog信息
    local master_log_file=$(echo "$binlog_info" | awk '{print $1}')
    local master_log_pos=$(echo "$binlog_info" | awk '{print $2}')
    
    log_info "使用binlog位置: 文件=$master_log_file, 位置=$master_log_pos"
    
    sql="$sql,
  MASTER_LOG_FILE='$master_log_file',
  MASTER_LOG_POS=$master_log_pos;"
  fi
  
  # 添加启动复制的命令
  sql="$sql
START SLAVE;"
  
  log_info "执行复制配置..."
  
  # 配置从节点
  docker exec -i "$THIS_CONTAINER" mysql -h"127.0.0.1" -u"root" -p"$ROOT_PASSWORD" <<EOF
$sql
EOF
  
  if [ $? -ne 0 ]; then
    log_error "配置从节点失败"
    return 1
  fi
  
  # 检查从节点状态
  check_slave_status
  return $?
}

# 检查从节点状态
check_slave_status() {
  log_info "检查从节点状态..."
  
  # 使用临时文件避免输出干扰
  local tmp_file="/tmp/slave_status.txt"
  
  # 获取从节点状态
  docker exec -i "$THIS_CONTAINER" mysql -h"127.0.0.1" -u"root" -p"$ROOT_PASSWORD" -e "SHOW SLAVE STATUS\G" > "$tmp_file" 2>/dev/null
  
  # 检查IO和SQL线程状态
  local io_running=$(grep "Slave_IO_Running:" "$tmp_file" | awk '{print $2}')
  local sql_running=$(grep "Slave_SQL_Running:" "$tmp_file" | awk '{print $2}')
  
  if [ "$io_running" = "Yes" ] && [ "$sql_running" = "Yes" ]; then
    log_success "从节点复制成功启动"
    
    # 显示从节点复制状态详情
    cat "$tmp_file" | grep -E "Slave_IO_Running:|Slave_SQL_Running:|Master_Host:|Master_UUID:|Retrieved_Gtid_Set:|Executed_Gtid_Set:|Seconds_Behind_Master:"
    
    # 检查是否为master1或master2容器
    if [[ "$THIS_CONTAINER" == *"master"* ]]; then
      log_info "检测到当前节点可能是主节点，以下是主主复制的配置建议:"
      if [[ "$THIS_CONTAINER" == *"master1"* ]]; then
        log_info "建议配置自增ID设置 (master1):"
        log_info "  auto_increment_increment=2, auto_increment_offset=1"
      elif [[ "$THIS_CONTAINER" == *"master2"* ]]; then
        log_info "建议配置自增ID设置 (master2):"
        log_info "  auto_increment_increment=2, auto_increment_offset=2"
      fi
      
      log_info "可以使用以下命令设置自增ID配置:"
      log_info "docker exec -i $THIS_CONTAINER mysql -h127.0.0.1 -uroot -p$ROOT_PASSWORD -e \"SET GLOBAL auto_increment_increment=2; SET GLOBAL auto_increment_offset=1;\""
      log_info "注意: 请根据节点角色调整offset值"
      log_info "      这些设置在MySQL重启后会失效，建议添加到my.cnf配置文件中"
    fi
    
    # 删除临时文件
    rm -f "$tmp_file"
    return 0
  else
    log_error "从节点复制未正常启动"
    log_error "IO线程状态: $io_running"
    log_error "SQL线程状态: $sql_running"
    
    # 显示错误信息
    cat "$tmp_file" | grep -E "Slave_IO_Running:|Slave_SQL_Running:|Last_IO_Error:|Last_SQL_Error:"
    
    # 删除临时文件
    rm -f "$tmp_file"
    return 1
  fi
}

# 根据命令执行相应的操作
case "$COMMAND" in
  "setup-slave")
    # 检查必要参数
    if [ -z "$MASTER_IP" ]; then
      log_error "未指定主节点IP"
      show_usage
      exit 1
    fi
    
    # 配置从节点
    setup_slave_replication "$MASTER_IP" "$MYSQL_PORT" "$REPLICATION_MODE"
    exit $?
    ;;
    
  *)
    log_error "未知命令: $COMMAND"
    show_usage
    exit 1
    ;;
esac 