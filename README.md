# 医疗产品离线容器化部署方案

## 项目概述

本项目提供了一套完整的医疗产品离线容器化部署解决方案，专门针对云密钥系统、电子签章系统、快捷签系统等医疗相关产品在隔离网络环境下的自动化部署需求。支持 x86_64 和 aarch64 双架构部署，确保在不同硬件环境下的兼容性。

## 变更记录

| 日期 | 类型 | 内容 | 作者 |
|------|------|------|------|
| 2025-03-01 | refactor | 重构 build-tomcat.sh 脚本，优化代码结构，增强可维护性和可扩展性 | 开发团队 |
| 2025-02-28 | feat | 优化Tomcat Dockerfile模板，增强DNS配置和依赖安装，改进镜像构建过程 | 开发团队 |
| 2025-02-27 | fix | 修复build-jdk.sh脚本中的路径问题，增强错误处理和依赖检查 | 开发团队 |
| 2025-02-27 | feat | 优化build-tomcat.sh脚本，使用配置文件驱动，支持JDK/Tomcat版本兼容性检查，增强错误处理和参数配置 | 开发团队 |
| 2025-02-27 | fix | 修复 build-tomcat.sh 脚本中的路径问题，删除无用文件 | 开发团队 |
| 2025-02-27 | feat | 添加 Harbor 配置文件，支持动态配置 Harbor 仓库地址 | 开发团队 |
| 2025-02-27 | feat | 更新 JDK 和 Tomcat 构建脚本，支持从配置文件读取 Harbor 地址 | 开发团队 |
| 2025-02-27 | feat | 增强构建脚本，支持从 harbor.conf 配置文件读取 Harbor 账户和密码 | 开发团队 |

## 系统架构

整体架构分为以下几层：

1. 基础设施层（Infrastructure）
   - MySQL 数据库集群
   - Redis 缓存服务
   - Nginx 网关服务
   - Keepalived 高可用保障

2. 业务应用层（Products）
   - 云密钥系统（CloudKey）
   - 电子签章系统（ESeal）
   - 知情文书系统（QuickSign）

3. 运维管控层（Operations）
   - 日志管理
   - 高可用配置
   - 备份管理

## 待完成任务清单

### 1. Docker 基础环境搭建

- [ ] 完成 Docker 和 Docker Compose 的离线安装包准备
  - 准备 x86_64 和 aarch64 架构的二进制文件
  - 配置 systemd 服务文件
  - 优化 daemon.json 配置

### 2. 镜像仓库准备

- [ ] 搭建私有镜像仓库
  - 配置 Registry 服务
  - 准备基础设施镜像（MySQL、Redis、Nginx等）
  - 准备业务应用镜像

### 3. 基础设施层配置

- [ ] MySQL 集群配置
  - 主从复制配置
  - 备份策略实现
  - 故障转移脚本

- [ ] Redis 服务配置
  - 持久化配置
  - 访问控制配置
  - 性能优化

- [ ] Nginx 网关配置
  - SSL 证书配置
  - 负载均衡策略
  - 虚拟主机配置

- [ ] Keepalived 高可用配置
  - 故障检测脚本
  - 虚拟IP配置
  - 故障通知机制

### 4. 业务应用配置

- [ ] 云密钥系统
  - 应用配置模板
  - 日志配置
  - 监控指标配置

- [ ] 电子签章系统
  - 签署流程配置
  - 时间戳服务配置
  - 合规性日志配置

- [ ] 快捷签系统
  - 工作流配置
  - 审计日志配置
  - 业务参数配置

### 5. 运维管控配置

- [ ] 日志管理
  - 统一日志轮转策略
  - ELK 日志分析系统配置
  - 日志清理策略

- [ ] 高可用配置
  - 故障转移策略
  - 健康检查模板
  - 告警通知机制

- [ ] 备份管理
  - 中间件数据备份策略
  - 应用数据备份策略
  - 定期清理机制

### 6. 部署脚本开发

- [x] 基础构建脚本
  - 完成 build-tomcat.sh 脚本重构
  - 优化脚本结构和错误处理
  - 增强配置管理和日志记录

- [ ] 辅助功能脚本
  - 架构检测脚本
  - 依赖检查脚本
  - 环境初始化脚本

### 7. 文档编写

- [ ] 安装部署文档
  - 离线部署指南
  - 配置参考手册
  - 故障处理指南

- [ ] 运维操作文档
  - 日常运维手册
  - 备份恢复手册
  - 监控告警手册

## 技术要求

- Docker 容器化技术
- Shell 脚本编程
- 负载均衡和高可用架构
- 日志管理和分析
- 数据备份和恢复
- 安全加固和优化

## 注意事项

1. 所有配置文件必须进行版本控制
2. 重要数据必须实现备份机制
3. 所有服务必须具备监控和告警能力
4. 确保系统安全性和数据隔离
5. 保持良好的日志记录和审计跟踪

## OpenEuler容器JVM问题解决方案

在OpenEuler环境下运行Java应用容器时，可能会遇到JVM无法启动的问题。具体表现为：

```
Error occurred during initialization of VM
Cannot create VM thread. Out of system resources.
```

### 问题根因

经过测试发现，这是由于OpenEuler系统安全机制较Ubuntu等系统更为严格，对容器内进程创建和系统调用有更严格的限制导致的。主要涉及：

1. **seccomp（安全计算模式）过滤器**：限制容器内可执行的系统调用
2. **Linux安全模块（LSM）**：对应用程序的访问控制更为严格
3. **容器权限控制**：对容器内进程的权限控制更为严格

### 解决方案

我们采用了**Docker Compose配置模板方案**，为所有Java应用容器提供统一的安全配置：

```yaml
version: '3'
services:
  app:
    image: ${IMAGE}
    security_opt:
      - seccomp=unconfined
      - apparmor=unconfined
    cap_add:
      - SYS_ADMIN
    # 其他通用配置
```

这种方案的优点：
- 提供了一种标准化的配置模板，便于所有服务复用
- 无需修改每个镜像内部配置
- 适用于所有Java应用，包括JDK和Tomcat容器

### 安全注意事项

降低安全限制会带来一定风险，建议：
1. 仅为必要的Java应用容器配置这些参数
2. 在生产环境中，考虑创建更精细化的安全配置，只允许必要的系统调用
3. 长期解决方案应考虑基于OpenEuler构建专用JDK基础镜像

### 验证方法

可通过以下命令验证Java容器是否正常运行：

```bash
docker run --security-opt seccomp=unconfined --security-opt apparmor=unconfined --cap-add=SYS_ADMIN ${JDK_IMAGE} java -version
```

正常情况下应输出Java版本信息而非资源不足错误。

## 目录结构说明

请参考 `dir-structure.txt` 文件了解详细的目录结构设计。

## 贡献指南

1. 遵循项目的编码和命名规范
2. 提交前进行充分的测试
3. 保持文档的同步更新
4. 遵循 Git 工作流程

## 许可证

[待补充]
