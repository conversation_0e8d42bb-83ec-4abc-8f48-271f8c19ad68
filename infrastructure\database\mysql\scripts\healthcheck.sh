#!/bin/bash
# MySQL健康检查脚本
# 2023-10-01
# 版本: 1.0.0

# 环境变量配置
ROOT_PASSWORD_FILE=${ROOT_PASSWORD_FILE:-"/run/secrets/mysql_root_password"}
HOSTNAME=$(hostname)

# 获取密码
if [ -f "$ROOT_PASSWORD_FILE" ]; then
  ROOT_PASSWORD=$(cat $ROOT_PASSWORD_FILE)
else
  echo "错误: 找不到密码文件 $ROOT_PASSWORD_FILE"
  exit 1
fi

# 检查MySQL服务是否运行
if ! mysqladmin ping -h"localhost" -u"root" -p"$ROOT_PASSWORD" --silent; then
  echo "错误: MySQL 服务未运行"
  exit 1
fi

# 检查是否能执行SQL
if ! mysql -h"localhost" -u"root" -p"$ROOT_PASSWORD" -e "SELECT 1" >/dev/null 2>&1; then
  echo "错误: 无法执行SQL查询"
  exit 1
fi

# 检查主从复制状态(如果节点配置为从节点)
slave_status=$(mysql -h"localhost" -u"root" -p"$ROOT_PASSWORD" -e "SHOW SLAVE STATUS\G" 2>/dev/null)
if [ ! -z "$slave_status" ]; then
  io_running=$(echo "$slave_status" | grep "Slave_IO_Running:" | awk '{print $2}')
  sql_running=$(echo "$slave_status" | grep "Slave_SQL_Running:" | awk '{print $2}')
  
  if [ "$io_running" != "Yes" ] || [ "$sql_running" != "Yes" ]; then
    echo "警告: 复制状态异常 - IO: $io_running, SQL: $sql_running"
    exit 1
  fi
fi

# 检查磁盘空间
disk_usage=$(df -h /var/lib/mysql | awk 'NR==2 {print $5}' | sed 's/%//')
if [ "$disk_usage" -gt 90 ]; then
  echo "警告: 数据目录磁盘空间使用率高: ${disk_usage}%"
  exit 1
fi

# 简单性能检查 - 可用连接数
max_connections=$(mysql -h"localhost" -u"root" -p"$ROOT_PASSWORD" -e "SHOW VARIABLES LIKE 'max_connections';" | awk 'NR==2 {print $2}')
current_connections=$(mysql -h"localhost" -u"root" -p"$ROOT_PASSWORD" -e "SHOW STATUS LIKE 'Threads_connected';" | awk 'NR==2 {print $2}')
connection_usage=$((current_connections * 100 / max_connections))

if [ "$connection_usage" -gt 80 ]; then
  echo "警告: 连接使用率高: ${connection_usage}% (${current_connections}/${max_connections})"
  exit 1
fi

echo "健康检查通过: MySQL服务正常运行 [节点: $HOSTNAME]"
exit 0 