version: '3.8'

services:
  # 主节点1
  quicksign-mysql-master1:
    image: **************:1443/btit/infra/mysql:8.0.41-debian
    container_name: quicksign-mysql-master1
    hostname: quicksign-mysql-master1
    restart: always
    environment:
      - MYSQL_ROOT_PASSWORD=NetcaMySQL@2023#Strong
      - TZ=Asia/Shanghai
    volumes:
      - ../data/quicksign/master1:/var/lib/mysql
      - ../config/master1/my.cnf:/etc/mysql/conf.d/master.cnf:ro
      - ../init/quicksign:/docker-entrypoint-initdb.d
      - ../scripts:/scripts
      - ../logs/quicksign/master1:/var/log/mysql
      - ../backups/quicksign:/backups
    ports:
      - "13326:3306"
    networks:
      mysql-network:
        aliases:
          - quicksign-mysql-master1
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-uroot", "--password=NetcaMySQL@2023#Strong"]
      interval: 10s
      timeout: 5s
      retries: 5

networks:
  mysql-network:
    driver: bridge 