#!/bin/bash
# MySQL 备份容器清理脚本
# 2025-03-15
# 版本: 1.0.0
# 描述: 清理旧的MySQL备份容器，配合宿主机cron备份策略使用

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # 无颜色

# 日志函数
log_info() {
  echo -e "${BLUE}[信息]${NC} $1"
}

log_success() {
  echo -e "${GREEN}[成功]${NC} $1"
}

log_warning() {
  echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
  echo -e "${RED}[错误]${NC} $1"
}

# 显示使用帮助
show_usage() {
  echo "MySQL 备份容器清理脚本"
  echo "用法: $0 [产品名]"
  echo ""
  echo "参数:"
  echo "  产品名    - 要清理的产品备份容器 (例如: cloudkey, eseal, quicksign)"
  echo ""
  echo "示例:"
  echo "  $0 cloudkey       # 清理云密钥系统的备份容器"
  echo "  $0 eseal          # 清理电子签章系统的备份容器"
  echo ""
}

# 判断容器是否存在
container_exists() {
  local container_name="$1"
  if docker ps -a --format '{{.Names}}' | grep -q "^${container_name}$"; then
    return 0  # 容器存在
  else
    return 1  # 容器不存在
  fi
}

# 清理备份容器
cleanup_backup_container() {
  local product="$1"
  local backup_container="${product}-mysql-backup"
  
  log_info "检查 $backup_container 容器是否存在..."
  
  if container_exists "$backup_container"; then
    log_info "发现备份容器: $backup_container"
    
    # 检查容器是否在运行
    if docker ps --format '{{.Names}}' | grep -q "^${backup_container}$"; then
      log_info "停止备份容器..."
      docker stop "$backup_container"
      
      if [ $? -eq 0 ]; then
        log_success "备份容器已停止"
      else
        log_error "无法停止备份容器"
        return 1
      fi
    else
      log_info "备份容器当前未运行"
    fi
    
    # 移除容器
    log_info "移除备份容器..."
    docker rm "$backup_container"
    
    if [ $? -eq 0 ]; then
      log_success "备份容器已移除"
    else
      log_error "无法移除备份容器"
      return 1
    fi
    
    # 检查主机上的备份cron任务
    local cron_file="/etc/cron.d/${product}-mysql-backup"
    if [ -f "$cron_file" ]; then
      log_info "检测到宿主机上已配置备份定时任务: $cron_file"
      log_success "您已成功迁移到宿主机备份策略"
    else
      log_warning "未在宿主机上检测到备份定时任务"
      log_info "建议配置宿主机备份定时任务，可以使用以下命令:"
      log_info "sudo sh $(dirname "$0")/deploy.sh $product [master1|master2] # 并在提示时选择设置备份定时任务"
    fi
  else
    log_info "未发现 $backup_container 备份容器，无需清理"
  fi
  
  log_success "清理操作完成"
  return 0
}

# 主函数
main() {
  local product="$1"
  
  # 检查是否提供了产品名
  if [ -z "$product" ]; then
    log_error "未提供产品名"
    show_usage
    exit 1
  fi
  
  # 显示当前的容器列表
  echo "当前运行的容器列表:"
  docker ps
  echo ""
  echo "所有容器列表(包括未运行的):"
  docker ps -a
  echo ""
  
  # 执行清理
  cleanup_backup_container "$product"
  
  # 显示迁移到宿主机备份的信息
  echo ""
  log_info "关于宿主机备份策略:"
  log_info "1. 备份文件仍将保存在 ../backups/$product 目录"
  log_info "2. 备份日志将保存在 ../logs/$product/backup 目录"
  log_info "3. 默认备份时间为每天凌晨3点"
  log_info "4. 您可以通过编辑 /etc/cron.d/${product}-mysql-backup 文件调整备份时间"
  echo ""
  log_warning "注意: 确保宿主机cron服务正常运行以执行备份任务"
  
  exit 0
}

# 处理帮助参数
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
  show_usage
  exit 0
fi

# 执行主函数
main "$@" 