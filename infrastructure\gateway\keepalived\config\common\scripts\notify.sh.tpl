#!/bin/bash
# Keepalived状态变更通知脚本模板
# 由keepalived-config-generator.sh自动替换变量

# 产品配置变量
PRODUCT_NAME="{{PRODUCT_NAME}}"
PRODUCT_DISPLAY_NAME="{{PRODUCT_DISPLAY_NAME}}"

# Redis配置参数 - 可通过环境变量覆盖
REDIS_LOCAL_HOST=${REDIS_LOCAL_HOST:-"127.0.0.1"}
REDIS_PORT=${REDIS_PORT:-{{REDIS_PORT}}}
REDIS_PASSWORD=${REDIS_PASSWORD:-"{{REDIS_PASSWORD}}"}
VIP=""  # 将从keepalived.conf中自动获取VIP

# 日志配置
LOG_DIR="/var/log/keepalived"
LOG_FILE="${LOG_DIR}/${PRODUCT_NAME}-notify.log"

# 确保日志目录存在
mkdir -p $LOG_DIR

# 日志函数
log_message() {
    local message="$1"
    logger -t "keepalived_${PRODUCT_NAME}" "$message"
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $message" >> ${LOG_FILE}
}

# 获取当前VIP所在的主机IP
get_vip_host() {
    # 从keepalived.conf中提取VIP
    VIP=$(grep -A 2 "virtual_ipaddress" /etc/keepalived/keepalived.conf | grep -oE '[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}' | head -1)

    if [ -z "$VIP" ]; then
        log_message "无法确定VIP地址，请检查配置文件"
        return 1
    fi

    log_message "使用VIP: $VIP"
    return 0
}

# 获取另一个节点的IP地址
get_peer_ip() {
    # 从keepalived配置中查找peer地址
    PEER_IP=$(grep -A 2 "unicast_peer" /etc/keepalived/keepalived.conf | grep -oE '[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}' | head -1)

    if [ -z "$PEER_IP" ]; then
        log_message "无法从配置中确定对方节点IP"
        return 1
    fi

    log_message "对方节点IP: $PEER_IP"
    echo $PEER_IP
    return 0
}

# Redis状态变更处理函数
handle_redis_state_change() {
    local state=$1
    local instance=$2

    # 获取对方节点IP
    PEER_IP=$(get_peer_ip)
    if [ $? -ne 0 ]; then
        log_message "无法获取对方节点IP，操作中断"
        return 1
    fi

    # 根据Keepalived状态执行相应操作
    case $state in
        "MASTER")
            log_message "$instance 状态变为 MASTER - 将本地Redis切换为master角色"
            # 将Redis提升为master
            redis-cli -h $REDIS_LOCAL_HOST -p $REDIS_PORT -a "$REDIS_PASSWORD" --no-auth-warning SLAVEOF NO ONE
            if [ $? -eq 0 ]; then
                log_message "Redis已成功切换为master角色"
            else
                log_message "Redis切换为master角色失败"
            fi
            ;;
        "BACKUP")
            log_message "$instance 状态变为 BACKUP - 将本地Redis切换为slave角色"

            # 设置master认证密码
            log_message "设置Redis主节点认证密码"
            redis-cli -h $REDIS_LOCAL_HOST -p $REDIS_PORT -a "$REDIS_PASSWORD" --no-auth-warning CONFIG SET masterauth "$REDIS_PASSWORD"
            if [ $? -ne 0 ]; then
                log_message "设置masterauth失败，可能影响主从同步"
            else
                log_message "已成功设置masterauth密码"
            fi

            # 将Redis切换为slave，连接到对方节点
            redis-cli -h $REDIS_LOCAL_HOST -p $REDIS_PORT -a "$REDIS_PASSWORD" --no-auth-warning SLAVEOF $PEER_IP $REDIS_PORT
            if [ $? -eq 0 ]; then
                log_message "Redis已成功切换为slave角色，主机为 $PEER_IP:$REDIS_PORT"
            else
                log_message "Redis切换为slave角色失败"
            fi
            ;;
        "FAULT")
            log_message "$instance 状态变为 FAULT - 服务异常"
            # 在故障状态下，如果本机是slave，保持slave状态
            # 如果本机是master，不做改变，由对方节点接管
            ;;
        *)
            log_message "未知状态: $state"
            ;;
    esac
}

# 检测Redis当前角色
check_redis_role() {
    local role=$(redis-cli -h $REDIS_LOCAL_HOST -p $REDIS_PORT -a "$REDIS_PASSWORD" --no-auth-warning INFO | grep role | cut -d: -f2 | tr -d '[:space:]')
    log_message "当前Redis角色: $role"
    echo $role
}

# 主函数
main() {
    # 检查参数数量
    if [ $# -lt 2 ]; then
        log_message "用法: $0 <STATE> <INSTANCE>"
        exit 1
    fi

    # 获取参数
    STATE=$1
    INSTANCE=$2

    # 获取VIP配置
    get_vip_host

    # 记录状态变更
    log_message "Keepalived状态变更: ${PRODUCT_DISPLAY_NAME} - $INSTANCE => $STATE"

    # 记录Redis当前角色
    CURRENT_ROLE=$(check_redis_role)
    log_message "状态变更前Redis角色: $CURRENT_ROLE"

    # 处理Redis状态变更
    handle_redis_state_change "$STATE" "$INSTANCE"

    # 再次检查Redis角色，确认变更是否成功
    NEW_ROLE=$(check_redis_role)
    log_message "状态变更后Redis角色: $NEW_ROLE"
    
    # 根据不同状态执行其他自定义逻辑
    case $STATE in
        "MASTER")
            log_message "${PRODUCT_DISPLAY_NAME}成为主节点，执行额外的主节点初始化操作"
            # 可以在此添加主节点特定的初始化操作
            ;;
        "BACKUP")
            log_message "${PRODUCT_DISPLAY_NAME}成为备节点，执行额外的备节点初始化操作"
            # 可以在此添加备节点特定的初始化操作
            ;;
        "FAULT")
            log_message "${PRODUCT_DISPLAY_NAME}节点故障，执行额外的故障处理操作"
            # 可以在此添加故障处理特定操作
            ;;
    esac
}

# 执行主函数并传递所有参数
main "$@" 