version: '3'

services:
  openresty:
    image: **************:1443/btit/infra/openresty:********-3-jammy
    container_name: gateway-openresty
    restart: always
    network_mode: "host"
    volumes:
      - ./config/nginx.conf:/usr/local/openresty/nginx/conf/nginx.conf:ro
      - ./config/conf.d:/etc/nginx/conf.d:ro
      - ./config/ssl:/etc/nginx/ssl:ro
      - ./config/lua:/usr/local/openresty/nginx/lua:ro
      - ./logs:/usr/local/openresty/nginx/logs
    environment:
      - TZ=Asia/Shanghai
      - NGINX_WORKER_PROCESSES=auto
      - NGINX_WORKER_CONNECTIONS=4096
    healthcheck:
      test: ["CMD", "curl", "-k", "https://localhost:6443/status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 5s
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "3"
    ulimits:
      nofile:
        soft: 65536
        hard: 65536
