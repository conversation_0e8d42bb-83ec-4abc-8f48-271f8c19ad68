#!/bin/bash
# MySQL 部署脚本 - 简化部署流程
# 2025-03-02
# 版本: 1.0.2

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # 无颜色

# 添加 Docker Compose 命令变量
DOCKER_COMPOSE_CMD=""

# 引入路径处理库
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BASE_DIR="$(dirname "$SCRIPT_DIR")"
COMPOSE_DIR="$BASE_DIR/compose"

# 引入复制用户创建库
if [ -f "$SCRIPT_DIR/lib/create_replication_user.sh" ]; then
  source "$SCRIPT_DIR/lib/create_replication_user.sh"
else
  echo "警告: 复制用户创建库不存在，无法自动创建复制用户"
fi

# 日志函数
log_info() {
  echo -e "${BLUE}[信息]${NC} $1"
}

log_success() {
  echo -e "${GREEN}[成功]${NC} $1"
}

log_warning() {
  echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
  echo -e "${RED}[错误]${NC} $1"
}

if [ -f "$SCRIPT_DIR/lib/paths.sh" ]; then
  source "$SCRIPT_DIR/lib/paths.sh"
else
  echo "警告: 路径处理库不存在，将使用内置路径处理"
fi

# 获取产品名称和节点角色
PRODUCT="${1:-cloudkey}"
NODE_ROLE="${2:-both}"  # 默认为both，可选值: master1, master2, both

# 验证节点角色参数
if [ "$NODE_ROLE" != "master1" ] && [ "$NODE_ROLE" != "master2" ] && [ "$NODE_ROLE" != "both" ]; then
  echo -e "${RED}[错误]${NC} 节点角色参数无效: $NODE_ROLE"
  echo -e "有效的选项: master1, master2, both"
  exit 1
fi

# 根据产品分配端口
case "$PRODUCT" in
  "cloudkey")
    MYSQL_PORT=13306
    ;;
  "eseal")
    MYSQL_PORT=13316
    ;;
  "quicksign")
    MYSQL_PORT=13326
    ;;
  *)
    MYSQL_PORT=13306
    log_warning "未知产品: $PRODUCT，使用默认端口 $MYSQL_PORT"
    ;;
esac

# MySQL连接信息
MYSQL_USER="root"
MYSQL_HOST="localhost"
MYSQL_HOST2="localhost"
MYSQL_PORT2="$MYSQL_PORT"

# 全局变量
ROOT_PASSWORD=""
ROOT_PASSWORD_FILE=""

# 确定使用哪个版本的 Docker Compose 命令
determine_docker_compose_cmd() {
  # 首先尝试 Docker Compose V2
  if docker compose version &> /dev/null; then
    DOCKER_COMPOSE_CMD="docker compose"
    compose_version=$(docker compose version --short 2>/dev/null)
    log_info "使用 Docker Compose V2，版本: $compose_version"
    return 0
  fi
  
  # 如果 V2 不可用，尝试传统版本
  if command -v docker-compose &> /dev/null; then
    DOCKER_COMPOSE_CMD="docker-compose"
    compose_version=$(docker-compose version --short 2>/dev/null)
    log_info "使用传统版本 Docker Compose，版本: $compose_version"
    return 0
  fi
  
  # 如果都不可用
  log_error "未找到可用的 Docker Compose 命令"
  return 1
}

# 检测操作系统类型
detect_os() {
  log_info "检测操作系统类型..."
  
  # 尝试使用系统信息命令获取OS信息
  if [ -f /etc/os-release ]; then
    . /etc/os-release
    OS_NAME=$NAME
    OS_VERSION=$VERSION_ID
    log_info "检测到操作系统: $OS_NAME $OS_VERSION"
    
    # 判断是否为OpenEuler
    if echo "$OS_NAME" | grep -i "openeuler" > /dev/null; then
      OS_TYPE="openeuler"
      return 0
    # 判断是否为Ubuntu
    elif echo "$OS_NAME" | grep -i "ubuntu" > /dev/null; then
      OS_TYPE="ubuntu"
      return 0
    else
      OS_TYPE="unknown"
      log_warning "未识别的操作系统: $OS_NAME $OS_VERSION"
      return 1
    fi
  else
    OS_TYPE="unknown"
    log_warning "无法检测操作系统类型，将跳过端口开放步骤"
    return 1
  fi
}

# 开放端口
open_port() {
  log_info "开放MySQL端口 $MYSQL_PORT..."
  
  # 根据操作系统类型执行不同的命令
  case "$OS_TYPE" in
    "openeuler")
      # 检查firewalld是否在运行
      if systemctl is-active firewalld &> /dev/null; then
        log_info "使用firewalld开放端口..."
        
        # 检查端口是否已开放
        if firewall-cmd --list-ports | grep "$MYSQL_PORT/tcp" &> /dev/null; then
          log_info "端口 $MYSQL_PORT 已经开放"
        else
          # 使用临时规则开放端口（重启后失效）
          firewall-cmd --zone=public --add-port=$MYSQL_PORT/tcp
          
          if [ $? -eq 0 ]; then
            log_success "端口 $MYSQL_PORT 已在firewalld中临时开放"
            log_info "注意：此端口规则在系统重启后将失效"
            log_info "如需永久开放，请手动执行："
            log_info "sudo firewall-cmd --zone=public --add-port=$MYSQL_PORT/tcp --permanent"
            log_info "sudo firewall-cmd --reload"
          else
            log_error "开放端口失败，请手动检查防火墙配置"
            log_info "您可以使用以下命令手动开放端口:"
            log_info "sudo firewall-cmd --zone=public --add-port=$MYSQL_PORT/tcp"
          fi
        fi
      else
        log_warning "firewalld未运行，请确保已开放需要的端口"
        log_info "您可以使用以下命令开启firewalld并开放端口:"
        log_info "sudo systemctl start firewalld"
        log_info "sudo firewall-cmd --zone=public --add-port=$MYSQL_PORT/tcp"
      fi
      ;;
      
    "ubuntu")
      # 检查ufw是否安装并启用
      if command -v ufw &> /dev/null && ufw status | grep -q "active"; then
        log_info "使用ufw开放端口..."
        
        # 检查端口是否已开放
        if ufw status | grep "$MYSQL_PORT/tcp" &> /dev/null; then
          log_info "端口 $MYSQL_PORT 已经开放"
        else
          # 开放端口
          ufw allow $MYSQL_PORT/tcp
          
          if [ $? -eq 0 ]; then
            log_success "端口 $MYSQL_PORT 已在ufw中开放"
          else
            log_error "开放端口失败，请手动检查防火墙配置"
            log_info "您可以使用以下命令手动开放端口:"
            log_info "sudo ufw allow $MYSQL_PORT/tcp"
          fi
        fi
      else
        log_warning "ufw未启用或未安装，请确保已开放需要的端口"
        log_info "您可以使用以下命令启用ufw并开放端口:"
        log_info "sudo apt-get install ufw -y  # 如果未安装"
        log_info "sudo ufw enable"
        log_info "sudo ufw allow $MYSQL_PORT/tcp"
      fi
      ;;
      
    *)
      log_warning "未识别的操作系统，请手动确保端口 $MYSQL_PORT 已开放"
      log_info "您可以参考以下方式开放端口:"
      log_info "- 对于使用firewalld的系统 (如OpenEuler, CentOS等):"
      log_info "  sudo firewall-cmd --zone=public --add-port=$MYSQL_PORT/tcp"
      log_info "- 对于使用ufw的系统 (如Ubuntu等):"
      log_info "  sudo ufw allow $MYSQL_PORT/tcp"
      log_info "- 对于使用iptables的系统:"
      log_info "  sudo iptables -A INPUT -p tcp --dport $MYSQL_PORT -j ACCEPT"
      ;;
  esac
}

# 询问用户是否需要开放端口
ask_to_open_port() {
  local answer
  
  echo -e "${YELLOW}是否需要自动开放MySQL端口 $MYSQL_PORT?${NC} [y/N]"
  read -r answer
  
  case "$answer" in
    [Yy]*)
      detect_os
      open_port
      ;;
    *)
      log_info "跳过端口开放步骤。请确保端口 $MYSQL_PORT 已在防火墙中开放。"
      ;;
  esac
}

# 检查必要工具
check_prerequisites() {
  log_info "检查部署环境..."
  
  # 检查Docker
  if ! command -v docker &> /dev/null; then
    log_error "未安装Docker，请先安装Docker"
    return 1
  fi
  
  # 检查Docker版本
  docker_version=$(docker version --format '{{.Server.Version}}' 2>/dev/null)
  if [ $? -ne 0 ]; then
    log_error "无法获取Docker版本，请确保Docker服务正在运行"
    return 1
  fi
  log_info "检测到Docker版本: $docker_version"
  
  # 确定 Docker Compose 命令并检查版本
  if ! determine_docker_compose_cmd; then
    log_error "未安装Docker Compose，请先安装Docker Compose"
    return 1
  fi
  
  log_success "环境检查通过"
  return 0
}

# 创建必要的目录结构
create_directories() {
  log_info "创建目录结构..."
  
  local base_dir="$(dirname "$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)")"
  
  # 创建数据目录
  mkdir -p "$base_dir/data/$PRODUCT/master1" \
           "$base_dir/data/$PRODUCT/master2" \
           "$base_dir/logs/$PRODUCT/master1" \
           "$base_dir/logs/$PRODUCT/master2" \
           "$base_dir/backups/$PRODUCT"
  
  # 设置目录权限
  chmod -R 755 "$base_dir/scripts"
  
  log_success "目录结构创建完成"
}

# 设置权限
set_permissions() {
  log_info "设置脚本执行权限..."
  
  local base_dir="$(dirname "$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)")"
  
  # 设置脚本执行权限
  chmod +x "$base_dir/scripts/"*.sh
  
  log_success "脚本执行权限设置完成"
}

# 检查配置文件
check_configurations() {
  log_info "检查配置文件..."
  
  local required_files=(
    "$BASE_DIR/config/master1/my.cnf"
    "$BASE_DIR/config/master2/my.cnf"
    "$BASE_DIR/init/$PRODUCT/1_all.sql"
    "$BASE_DIR/secrets/mysql_root_password.txt"
    "$BASE_DIR/secrets/replication_password.txt"
  )
  
  for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
      log_error "缺少必要的配置文件: $file"
      return 1
    fi
  done
  
  log_success "配置文件检查通过"
  return 0
}

# 启动 MySQL 服务
start_mysql() {
  log_info "启动MySQL服务..."
  
  # 检查产品编排文件是否存在
  local master1_compose="$COMPOSE_DIR/$PRODUCT-mysql-master1.yml"
  local master2_compose="$COMPOSE_DIR/$PRODUCT-mysql-master2.yml"
  local setup_compose="$COMPOSE_DIR/$PRODUCT-setup-replication.yml"
  
  if [ ! -f "$master1_compose" ]; then
    log_error "找不到主节点1的编排文件: $master1_compose"
    return 1
  fi
  
  if [ "$NODE_ROLE" == "master2" ] || [ "$NODE_ROLE" == "both" ]; then
    if [ ! -f "$master2_compose" ]; then
      log_error "找不到主节点2的编排文件: $master2_compose"
      return 1
    fi
  fi
  
  cd "$COMPOSE_DIR" || exit 1
  
  # 检查并清理可能存在的网络
  local network_name="compose_mysql-network"
  if docker network ls | grep -q "$network_name"; then
    log_info "清理已存在的网络: $network_name"
    docker network rm "$network_name" 2>/dev/null || true
  fi
  
  # 设置超时时间（秒）
  local timeout=600
  local start_time=$(date +%s)
  
  case "$NODE_ROLE" in
    "master1")
      # 只启动master1
      log_info "启动主节点1服务..."
      # 使用后台进程启动服务
      $DOCKER_COMPOSE_CMD -f "$master1_compose" up -d --remove-orphans &
      local compose_pid=$!
      
      # 等待服务启动或超时
      while kill -0 $compose_pid 2>/dev/null; do
        local current_time=$(date +%s)
        local elapsed=$((current_time - start_time))
        if [ $elapsed -ge $timeout ]; then
          log_error "启动服务超时，正在清理..."
          kill $compose_pid 2>/dev/null
          $DOCKER_COMPOSE_CMD -f "$master1_compose" down --remove-orphans
          return 1
        fi
        sleep 1
      done
      
      wait_for_service_start "$MYSQL_HOST" "$MYSQL_PORT" "MySQL主节点1"
      log_info "MySQL主节点1服务已启动"
      check_mysql_status_master1
      
      # 检查状态结果，如果失败则返回
      if [ $? -ne 0 ]; then
        log_error "MySQL主节点1检查失败，终止后续操作"
        return 1
      fi
      
      # 添加额外等待时间，确保MySQL内部服务完全初始化
      log_info "等待MySQL内部服务完全初始化 (300秒)..."
      sleep 300
      log_info "继续执行复制用户创建..."
      
      # 创建复制用户
      if [ "$(type -t create_replication_user)" = "function" ]; then
        create_replication_user "$PRODUCT" "master1" "$BASE_DIR"
      else
        log_warning "缺少复制用户创建函数，跳过创建复制用户"
      fi
      ;;
      
    "master2")
      # 只启动master2
      log_info "启动主节点2服务..."
      # 使用后台进程启动服务
      $DOCKER_COMPOSE_CMD -f "$master2_compose" up -d --remove-orphans &
      local compose_pid=$!
      
      # 等待服务启动或超时
      while kill -0 $compose_pid 2>/dev/null; do
        local current_time=$(date +%s)
        local elapsed=$((current_time - start_time))
        if [ $elapsed -ge $timeout ]; then
          log_error "启动服务超时，正在清理..."
          kill $compose_pid 2>/dev/null
          $DOCKER_COMPOSE_CMD -f "$master2_compose" down --remove-orphans
          return 1
        fi
        sleep 1
      done
      
      wait_for_service_start "$MYSQL_HOST2" "$MYSQL_PORT2" "MySQL主节点2"
      log_info "MySQL主节点2服务已启动"
      check_mysql_status_master2
      
      # 检查状态结果，如果失败则返回
      if [ $? -ne 0 ]; then
        log_error "MySQL主节点2检查失败，终止后续操作"
        return 1
      fi
      
      # 添加额外等待时间，确保MySQL内部服务完全初始化
      log_info "等待MySQL内部服务完全初始化 (300秒)..."
      sleep 300
      log_info "继续执行复制用户创建..."
      
      # 创建复制用户
      if [ "$(type -t create_replication_user)" = "function" ]; then
        create_replication_user "$PRODUCT" "master2" "$BASE_DIR"
      else
        log_warning "缺少复制用户创建函数，跳过创建复制用户"
      fi
      ;;
      
    *)
      # 启动两个节点
      log_info "启动主节点1服务..."
      # 使用后台进程启动服务
      $DOCKER_COMPOSE_CMD -f "$master1_compose" up -d --remove-orphans &
      local compose_pid1=$!
      
      # 等待服务启动或超时
      while kill -0 $compose_pid1 2>/dev/null; do
        local current_time=$(date +%s)
        local elapsed=$((current_time - start_time))
        if [ $elapsed -ge $timeout ]; then
          log_error "启动主节点1服务超时，正在清理..."
          kill $compose_pid1 2>/dev/null
          $DOCKER_COMPOSE_CMD -f "$master1_compose" down --remove-orphans
          return 1
        fi
        sleep 1
      done
      
      log_info "启动主节点2服务..."
      # 使用后台进程启动服务
      $DOCKER_COMPOSE_CMD -f "$master2_compose" up -d --remove-orphans &
      local compose_pid2=$!
      
      # 等待服务启动或超时
      while kill -0 $compose_pid2 2>/dev/null; do
        local current_time=$(date +%s)
        local elapsed=$((current_time - start_time))
        if [ $elapsed -ge $timeout ]; then
          log_error "启动主节点2服务超时，正在清理..."
          kill $compose_pid2 2>/dev/null
          $DOCKER_COMPOSE_CMD -f "$master2_compose" down --remove-orphans
          return 1
        fi
        sleep 1
      done
      
      wait_for_service_start "$MYSQL_HOST" "$MYSQL_PORT" "MySQL主节点1"
      wait_for_service_start "$MYSQL_HOST2" "$MYSQL_PORT2" "MySQL主节点2"
      
      log_info "MySQL主从服务均已启动"
      check_mysql_status_both
      
      # 检查状态结果，如果失败则返回
      if [ $? -ne 0 ]; then
        log_error "MySQL主节点状态检查失败，终止后续操作"
        return 1
      fi
      
      # 添加额外等待时间，确保MySQL内部服务完全初始化
      log_info "等待MySQL内部服务完全初始化 (300秒)..."
      sleep 300
      log_info "继续执行复制用户创建..."
      
      # 创建复制用户 (在两个节点上都创建)
      if [ "$(type -t create_replication_user)" = "function" ]; then
        create_replication_user "$PRODUCT" "both" "$BASE_DIR"
      else
        log_warning "缺少复制用户创建函数，跳过创建复制用户"
      fi
      
      # 配置主主复制
      log_info "开始配置主主复制..."
      if [ -f "$setup_compose" ]; then
        $DOCKER_COMPOSE_CMD -f "$setup_compose" up --abort-on-container-exit
        
        if [ $? -eq 0 ]; then
          log_success "MySQL主主复制配置成功"
        else
          log_error "MySQL主主复制配置失败，请检查日志"
        fi
      else
        log_warning "找不到复制配置文件: $setup_compose"
        log_info "尝试使用通用复制配置..."
        setup_master_master_replication
      fi
      ;;
  esac
  
  # 设置备份cron任务
  setup_backup_cron
}

# 检查服务状态
check_service_status() {
  log_info "检查MySQL服务状态..."
  
  local master1_compose="$COMPOSE_DIR/$PRODUCT-mysql-master1.yml"
  local master2_compose="$COMPOSE_DIR/$PRODUCT-mysql-master2.yml"
  
  case "$NODE_ROLE" in
    "master1")
      (cd "$COMPOSE_DIR" && $DOCKER_COMPOSE_CMD -f "$master1_compose" ps)
      ;;
    "master2")
      (cd "$COMPOSE_DIR" && $DOCKER_COMPOSE_CMD -f "$master2_compose" ps)
      ;;
    "both")
      log_info "主节点1状态:"
      (cd "$COMPOSE_DIR" && $DOCKER_COMPOSE_CMD -f "$master1_compose" ps)
      log_info "主节点2状态:"
      (cd "$COMPOSE_DIR" && $DOCKER_COMPOSE_CMD -f "$master2_compose" ps)
      ;;
  esac
  
  if [ $? -ne 0 ]; then
    log_warning "无法检查服务状态"
  fi
}

# 显示部署信息
show_deployment_info() {
  log_info "MySQL部署信息:"
  
  echo ""
  echo -e "${GREEN}部署信息:${NC}"
  echo -e "  产品名称: $PRODUCT"
  echo -e "  部署节点: $NODE_ROLE"
  echo -e "  端口配置: $MYSQL_PORT"
  echo -e "  数据目录: $BASE_DIR/data/$PRODUCT"
  echo -e "  日志目录: $BASE_DIR/logs/$PRODUCT"
  echo -e "  备份目录: $BASE_DIR/backups/$PRODUCT"
  echo -e "  管理员账号: root"
  echo -e "  管理员密码: $ROOT_PASSWORD"
  echo ""
  echo -e "${GREEN}备份配置信息:${NC}"
  echo -e "  备份频率: 每天凌晨3点自动执行"
  echo -e "  备份日志: $BASE_DIR/logs/$PRODUCT/backup/backup.log"
  echo -e "  备份保留期: 7天（可在backup.sh脚本中修改BACKUP_RETENTION_DAYS变量）"
  echo ""
  echo -e "${GREEN}推荐操作方式:${NC}"
  echo -e "  日常维护请使用维护脚本: ${SCRIPT_DIR}/maintenance.sh $PRODUCT"
  echo -e "  该脚本提供了便捷的服务启动、停止、重启等功能，以及其他维护选项"
  
  # 添加单节点部署说明
  if [ "$NODE_ROLE" != "both" ]; then
    echo ""
    echo -e "${YELLOW}[注意]${NC} 您选择了只部署单个节点 ($NODE_ROLE)。"
  fi
  
  echo ""
  echo -e "${GREEN}备用命令(仅供特殊情况使用):${NC}"
  
  local master1_compose="$COMPOSE_DIR/$PRODUCT-mysql-master1.yml"
  local master2_compose="$COMPOSE_DIR/$PRODUCT-mysql-master2.yml"
  
  case "$NODE_ROLE" in
    "master1")
      echo -e "  启动服务: cd $COMPOSE_DIR && $DOCKER_COMPOSE_CMD -f $master1_compose up -d"
      echo -e "  停止服务: cd $COMPOSE_DIR && $DOCKER_COMPOSE_CMD -f $master1_compose down"
      echo -e "  进入容器: docker exec -it ${PRODUCT}-mysql-master1 bash"
      echo -e "  执行SQL:  docker exec -it ${PRODUCT}-mysql-master1 mysql -uroot -p$ROOT_PASSWORD"
      ;;
    "master2")
      echo -e "  启动服务: cd $COMPOSE_DIR && $DOCKER_COMPOSE_CMD -f $master2_compose up -d"
      echo -e "  停止服务: cd $COMPOSE_DIR && $DOCKER_COMPOSE_CMD -f $master2_compose down"
      echo -e "  进入容器: docker exec -it ${PRODUCT}-mysql-master2 bash"
      echo -e "  执行SQL:  docker exec -it ${PRODUCT}-mysql-master2 mysql -uroot -p$ROOT_PASSWORD"
      ;;
    "both")
      echo -e "  启动服务:"
      echo -e "    cd $COMPOSE_DIR && $DOCKER_COMPOSE_CMD -f $master1_compose up -d"
      echo -e "    cd $COMPOSE_DIR && $DOCKER_COMPOSE_CMD -f $master2_compose up -d"
      echo -e "  停止服务:"
      echo -e "    cd $COMPOSE_DIR && $DOCKER_COMPOSE_CMD -f $master1_compose down"
      echo -e "    cd $COMPOSE_DIR && $DOCKER_COMPOSE_CMD -f $master2_compose down"
      echo -e "  查看状态:"
      echo -e "    cd $COMPOSE_DIR && $DOCKER_COMPOSE_CMD -f $master1_compose ps"
      echo -e "    cd $COMPOSE_DIR && $DOCKER_COMPOSE_CMD -f $master2_compose ps"
      echo -e "  进入容器: docker exec -it ${PRODUCT}-mysql-master1 bash"
      echo -e "  执行SQL:  docker exec -it ${PRODUCT}-mysql-master1 mysql -uroot -p$ROOT_PASSWORD"
      ;;
  esac
}

# 清理旧的配置文件
cleanup_old_config() {
  log_info "检查部署配置..."
  
  # 根据部署模式进行相应操作
  case "$NODE_ROLE" in
    "master1")
      # 如果只部署master1，提示用户
      log_info "当前只部署主节点1 (master1)"
      ;;
    "master2")
      # 如果只部署master2，提示用户
      log_info "当前只部署主节点2 (master2)"
      ;;
    "both")
      # both模式，提示用户
      log_info "当前部署模式: 双主节点 (master1 + master2)"
      ;;
  esac
}

# 异常退出处理
handle_exit() {
  local exit_code=$?
  if [ $exit_code -ne 0 ]; then
    log_error "脚本执行过程中发生错误，退出运行..."
  fi
  exit $exit_code
}

# 确认操作
confirm_action() {
  local message="$1"
  local default_choice="$2"
  
  # 设置默认选项
  if [ -z "$default_choice" ]; then
    default_choice="Y"
  fi
  
  local prompt=""
  if [ "$default_choice" = "Y" ]; then
    prompt="${YELLOW}${message} [Y/n]:${NC} "
  else
    prompt="${YELLOW}${message} [y/N]:${NC} "
  fi
  
  # 显示带颜色的提示
  echo -en "$prompt"
  read -r choice
  
  # 如果用户没有输入，使用默认选项
  if [ -z "$choice" ]; then
    choice="$default_choice"
  fi
  
  # 转换为大写以便比较
  choice=$(echo "$choice" | tr '[:lower:]' '[:upper:]')
  
  if [ "$choice" = "Y" ] || [ "$choice" = "YES" ]; then
    return 0
  else
    return 1
  fi
}

# 主函数
main() {
  # 设置trap捕获异常退出
  trap handle_exit ERR
  
  # 获取root密码
  ROOT_PASSWORD_FILE="${BASE_DIR}/secrets/mysql_root_password.txt"
  if [ ! -f "$ROOT_PASSWORD_FILE" ]; then
    log_error "MySQL root密码文件不存在: $ROOT_PASSWORD_FILE"
    return 1
  fi
  
  ROOT_PASSWORD=$(cat "$ROOT_PASSWORD_FILE")
  
  echo -e "${GREEN}===== MySQL 部署脚本开始执行 ====${NC}"
  echo -e "部署产品: $PRODUCT (端口: $MYSQL_PORT)"
  echo -e "部署节点: $NODE_ROLE"
  
  check_prerequisites || exit 1
  create_directories || exit 1
  set_permissions || exit 1
  check_configurations || exit 1
  
  # 根据节点角色管理相应容器
  case "$NODE_ROLE" in
    "master1")
      # 如果指定只部署master1，则停止可能存在的master2容器
      log_info "检查并停止可能存在的主节点2容器..."
      local master2_container="${PRODUCT}-mysql-master2"
      if docker ps -a | grep -q "$master2_container"; then
        log_warning "检测到主节点2容器 ($master2_container) 已存在，将停止它"
        docker stop "$master2_container" 2>/dev/null || true
      fi
      ;;
    "master2")
      # 如果指定只部署master2，则停止可能存在的master1容器
      log_info "检查并停止可能存在的主节点1容器..."
      local master1_container="${PRODUCT}-mysql-master1"
      if docker ps -a | grep -q "$master1_container"; then
        log_warning "检测到主节点1容器 ($master1_container) 已存在，将停止它"
        docker stop "$master1_container" 2>/dev/null || true
      fi
      ;;
  esac
  
  # 检查部署配置
  cleanup_old_config
  
  # 询问是否开放端口
  ask_to_open_port
  
  start_mysql || exit 1
  
  # 等待服务启动
  log_info "等待服务启动完成..."
  sleep 10
  
  check_service_status
  
  # 询问是否设置自动备份
  if confirm_action "是否设置自动备份定时任务(每天凌晨3点)?"; then
    setup_backup_cron
  else
    log_info "跳过备份定时任务设置"
  fi
  
  show_deployment_info
  
  # 如果只部署了单个节点，提示用户如何完成主主复制设置
  if [ "$NODE_ROLE" != "both" ]; then
    echo -e "${YELLOW}[注意]${NC} 您选择了只部署单个节点 ($NODE_ROLE)。"
    echo -e "要完成主主复制配置，请确保:"
    echo -e "1. 在另一台服务器上也部署对应的另一个节点"
    echo -e "2. 两台服务器能够相互访问MySQL端口 ($MYSQL_PORT)"
    echo -e "3. 部署完成后，在其中一台服务器上执行主主复制配置脚本:"
    echo -e "   ./setup-master-master.sh $PRODUCT <主节点1_IP> <主节点2_IP>"
  fi
  
  echo -e "${GREEN}===== MySQL 部署脚本执行完成 ====${NC}"
}

# 显示使用帮助
show_usage() {
  echo "用法: $0 [产品名] [节点角色]"
  echo ""
  echo "参数:"
  echo "  产品名    - 要部署的产品名称 (默认: cloudkey)"
  echo "             可选值: cloudkey, eseal, quicksign"
  echo "  节点角色  - 要部署的节点角色 (默认: both)"
  echo "             可选值: master1, master2, both"
  echo ""
  echo "示例:"
  echo "  $0 cloudkey master1    # 部署云密钥系统的主节点1"
  echo "  $0 eseal master2       # 部署电子签章系统的主节点2"
  echo "  $0 quicksign both      # 同时部署快捷签系统的两个节点（仅用于测试环境）"
  echo ""
}

# 处理帮助参数
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
  show_usage
  exit 0
fi

# 等待MySQL服务启动
wait_for_service_start() {
  local host="$1"
  local port="$2"
  local service_name="$3"
  local max_attempts=30
  local attempt=1
  
  log_info "等待$service_name服务启动，最多等待${max_attempts}秒..."
  
  while [ $attempt -le $max_attempts ]; do
    # 使用纯bash方式检查端口可用性
    (echo > /dev/tcp/$host/$port) >/dev/null 2>&1
    if [ $? -eq 0 ]; then
      log_success "$service_name服务已成功启动"
      return 0
    fi
    
    log_info "等待$service_name服务启动中 ($attempt/$max_attempts)..."
    attempt=$((attempt + 1))
    sleep 1
  done
  
  log_error "$service_name服务启动超时，请检查日志"
  return 1
}

# 检查MySQL服务状态
check_mysql_status() {
  local host="$1"
  local port="$2"
  local user="$3"
  local password="NetcaMySQL@2023#Strong"
  local node_name="$5"
  
  log_info "检查$node_name服务状态..."
  
  # 检查容器状态
  local container_name=""
  if [[ "$node_name" == "主节点1" ]]; then
    container_name="${PRODUCT}-mysql-master1"
  elif [[ "$node_name" == "主节点2" ]]; then
    container_name="${PRODUCT}-mysql-master2"
  else
    container_name="${PRODUCT}-mysql-${node_name#*节点}"
  fi
  
  log_info "检查容器: $container_name"
  local container_status=$(docker inspect -f '{{.State.Status}}' "$container_name" 2>/dev/null)
  
  if [ "$container_status" != "running" ]; then
    # 如果获取不到状态（为空），则显示更明确的信息
    if [ -z "$container_status" ]; then
      log_error "$node_name容器未找到或状态获取失败"
      log_info "尝试查找容器: $container_name"
    else
      log_error "$node_name容器未正常运行，状态为: $container_status"
    fi
    return 1
  fi
  
  log_info "容器状态: $container_status"
  
  # 使用纯bash方式检查端口可用性
  log_info "检查端口 $port 可用性..."
  (echo > /dev/tcp/$host/$port) >/dev/null 2>&1
  if [ $? -ne 0 ]; then
    log_error "$node_name服务未正常监听端口 $port"
    return 1
  fi
  
  log_info "端口 $port 可访问"
  
  # 检查容器健康状态
  log_info "检查容器健康状态..."
  local health_status=$(docker inspect --format='{{.State.Health.Status}}' "$container_name" 2>/dev/null)
  if [ -n "$health_status" ]; then
    log_info "$node_name容器健康状态: $health_status"
    # 如果状态是starting，给它多一点时间
    if [ "$health_status" = "starting" ]; then
      log_info "容器健康检查正在初始化，等待10秒..."
      sleep 10
    fi
  fi
  
  # 尝试执行MySQL命令，不使用-it参数，防止在脚本中执行时出错
  log_info "尝试执行SQL命令测试连接..."
  local sql_output=$(docker exec -i "$container_name" mysql  -u"$user" -p"$password" -e "SELECT 1" 2>&1)
  local sql_status=$?
  
  if [ $sql_status -eq 0 ]; then
    log_success "$node_name服务运行正常"
    return 0
  else
    log_error "$node_name服务无法正常执行SQL命令"
    log_error "错误信息: $sql_output"
    
    # 尝试检查MySQL错误日志
    log_info "尝试查看MySQL错误日志..."
    docker exec -i "$container_name" cat /var/log/mysql/error.log 2>/dev/null | tail -n 20 || true
    
    # 尝试不带密码参数格式直接进入容器测试
    log_info "尝试另一种方式连接MySQL..."
    docker exec -i "$container_name" mysql -e "SELECT 1" &>/dev/null
    if [ $? -eq 0 ]; then
      log_info "不带密码参数的连接成功，可能是密码格式问题"
    fi
    
    # 检查容器网络配置
    log_info "检查容器网络配置..."
    docker inspect --format='{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}' "$container_name"
    
    return 1
  fi
}

# 检查主节点1状态
check_mysql_status_master1() {
  check_mysql_status "$MYSQL_HOST" "$MYSQL_PORT" "$MYSQL_USER" "" "主节点1"
}

# 检查主节点2状态
check_mysql_status_master2() {
  check_mysql_status "$MYSQL_HOST2" "$MYSQL_PORT2" "$MYSQL_USER" "" "主节点2"
}

# 设置备份cron任务
setup_backup_cron() {
  log_info "设置数据库备份定时任务..."
  
  local master1_compose="$COMPOSE_DIR/$PRODUCT-mysql-master1.yml"
  local master2_compose="$COMPOSE_DIR/$PRODUCT-mysql-master2.yml"
  
  # 创建备份日志目录
  mkdir -p "$BASE_DIR/logs/$PRODUCT/backup"
  
  # 创建临时cron配置文件
  local cron_file="/tmp/${PRODUCT}_mysql_backup_cron"
  local log_file="$BASE_DIR/logs/$PRODUCT/backup/backup.log"
  
  # 根据节点角色设置备份命令
  local backup_command=""
  case "$NODE_ROLE" in
    "master1")
      backup_command="cd $COMPOSE_DIR && $DOCKER_COMPOSE_CMD -f $master1_compose exec -T $PRODUCT-mysql-master1 /scripts/backup.sh $PRODUCT"
      ;;
    "master2")
      backup_command="cd $COMPOSE_DIR && $DOCKER_COMPOSE_CMD -f $master2_compose exec -T $PRODUCT-mysql-master2 /scripts/backup.sh $PRODUCT"
      ;;
    "both")
      backup_command="cd $COMPOSE_DIR && $DOCKER_COMPOSE_CMD -f $master1_compose exec -T $PRODUCT-mysql-master1 /scripts/backup.sh $PRODUCT"
      ;;
  esac
  
  # 创建cron配置 - 设置为凌晨3点
  echo "0 3 * * * root $backup_command > $log_file 2>&1" > "$cron_file"
  
  # 复制到cron.d目录(需要root权限)
  log_info "将在以下位置创建cron任务: /etc/cron.d/${PRODUCT}-mysql-backup"
  
  # 使用sudo复制文件
  sudo cp "$cron_file" "/etc/cron.d/${PRODUCT}-mysql-backup"
  sudo chmod 644 "/etc/cron.d/${PRODUCT}-mysql-backup"
  rm -f "$cron_file"
  
  if [ $? -eq 0 ]; then
    log_success "备份定时任务已设置: 每天凌晨3点自动备份"
    log_info "备份日志将保存在: $log_file"
    log_info "备份文件将保存在: $BASE_DIR/backups/$PRODUCT 目录"
  else
    log_warning "无法设置备份定时任务，您可以手动添加以下内容到crontab:"
    echo "0 3 * * * $backup_command > $log_file 2>&1"
  fi
}

# 配置主主复制
setup_master_master_replication() {
  log_info "配置MySQL主主复制..."
  
  local setup_compose="$COMPOSE_DIR/setup-replication.yml"
  
  # 检查默认复制配置文件是否存在
  if [ ! -f "$setup_compose" ]; then
    log_error "找不到复制配置文件: $setup_compose"
    return 1
  fi
  
  # 等待MySQL服务完全启动
  log_info "等待MySQL服务完全就绪，准备配置复制..."
  sleep 15
  
  # 执行复制配置
  log_info "开始配置主主复制..."
  (cd "$COMPOSE_DIR" && $DOCKER_COMPOSE_CMD -f "$setup_compose" up --abort-on-container-exit)
  
  if [ $? -eq 0 ]; then
    log_success "MySQL主主复制配置成功"
  else
    log_error "MySQL主主复制配置失败，请检查日志"
    return 1
  fi
  
  return 0
}

# 检查主节点1和主节点2状态
check_mysql_status_both() {
  check_mysql_status_master1
  if [ $? -ne 0 ]; then
    return 1
  fi
  
  check_mysql_status_master2
  if [ $? -ne 0 ]; then
    return 1
  fi
  
  return 0
}

# 执行主函数
main "$@" 