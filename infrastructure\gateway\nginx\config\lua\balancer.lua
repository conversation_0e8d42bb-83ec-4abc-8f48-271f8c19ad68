-- 负载均衡器脚本
local balancer = require "ngx.balancer"

-- 设置超时
local ok, err = balancer.set_timeouts(3, 10, 10)
if not ok then
    ngx.log(ngx.ERR, "设置超时失败: ", err)
end

-- 获取目标主机和端口
local target_host = ngx.ctx.target_host
local target_port = ngx.ctx.target_port

-- 如果没有健康目标，使用默认代理
if not target_host then
    ngx.log(ngx.WARN, "未找到健康的目标服务器，使用默认代理")
    return
end

-- 设置当前请求的目标服务器
local ok, err = balancer.set_current_peer(target_host, target_port)
if not ok then
    ngx.log(ngx.ERR, "设置后端服务器失败: ", err)
end 