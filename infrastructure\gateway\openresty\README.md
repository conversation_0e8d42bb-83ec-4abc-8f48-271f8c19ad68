# OpenResty 服务

本目录包含OpenResty服务的配置、脚本和相关资源，用于作为API网关和负载均衡器，管理对云密钥系统的访问。

## 目录结构

```
infrastructure/gateway/openresty/
├── config/                  # 配置目录
│   ├── conf.d/              # Nginx配置文件目录
│   │   └── cloudkey.conf    # 云密钥系统的Nginx配置
│   ├── lua/                 # Lua脚本目录
│   │   ├── health_init.lua  # 健康检查初始化脚本
│   │   ├── health_utils.lua # 健康检查工具函数
│   │   └── route_select.lua # 路由选择脚本
│   └── ssl/                 # SSL证书目录
│       ├── nginx.cer        # SSL证书
│       └── nginx_cert.key   # SSL私钥
├── logs/                    # 日志目录
├── scripts/                 # 运维脚本目录
│   └── control.sh           # 服务控制脚本
└── docker-compose.yml       # Docker Compose编排文件
```

## 功能特点

- **负载均衡**: 使用智能负载均衡算法分发请求至多个后端服务节点
- **SSL终端**: 支持HTTPS协议，提供TLS 1.2/1.3安全连接
- **健康检查**: 自动监控后端服务健康状态，剔除不健康节点
- **智能路由**: 基于Lua脚本的动态路由决策
- **服务监控**: 提供服务状态监控接口和健康检查API
- **高性能**: 基于OpenResty的高性能代理服务

## 配置说明

### 主要配置文件

- **docker-compose.yml**: 定义OpenResty服务的容器配置、网络设置和卷挂载
- **config/conf.d/cloudkey.conf**: 云密钥系统的Nginx配置，包含upstream定义、SSL配置、路由规则等
- **config/lua/*.lua**: Lua脚本文件，实现健康检查、负载均衡和智能路由等功能

### 后端服务配置

默认配置指向以下后端服务器：
- 192.168.200.137:8082
- 192.168.200.138:8082

如需修改后端服务器配置，请同时更新以下文件：
1. `config/conf.d/cloudkey.conf` 中的 `upstream cloudkey_backend` 块
2. `config/lua/health_init.lua` 中的健康检查目标服务器配置

## 使用方法

### 控制脚本

提供了便捷的控制脚本用于管理OpenResty服务，分为服务级命令和容器级命令：

#### 服务级命令（更快，推荐日常使用）

这些命令直接操作OpenResty进程，不会重建容器，适合频繁使用：

```bash
# 启动OpenResty服务进程（容器未运行时会自动启动容器）
./scripts/control.sh start

# 停止OpenResty服务进程（容器仍在运行）
./scripts/control.sh stop

# 重启OpenResty服务进程
./scripts/control.sh restart

# 重载配置（无需重启服务）
./scripts/control.sh reload

# 测试配置文件语法
./scripts/control.sh test

# 显示完整配置
./scripts/control.sh showconf
```

#### 容器级命令（更全面，适用于初始部署或环境重建）

这些命令操作Docker容器本身，适合环境初始化或完整重建：

```bash
# 启动OpenResty容器
./scripts/control.sh container_start

# 停止并移除OpenResty容器
./scripts/control.sh container_stop

# 重启OpenResty容器（完全重建）
./scripts/control.sh container_restart

# 查看容器状态
./scripts/control.sh status

# 查看容器日志
./scripts/control.sh logs
```

### 健康检查API

- **状态查询**: `/status` - 显示所有后端服务器的健康状态
- **手动检查**: `/check?target=IP:PORT` - 手动检查指定服务器的健康状态
- **健康端点**: `/health` - 用于上层负载均衡器检查OpenResty自身的健康状态

## 扩展与维护

- 添加新的后端服务需同时更新Nginx配置和Lua脚本
- 日志文件位于 `logs/` 目录，默认配置了日志轮转
- 可通过修改 `config/lua/health_utils.lua` 调整健康检查的判断逻辑和参数 