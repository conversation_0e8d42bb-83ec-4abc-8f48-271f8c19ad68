#!/bin/bash
# MySQL 通用函数库
# 包含所有脚本共享的函数和变量

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # 无颜色

# 目录路径定义
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BASE_DIR="$(dirname "$SCRIPT_DIR")"
COMPOSE_DIR="$BASE_DIR/compose"
DATA_DIR="$BASE_DIR/data"
CONFIG_DIR="$BASE_DIR/config"
BACKUP_DIR="$BASE_DIR/backups"
SECRETS_DIR="$BASE_DIR/secrets"

# 引入路径处理库(如果存在)
if [ -f "$SCRIPT_DIR/lib/paths.sh" ]; then
  source "$SCRIPT_DIR/lib/paths.sh"
fi

# 日志函数
log_info() {
  echo -e "${BLUE}[信息]${NC} $1"
}

log_success() {
  echo -e "${GREEN}[成功]${NC} $1"
}

log_warning() {
  echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
  echo -e "${RED}[错误]${NC} $1"
}

# 产品名称验证函数
validate_product_name() {
  local product="$1"
  
  # 检查名称有效性
  if [ -z "$product" ]; then
    log_error "产品名称不能为空"
    return 1
  fi
  
  # 确认是支持的产品
  case "$product" in
    cloudkey|eseal|quicksign)
      return 0
      ;;
    *)
      log_warning "未知产品名称: $product"
      # 返回0，以便可以尝试继续执行，但会记录警告
      return 0
      ;;
  esac
}

# 获取MySQL端口号
get_mysql_port() {
  local product="$1"
  
  case "$product" in
    "cloudkey")
      echo 13306
      ;;
    "eseal")
      echo 13316
      ;;
    "quicksign")
      echo 13326
      ;;
    *)
      echo 13306  # 默认端口
      ;;
  esac
}

# 检查Compose文件是否存在
check_compose_file() {
  local product="$1"
  local compose_file="$COMPOSE_DIR/${product}-mysql.yml"
  
  if [ ! -f "$compose_file" ]; then
    log_error "找不到Compose文件: $compose_file"
    echo "在 $COMPOSE_DIR 下找到的Compose文件:"
    ls -1 "$COMPOSE_DIR"/*.yml 2>/dev/null || echo "  没有找到任何Compose文件"
    return 1
  fi
  
  return 0
}

# 检查容器状态
check_container_status() {
  local container_name="$1"
  
  if docker ps --filter "name=$container_name" --format "{{.Names}}" | grep -q "$container_name"; then
    return 0  # 容器运行中
  else
    return 1  # 容器未运行
  fi
}

# 其他共用函数可以在这里添加 