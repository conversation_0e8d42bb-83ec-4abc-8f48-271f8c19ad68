#!/bin/bash
#
# Redis 卸载脚本
# 用于卸载Redis服务，可选择是否保留数据
#

# 设置错误时退出
set -e

# 脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
BASE_DIR="$(dirname "$SCRIPT_DIR")"
COMPOSE_DIR="$BASE_DIR/compose"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # 无颜色

# 引入公共库函数
source "$SCRIPT_DIR/common_lib.sh"

# 默认参数
PRODUCT_NAME=${1:-"cloudkey"}
NODE_TYPE=${2:-"both"}
REMOVE_DATA=0

# 显示帮助信息
function show_help() {
  echo "Redis服务卸载脚本"
  echo "用法: $0 [产品名称] [节点类型] [--remove-data|--keep-data]"
  echo
  echo "参数:"
  echo "  产品名称: cloudkey, eseal, quicksign (默认: cloudkey)"
  echo "  节点类型: both, master, slave (默认: both)"
  echo "  数据选项:"
  echo "    --remove-data: 删除所有数据目录和配置"
  echo "    --keep-data: 保留数据目录 (默认)"
  echo
  echo "示例:"
  echo "  $0 cloudkey both --keep-data     # 卸载cloudkey的主从节点，保留数据"
  echo "  $0 cloudkey master --remove-data # 卸载cloudkey的主节点，删除所有数据"
  echo "  $0 eseal slave                   # 卸载eseal的从节点，保留数据"
  exit 0
}

# 解析参数
for arg in "$@"; do
  case $arg in
    -h|--help)
      show_help
      ;;
    --remove-data)
      REMOVE_DATA=1
      ;;
    --keep-data)
      REMOVE_DATA=0
      ;;
  esac
done

# 检查节点类型
if [[ "$NODE_TYPE" != "master" && "$NODE_TYPE" != "slave" && "$NODE_TYPE" != "both" ]]; then
  echo -e "${RED}错误: 节点类型必须是 'master', 'slave' 或 'both'${NC}"
  show_help
  exit 1
fi

# 欢迎信息
echo -e "${BLUE}====================================${NC}"
echo -e "${BLUE}  Redis 卸载工具                  ${NC}"
echo -e "${BLUE}====================================${NC}"
echo -e "${YELLOW}产品名称: ${GREEN}$PRODUCT_NAME${NC}"
echo -e "${YELLOW}节点类型: ${GREEN}$NODE_TYPE${NC}"
if [ $REMOVE_DATA -eq 1 ]; then
  echo -e "${YELLOW}数据处理: ${RED}删除所有数据${NC}"
else
  echo -e "${YELLOW}数据处理: ${GREEN}保留数据${NC}"
fi
echo

# 确认卸载
echo -e "${RED}警告: 此操作将停止并移除Redis服务!${NC}"
if [ $REMOVE_DATA -eq 1 ]; then
  echo -e "${RED}警告: 所有数据和配置将被永久删除!${NC}"
fi
echo -e "${YELLOW}确定要继续吗? (yes/no)${NC}"
read -r confirm
if [[ "$confirm" != "yes" ]]; then
  echo -e "${YELLOW}操作已取消${NC}"
  exit 0
fi

# 获取Docker Compose命令
DOCKER_COMPOSE_CMD=$(get_docker_compose_cmd) || { echo "退出卸载"; exit 1; }

# 停止并移除容器
echo -e "${BLUE}[1/3] 停止Redis服务...${NC}"
$DOCKER_COMPOSE_CMD -f "$COMPOSE_DIR/$PRODUCT_NAME-redis-master.yml" down
$DOCKER_COMPOSE_CMD -f "$COMPOSE_DIR/$PRODUCT_NAME-redis-slave.yml" down

# 处理数据目录
echo -e "${BLUE}[2/3] 处理数据和日志目录...${NC}"
if [ $REMOVE_DATA -eq 1 ]; then
  # 删除数据和日志目录
  if [[ "$NODE_TYPE" == "master" || "$NODE_TYPE" == "both" ]]; then
    if [ -d "$BASE_DIR/data/$PRODUCT_NAME/master" ]; then
      echo -e "${YELLOW}删除主节点数据目录...${NC}"
      rm -rf "$BASE_DIR/data/$PRODUCT_NAME/master"
    fi
    if [ -d "$BASE_DIR/logs/$PRODUCT_NAME/master" ]; then
      echo -e "${YELLOW}删除主节点日志目录...${NC}"
      rm -rf "$BASE_DIR/logs/$PRODUCT_NAME/master"
    fi
  fi
  
  if [[ "$NODE_TYPE" == "slave" || "$NODE_TYPE" == "both" ]]; then
    if [ -d "$BASE_DIR/data/$PRODUCT_NAME/slave" ]; then
      echo -e "${YELLOW}删除从节点数据目录...${NC}"
      rm -rf "$BASE_DIR/data/$PRODUCT_NAME/slave"
    fi
    if [ -d "$BASE_DIR/logs/$PRODUCT_NAME/slave" ]; then
      echo -e "${YELLOW}删除从节点日志目录...${NC}"
      rm -rf "$BASE_DIR/logs/$PRODUCT_NAME/slave"
    fi
  fi
  
  # 如果是both且要删除全部数据，则删除备份和密码
  if [[ "$NODE_TYPE" == "both" ]]; then
    if [ -d "$BASE_DIR/backups/$PRODUCT_NAME" ]; then
      echo -e "${YELLOW}删除备份目录...${NC}"
      rm -rf "$BASE_DIR/backups/$PRODUCT_NAME"
    fi
    if [ -f "$BASE_DIR/secrets/redis_password.txt" ]; then
      echo -e "${YELLOW}删除密码文件...${NC}"
      rm -f "$BASE_DIR/secrets/redis_password.txt"
    fi
  fi
  
  echo -e "${GREEN}数据目录已删除${NC}"
else
  echo -e "${GREEN}保留所有数据目录${NC}"
fi

# 清理完成
echo -e "${BLUE}[3/3] 清理完成...${NC}"

# 显示卸载结果
echo
echo -e "${BLUE}====================================${NC}"
echo -e "${GREEN}Redis服务卸载完成!${NC}"
echo -e "${BLUE}====================================${NC}"
echo -e "${YELLOW}产品名称: ${GREEN}$PRODUCT_NAME${NC}"
echo -e "${YELLOW}节点类型: ${GREEN}$NODE_TYPE${NC}"
if [ $REMOVE_DATA -eq 1 ]; then
  echo -e "${YELLOW}数据处理: ${RED}已删除所有数据${NC}"
else
  echo -e "${YELLOW}数据处理: ${GREEN}已保留所有数据${NC}"
  echo -e "${YELLOW}数据路径: ${GREEN}$BASE_DIR/data/$PRODUCT_NAME/${NC}"
fi
echo
echo -e "${BLUE}====================================${NC}" 