---
description:  此规则定义了日常开发中的Git工作流程规范。   确保代码变更被正确追踪和同步
globs: **/.git/**/*,**/.gitignore,**/.gitattributes,**/.github/**/*,**/.gitlab-ci.yml,**/hooks/**/*,**/.husky/**/*,**/CHANGELOG.md,**/VERSION, .git/config, .gitmodules
alwaysApply: false
---
# Git工作流规则

description: |
  此规则定义了日常开发中的Git工作流程规范。
  确保代码变更被正确追踪和同步。

references:
  - "@git.mdc"
  - "@remote.mdc"

rules:
  - 变更管理：
    - 每次功能开发或bug修复必须形成完整的提交
    - 相关文件的修改必须在同一个提交中完成
    - 禁止将不相关的变更混在一起提交
  
  - 提交规范：
    - 每完成一个独立的功能点或修复后，必须立即提交
    - 提交信息格式：
      ```
      <type>(<scope>): <subject>

      <body>
      ```
    - type 类型：
      - feat: 新功能
      - fix: 修复bug
      - docs: 文档更新
      - style: 代码格式调整
      - refactor: 重构
      - test: 测试相关
      - chore: 构建过程或辅助工具的变动
    - scope: 变更影响的范围（可选）
    - subject: 简短描述，不超过50个字符
    - body: 详细说明（可选）

  - 提交流程：
    - 完成代码修改后立即执行 git add .
    - 使用规范的格式提交 git commit -m "type(scope): subject"
    - 提交后立即推送到远程 git push

  - 同步策略：
    - 推送前必须先拉取最新代码：git pull --rebase
    - 如遇冲突，必须完整解决后再继续
    - 确保本地分支与远程保持同步

  - 最佳实践：
    - 频繁小步提交，保持提交粒度适中
    - 每个提交都应该是可编译和可测试的
    - 提交信息要清晰描述变更内容和原因
    - 相关文件的修改要放在同一个提交中

  - 禁止事项：
    - 禁止提交未完成的代码
    - 禁止提交不符合规范的代码
    - 禁止提交未经测试的代码
    - 禁止提交敏感信息（密码、密钥等）

examples:
  - 正确的提交信息：
    ```
    feat(docker): 添加离线安装脚本
    
    - 支持x86_64和aarch64架构
    - 配置本地镜像仓库
    - 添加安装验证功能
    ```
  - 错误的提交信息：
    ```
    update some files
    ``` 