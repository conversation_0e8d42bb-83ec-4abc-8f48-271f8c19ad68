#!/bin/bash
# 批量构建操作系统镜像脚本

set -e

# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
# 获取项目根目录
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../../.." && pwd)"

# 定义颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # 无颜色

# 定义目录路径
LIB_DIR="${PROJECT_ROOT}/docker/scripts/lib"
BUILD_LIB_DIR="${PROJECT_ROOT}/docker/scripts/lib"
OS_BASE_DIR="${PROJECT_ROOT}/docker/infrastructure/os/ubuntu"
CONFIG_FILE="${PROJECT_ROOT}/docker/scripts/build/config/harbor.conf"
VERSION_FILE="${SCRIPT_DIR}/versions/os_versions.json"

# 显示帮助信息
show_help() {
    echo -e "${GREEN}操作系统镜像批量构建工具${NC}"
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help                显示帮助信息"
    echo "  --harbor-registry=<地址>  指定Harbor仓库地址"
    echo "  --harbor-user=<用户名>    指定Harbor用户名"
    echo "  --harbor-password=<密码>  指定Harbor密码"
    echo "  --no-push                 构建镜像但不推送到仓库"
    echo "  --filter=<过滤器>         仅构建指定的操作系统(逗号分隔)"
    echo "                            例如: --filter=ubuntu,centos"
    echo "  --install-card-mgr        安装密码卡管理的JNI库"
    echo "  --install-sc62            安装SansecCard_SC62组件"
    echo "  --install-sc34            安装SansecCard_SC34组件(暂未实现)"
    echo "  --install-all-cards       安装所有支持的密码卡组件"
    echo ""
    echo "示例:"
    echo "  $0                                      # 构建所有支持的OS镜像"
    echo "  $0 --harbor-registry=docker.example.com # 使用指定的Harbor仓库"
    echo "  $0 --filter=ubuntu,alpine               # 仅构建Ubuntu和Alpine镜像"
    echo "  $0 --install-card-mgr --install-sc62    # 构建所有OS镜像并安装指定的密码卡组件"
    exit 0
}

# 解析命令行参数
parse_args() {
    BUILD_ARGS=""
    FILTER=""
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                ;;
            --harbor-registry=*)
                BUILD_ARGS="$BUILD_ARGS --harbor-registry=${1#*=}"
                shift
                ;;
            --harbor-registry)
                BUILD_ARGS="$BUILD_ARGS --harbor-registry=$2"
                shift 2
                ;;
            --harbor-user=*)
                BUILD_ARGS="$BUILD_ARGS --harbor-user=${1#*=}"
                shift
                ;;
            --harbor-user)
                BUILD_ARGS="$BUILD_ARGS --harbor-user=$2"
                shift 2
                ;;
            --harbor-password=*)
                BUILD_ARGS="$BUILD_ARGS --harbor-password=${1#*=}"
                shift
                ;;
            --harbor-password)
                BUILD_ARGS="$BUILD_ARGS --harbor-password=$2"
                shift 2
                ;;
            --no-push)
                BUILD_ARGS="$BUILD_ARGS --no-push"
                shift
                ;;
            --filter=*)
                FILTER="${1#*=}"
                shift
                ;;
            --filter)
                FILTER="$2"
                shift 2
                ;;
            --install-card-mgr)
                BUILD_ARGS="$BUILD_ARGS --install-card-mgr"
                shift
                ;;
            --install-sc62)
                BUILD_ARGS="$BUILD_ARGS --install-sc62"
                shift
                ;;
            --install-sc34)
                BUILD_ARGS="$BUILD_ARGS --install-sc34"
                shift
                ;;
            --install-all-cards)
                BUILD_ARGS="$BUILD_ARGS --install-all-cards"
                shift
                ;;
            *)
                echo -e "${RED}错误: 未知选项 $1${NC}"
                show_help
                ;;
        esac
    done
}

# 检查构建脚本是否存在
check_build_script() {
    if [ ! -f "$BUILD_SCRIPT" ]; then
        echo -e "${RED}错误: 构建脚本不存在: $BUILD_SCRIPT${NC}"
        exit 1
    fi
    
    if [ ! -x "$BUILD_SCRIPT" ]; then
        echo -e "${YELLOW}警告: 构建脚本没有执行权限，正在添加...${NC}"
        chmod +x "$BUILD_SCRIPT"
    fi
}

# 获取支持的操作系统列表
get_os_list() {
    # 检查版本配置文件
    if [ ! -f "$VERSION_FILE" ]; then
        echo "错误: 版本配置文件不存在: $VERSION_FILE"
        exit 1
    fi
    
    # 获取所有支持的版本
    local versions=($(jq -r '.versions[].version' "$VERSION_FILE"))
    if [ ${#versions[@]} -eq 0 ]; then
        echo "错误: 未找到支持的操作系统版本"
        exit 1
    fi
    
    # 如果指定了过滤器，应用过滤
    if [ -n "$FILTER" ]; then
        local filtered_versions=()
        for version in "${versions[@]}"
        do
            if [[ "$version" == *"$FILTER"* ]]; then
                filtered_versions+=("$version")
            fi
        done
        versions=("${filtered_versions[@]}")
    fi
    
    echo "${versions[@]}"
}

# 构建指定操作系统的所有版本
build_os_versions() {
    local os=$1
    echo -e "${GREEN}======== 开始构建 $os 镜像 ========${NC}"
    
    # 检查版本配置文件
    local VERSION_FILE="$BASE_DIR/$os/versions.json"
    if [ ! -f "$VERSION_FILE" ]; then
        echo -e "${RED}错误: $os 版本配置文件不存在: $VERSION_FILE${NC}"
        return 1
    fi
    
    # 获取所有支持的版本
    local VERSIONS=($(jq -r '.versions[].version' "$VERSION_FILE" 2>/dev/null))
    if [ ${#VERSIONS[@]} -eq 0 ]; then
        echo -e "${RED}错误: $os 没有支持的版本${NC}"
        return 1
    fi
    
    echo "发现 $os 支持的版本: ${VERSIONS[*]}"
    
    # 获取默认架构
    local DEFAULT_ARCH=$(jq -r '.default_arch // "amd64"' "$VERSION_FILE")
    
    # 构建每个版本
    for version in "${VERSIONS[@]}"; do
        # 获取该版本支持的架构
        local ARCHES=($(jq -r ".versions[] | select(.version == \"$version\") | .architectures[]" "$VERSION_FILE" 2>/dev/null))
        if [ ${#ARCHES[@]} -eq 0 ]; then
            echo -e "${YELLOW}警告: $os $version 没有指定支持的架构，使用默认架构: $DEFAULT_ARCH${NC}"
            ARCHES=("$DEFAULT_ARCH")
        fi
        
        # 构建每个架构
        for arch in "${ARCHES[@]}"; do
            echo -e "${GREEN}------ 构建 $os $version $arch ------${NC}"
            $BUILD_SCRIPT $BUILD_ARGS "$os" "$version" "$arch"
            
            # 检查构建结果
            if [ $? -ne 0 ]; then
                echo -e "${RED}错误: $os $version $arch 构建失败${NC}"
            else
                echo -e "${GREEN}$os $version $arch 构建成功${NC}"
            fi
        done
    done
    
    echo -e "${GREEN}======== $os 所有版本构建完成 ========${NC}"
}

# 主函数
main() {
    # 解析命令行参数
    parse_args "$@"
    
    # 检查构建脚本
    check_build_script
    
    # 获取操作系统列表
    get_os_list
    
    # 检查是否有找到OS
    if [ ${#OS_DIRS[@]} -eq 0 ]; then
        echo -e "${RED}错误: 未找到任何操作系统${NC}"
        exit 1
    fi
    
    # 统计要构建的总数
    local TOTAL_COUNT=0
    for os in "${OS_DIRS[@]}"; do
        local VERSION_FILE="$BASE_DIR/$os/versions.json"
        if [ ! -f "$VERSION_FILE" ]; then
            continue
        fi
        
        local VERSIONS=($(jq -r '.versions[].version' "$VERSION_FILE" 2>/dev/null))
        if [ ${#VERSIONS[@]} -eq 0 ]; then
            continue
        fi
        
        for version in "${VERSIONS[@]}"; do
            local ARCHES=($(jq -r ".versions[] | select(.version == \"$version\") | .architectures[]" "$VERSION_FILE" 2>/dev/null))
            if [ ${#ARCHES[@]} -eq 0 ]; then
                local DEFAULT_ARCH=$(jq -r '.default_arch // "amd64"' "$VERSION_FILE")
                ARCHES=("$DEFAULT_ARCH")
            fi
            
            TOTAL_COUNT=$((TOTAL_COUNT + ${#ARCHES[@]}))
        done
    done
    
    echo -e "${GREEN}将构建 ${#OS_DIRS[@]} 个操作系统，共 $TOTAL_COUNT 个版本/架构组合${NC}"
    
    # 显示传递的构建参数
    if [[ $BUILD_ARGS == *"--install-card-mgr"* ]]; then
        echo -e "${YELLOW}将安装密码卡管理JNI库${NC}"
    fi
    if [[ $BUILD_ARGS == *"--install-sc62"* ]]; then
        echo -e "${YELLOW}将安装SansecCard_SC62组件${NC}"
    fi
    if [[ $BUILD_ARGS == *"--install-sc34"* ]]; then
        echo -e "${YELLOW}将安装SansecCard_SC34组件${NC}"
    fi
    if [[ $BUILD_ARGS == *"--install-all-cards"* ]]; then
        echo -e "${YELLOW}将安装所有支持的密码卡组件${NC}"
    fi
    
    read -p "是否继续构建? [y/N] " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "已取消构建"
        exit 0
    fi
    
    # 构建每个操作系统的所有版本
    for os in "${OS_DIRS[@]}"; do
        build_os_versions "$os"
    done
    
    echo -e "${GREEN}========= 所有操作系统构建完成 ==========${NC}"
}

# 执行主函数
main "$@" 