#!/bin/bash
# MySQL 监控脚本 - 简化监控流程
# 2025-03-02
# 版本: 1.0.0

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # 无颜色

# 配置
PRODUCT="${1:-cloudkey}"
MYSQL_DIR="$(dirname "$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)")"
COMPOSE_DIR="$MYSQL_DIR/compose"
MASTER1="$PRODUCT-mysql-master1"
MASTER2="$PRODUCT-mysql-master2"
ROOT_PASSWORD_FILE="${MYSQL_DIR}/secrets/mysql_root_password.txt"
REPORT_FILE="/tmp/${PRODUCT}_mysql_report_$(date +%Y%m%d).html"

# 根据产品分配端口
case "$PRODUCT" in
  "cloudkey")
    MYSQL_PORT=13306
    ;;
  "eseal")
    MYSQL_PORT=13316
    ;;
  "quicksign")
    MYSQL_PORT=13326
    ;;
  *)
    MYSQL_PORT=13306
    echo -e "${YELLOW}[警告]${NC} 未知产品: $PRODUCT，使用默认端口 $MYSQL_PORT"
    ;;
esac

# 日志函数
log_info() {
  echo -e "${BLUE}[信息]${NC} $1"
}

log_success() {
  echo -e "${GREEN}[成功]${NC} $1"
}

log_warning() {
  echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
  echo -e "${RED}[错误]${NC} $1"
}

# 检查节点是否运行中
is_node_running() {
  local node=$1
  if docker ps --filter "name=$node" --format "{{.Names}}" | grep -q "$node"; then
    return 0
  else
    return 1
  fi
}

# 获取活动节点
get_active_node() {
  if is_node_running "$MASTER1"; then
    echo "$MASTER1"
  elif is_node_running "$MASTER2"; then
    echo "$MASTER2"
  else
    echo ""
  fi
}

# 初始化HTML报告
init_html_report() {
  cat > "$REPORT_FILE" << EOF
<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>MySQL健康报告 - $PRODUCT</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    h1, h2 { color: #2c3e50; }
    .container { max-width: 1200px; margin: 0 auto; }
    .section { margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 4px; }
    .success { color: green; }
    .warning { color: orange; }
    .error { color: red; }
    table { width: 100%; border-collapse: collapse; margin-top: 10px; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
    tr:nth-child(even) { background-color: #f9f9f9; }
    .timestamp { text-align: right; color: #7f8c8d; margin-top: 30px; }
  </style>
</head>
<body>
  <div class="container">
    <h1>MySQL 健康报告</h1>
    <div class="timestamp">生成时间: $(date '+%Y-%m-%d %H:%M:%S')</div>
    <div class="section">
      <h2>系统概览</h2>
      <p>产品名称: $PRODUCT</p>
      <p>服务端口: $MYSQL_PORT</p>
      <p>报告文件: $REPORT_FILE</p>
    </div>
EOF
}

# 添加HTML部分
add_html_section() {
  local title=$1
  local content=$2
  local status=${3:-""}
  
  local status_class=""
  if [ "$status" = "success" ]; then
    status_class="success"
  elif [ "$status" = "warning" ]; then
    status_class="warning"
  elif [ "$status" = "error" ]; then
    status_class="error"
  fi
  
  cat >> "$REPORT_FILE" << EOF
    <div class="section">
      <h2>$title</h2>
      <div class="$status_class">$content</div>
    </div>
EOF
}

# 添加HTML表格
add_html_table() {
  local title=$1
  local headers=$2
  local data=$3
  
  cat >> "$REPORT_FILE" << EOF
    <div class="section">
      <h2>$title</h2>
      <table>
        <tr>
EOF

  # 添加表头
  IFS='|' read -ra HEADER_ARRAY <<< "$headers"
  for header in "${HEADER_ARRAY[@]}"; do
    echo "<th>$header</th>" >> "$REPORT_FILE"
  done
  
  echo "</tr>" >> "$REPORT_FILE"
  
  # 添加数据行
  echo "$data" >> "$REPORT_FILE"
  
  cat >> "$REPORT_FILE" << EOF
      </table>
    </div>
EOF
}

# 完成HTML报告
finalize_html_report() {
  cat >> "$REPORT_FILE" << EOF
    <div class="timestamp">报告生成完毕 - $(date '+%Y-%m-%d %H:%M:%S')</div>
  </div>
</body>
</html>
EOF

  log_success "健康报告已生成: $REPORT_FILE"
}

# 检查服务状态并添加到报告
check_service_status() {
  log_info "检查MySQL服务状态..."
  
  local service_status=""
  local status_class=""
  
  local master1_running=$(docker ps --filter "name=$MASTER1" --format "{{.Status}}" | grep -i "up")
  local master2_running=$(docker ps --filter "name=$MASTER2" --format "{{.Status}}" | grep -i "up")
  
  if [ -n "$master1_running" ] && [ -n "$master2_running" ]; then
    service_status="<p class='success'>所有MySQL服务正常运行</p>"
    status_class="success"
  elif [ -z "$master1_running" ] && [ -z "$master2_running" ]; then
    service_status="<p class='error'>错误: 所有MySQL服务未运行</p>"
    status_class="error"
  else
    service_status="<p class='warning'>警告: 部分MySQL服务未运行</p>"
    if [ -z "$master1_running" ]; then
      service_status+="<p class='warning'>- 主节点1 ($MASTER1) 未运行</p>"
    else
      service_status+="<p class='success'>- 主节点1 ($MASTER1) 正常运行</p>"
    fi
    
    if [ -z "$master2_running" ]; then
      service_status+="<p class='warning'>- 主节点2 ($MASTER2) 未运行</p>"
    else
      service_status+="<p class='success'>- 主节点2 ($MASTER2) 正常运行</p>"
    fi
    status_class="warning"
  fi
  
  add_html_section "服务状态" "$service_status" "$status_class"
}

# 检查复制状态并添加到报告
check_replication_status() {
  log_info "检查主主复制状态..."
  
  local replication_table_data=""
  local replication_status="<p>主主复制状态检查:</p>"
  local status_class="success"
  
  # 检查主节点1
  if is_node_running "$MASTER1"; then
    local slave_io=$(docker exec $MASTER1 mysql -uroot -p$(cat "$ROOT_PASSWORD_FILE") -e "SHOW SLAVE STATUS\G" | grep "Slave_IO_Running:" | awk '{print $2}')
    local slave_sql=$(docker exec $MASTER1 mysql -uroot -p$(cat "$ROOT_PASSWORD_FILE") -e "SHOW SLAVE STATUS\G" | grep "Slave_SQL_Running:" | awk '{print $2}')
    local seconds_behind=$(docker exec $MASTER1 mysql -uroot -p$(cat "$ROOT_PASSWORD_FILE") -e "SHOW SLAVE STATUS\G" | grep "Seconds_Behind_Master:" | awk '{print $2}')
    
    if [ "$slave_io" = "Yes" ] && [ "$slave_sql" = "Yes" ]; then
      if [ "$seconds_behind" = "0" ]; then
        replication_table_data+="<tr><td>$MASTER1</td><td class='success'>正常</td><td class='success'>$slave_io</td><td class='success'>$slave_sql</td><td class='success'>$seconds_behind</td></tr>"
      else
        replication_table_data+="<tr><td>$MASTER1</td><td class='warning'>延迟</td><td class='success'>$slave_io</td><td class='success'>$slave_sql</td><td class='warning'>$seconds_behind</td></tr>"
        status_class="warning"
      fi
    else
      replication_table_data+="<tr><td>$MASTER1</td><td class='error'>错误</td><td class='${slave_io}'>$slave_io</td><td class='${slave_sql}'>$slave_sql</td><td>$seconds_behind</td></tr>"
      status_class="error"
    fi
  else
    replication_table_data+="<tr><td>$MASTER1</td><td class='error'>未运行</td><td class='error'>N/A</td><td class='error'>N/A</td><td class='error'>N/A</td></tr>"
    status_class="error"
  fi
  
  # 检查主节点2
  if is_node_running "$MASTER2"; then
    local slave_io=$(docker exec $MASTER2 mysql -uroot -p$(cat "$ROOT_PASSWORD_FILE") -e "SHOW SLAVE STATUS\G" | grep "Slave_IO_Running:" | awk '{print $2}')
    local slave_sql=$(docker exec $MASTER2 mysql -uroot -p$(cat "$ROOT_PASSWORD_FILE") -e "SHOW SLAVE STATUS\G" | grep "Slave_SQL_Running:" | awk '{print $2}')
    local seconds_behind=$(docker exec $MASTER2 mysql -uroot -p$(cat "$ROOT_PASSWORD_FILE") -e "SHOW SLAVE STATUS\G" | grep "Seconds_Behind_Master:" | awk '{print $2}')
    
    if [ "$slave_io" = "Yes" ] && [ "$slave_sql" = "Yes" ]; then
      if [ "$seconds_behind" = "0" ]; then
        replication_table_data+="<tr><td>$MASTER2</td><td class='success'>正常</td><td class='success'>$slave_io</td><td class='success'>$slave_sql</td><td class='success'>$seconds_behind</td></tr>"
      else
        replication_table_data+="<tr><td>$MASTER2</td><td class='warning'>延迟</td><td class='success'>$slave_io</td><td class='success'>$slave_sql</td><td class='warning'>$seconds_behind</td></tr>"
        [ "$status_class" = "success" ] && status_class="warning"
      fi
    else
      replication_table_data+="<tr><td>$MASTER2</td><td class='error'>错误</td><td class='${slave_io}'>$slave_io</td><td class='${slave_sql}'>$slave_sql</td><td>$seconds_behind</td></tr>"
      status_class="error"
    fi
  else
    replication_table_data+="<tr><td>$MASTER2</td><td class='error'>未运行</td><td class='error'>N/A</td><td class='error'>N/A</td><td class='error'>N/A</td></tr>"
    status_class="error"
  fi
  
  add_html_table "复制状态" "节点|状态|IO线程|SQL线程|延迟(秒)" "$replication_table_data"
}

# 检查连接状态并添加到报告
check_connections() {
  log_info "检查数据库连接状态..."
  
  local node=$(get_active_node)
  if [ -z "$node" ]; then
    add_html_section "连接状态" "<p class='error'>错误: 无法获取连接信息，所有节点均未运行</p>" "error"
    return 1
  fi
  
  log_info "使用节点 $node 检查连接状态..."
  
  local max_connections=$(docker exec $node mysql -uroot -p$(cat "$ROOT_PASSWORD_FILE") -e "SHOW VARIABLES LIKE 'max_connections';" | grep max_connections | awk '{print $2}')
  local current_connections=$(docker exec $node mysql -uroot -p$(cat "$ROOT_PASSWORD_FILE") -e "SHOW STATUS LIKE 'Threads_connected';" | grep Threads_connected | awk '{print $2}')
  local max_used_connections=$(docker exec $node mysql -uroot -p$(cat "$ROOT_PASSWORD_FILE") -e "SHOW STATUS LIKE 'Max_used_connections';" | grep Max_used_connections | awk '{print $2}')
  
  local connection_pct=$((current_connections * 100 / max_connections))
  local max_used_pct=$((max_used_connections * 100 / max_connections))
  
  local status_class="success"
  if [ $connection_pct -gt 80 ] || [ $max_used_pct -gt 90 ]; then
    status_class="warning"
  fi
  
  local connection_data="<tr><td>当前连接数</td><td>$current_connections</td><td>${connection_pct}%</td></tr>"
  connection_data+="<tr><td>最大使用连接数</td><td>$max_used_connections</td><td>${max_used_pct}%</td></tr>"
  connection_data+="<tr><td>最大允许连接数</td><td>$max_connections</td><td>100%</td></tr>"
  
  add_html_table "连接状态" "指标|值|百分比" "$connection_data"
}

# 检查表状态并添加到报告
check_tables() {
  log_info "检查数据库表状态..."
  
  local node=$(get_active_node)
  if [ -z "$node" ]; then
    add_html_section "表状态" "<p class='error'>错误: 无法获取表信息，所有节点均未运行</p>" "error"
    return 1
  fi
  
  log_info "使用节点 $node 检查表状态..."
  
  local table_data=""
  local table_status=$(docker exec $node mysql -uroot -p$(cat "$ROOT_PASSWORD_FILE") -e "USE ${PRODUCT}; SHOW TABLE STATUS;" 2>/dev/null)
  
  if [ $? -ne 0 ]; then
    add_html_section "表状态" "<p class='error'>错误: 无法获取表状态，可能数据库不存在</p>" "error"
    return 1
  fi
  
  # 使用awk处理表状态
  local formatted_data=$(echo "$table_status" | awk 'NR>1 {
    # 计算大小（MB）
    data_size=sprintf("%.2f", $7/1024/1024);
    index_size=sprintf("%.2f", $9/1024/1024);
    total_size=sprintf("%.2f", ($7+$9)/1024/1024);
    
    # 根据行数确定样式
    row_class="";
    if ($5 > 1000000) row_class="warning";
    if ($5 > 5000000) row_class="error";
    
    # 根据大小确定样式
    size_class="";
    if (total_size > 100) size_class="warning";
    if (total_size > 500) size_class="error";
    
    # 输出HTML行
    printf("<tr><td>%s</td><td>%s</td><td class=\"%s\">%d</td><td class=\"%s\">%s MB</td><td>%s MB</td><td>%s MB</td></tr>\n", 
          $1, $2, row_class, $5, size_class, data_size, index_size, total_size);
  }')
  
  add_html_table "表状态" "表名|引擎|行数|数据大小|索引大小|总大小" "$formatted_data"
}

# 检查磁盘空间并添加到报告
check_disk_space() {
  log_info "检查磁盘空间使用情况..."
  
  local node=$(get_active_node)
  if [ -z "$node" ]; then
    add_html_section "磁盘空间" "<p class='error'>错误: 无法获取磁盘信息，所有节点均未运行</p>" "error"
    return 1
  fi
  
  log_info "使用节点 $node 检查磁盘空间..."
  
  local disk_data=""
  local disk_info=$(docker exec $node df -h /var/lib/mysql)
  
  # 处理磁盘信息
  local filesystem=$(echo "$disk_info" | awk 'NR==2 {print $1}')
  local size=$(echo "$disk_info" | awk 'NR==2 {print $2}')
  local used=$(echo "$disk_info" | awk 'NR==2 {print $3}')
  local avail=$(echo "$disk_info" | awk 'NR==2 {print $4}')
  local use_percent=$(echo "$disk_info" | awk 'NR==2 {print $5}')
  local mount_point=$(echo "$disk_info" | awk 'NR==2 {print $6}')
  
  # 确定样式
  local disk_class="success"
  if [[ $use_percent == *%* ]]; then
    local percent_val=${use_percent%\%}
    if [ $percent_val -gt 80 ]; then
      disk_class="warning"
    fi
    if [ $percent_val -gt 90 ]; then
      disk_class="error"
    fi
  fi
  
  disk_data="<tr><td>$filesystem</td><td>$size</td><td>$used</td><td>$avail</td><td class='$disk_class'>$use_percent</td><td>$mount_point</td></tr>"
  
  add_html_table "磁盘空间" "文件系统|总大小|已用|可用|使用率|挂载点" "$disk_data"
  
  # 数据目录使用情况
  log_info "检查数据目录大小..."
  
  local data_dir_size=$(docker exec $node du -sh /var/lib/mysql 2>/dev/null | awk '{print $1}')
  local log_dir_size=$(docker exec $node du -sh /var/log/mysql 2>/dev/null | awk '{print $1}')
  
  local dir_data="<tr><td>/var/lib/mysql</td><td>$data_dir_size</td><td>数据文件</td></tr>"
  if [ -n "$log_dir_size" ]; then
    dir_data+="<tr><td>/var/log/mysql</td><td>$log_dir_size</td><td>日志文件</td></tr>"
  fi
  
  add_html_table "目录大小" "路径|大小|说明" "$dir_data"
}

# 检查性能指标并添加到报告
check_performance() {
  log_info "检查性能指标..."
  
  local node=$(get_active_node)
  if [ -z "$node" ]; then
    add_html_section "性能指标" "<p class='error'>错误: 无法获取性能信息，所有节点均未运行</p>" "error"
    return 1
  fi
  
  log_info "使用节点 $node 检查性能指标..."
  
  # 获取全局状态
  local global_status=$(docker exec $node mysql -uroot -p$(cat "$ROOT_PASSWORD_FILE") -e "SHOW GLOBAL STATUS;")
  
  # 提取关键指标
  local questions=$(echo "$global_status" | grep "Questions" | awk '{print $2}')
  local com_select=$(echo "$global_status" | grep "Com_select" | awk '{print $2}')
  local com_insert=$(echo "$global_status" | grep "Com_insert" | awk '{print $2}')
  local com_update=$(echo "$global_status" | grep "Com_update" | awk '{print $2}')
  local com_delete=$(echo "$global_status" | grep "Com_delete" | awk '{print $2}')
  local uptime=$(echo "$global_status" | grep "Uptime" | awk '{print $2}')
  
  # 计算每秒查询数
  local qps=$(echo "scale=2; $questions / $uptime" | bc 2>/dev/null || echo "N/A")
  
  # 创建性能数据
  local perf_data="<tr><td>总查询数</td><td>$questions</td></tr>"
  perf_data+="<tr><td>每秒查询数 (QPS)</td><td>$qps</td></tr>"
  perf_data+="<tr><td>SELECT 数</td><td>$com_select</td></tr>"
  perf_data+="<tr><td>INSERT 数</td><td>$com_insert</td></tr>"
  perf_data+="<tr><td>UPDATE 数</td><td>$com_update</td></tr>"
  perf_data+="<tr><td>DELETE 数</td><td>$com_delete</td></tr>"
  perf_data+="<tr><td>运行时间 (秒)</td><td>$uptime</td></tr>"
  
  add_html_table "性能指标" "指标|值" "$perf_data"
  
  # InnoDB 状态
  log_info "检查 InnoDB 状态..."
  
  local innodb_status=$(docker exec $node mysql -uroot -p$(cat "$ROOT_PASSWORD_FILE") -e "SHOW ENGINE INNODB STATUS\G")
  
  # 提取buffer pool信息
  local buffer_pool_info=$(echo "$innodb_status" | grep -A 20 "BUFFER POOL AND MEMORY" | grep -E "Buffer pool size|Free buffers|Database pages|Modified db pages")
  
  # 提取事务信息
  local trx_info=$(echo "$innodb_status" | grep -A 5 "TRANSACTIONS" | grep -E "transactions|lock wait|active|undo")
  
  # 创建InnoDB状态文本
  local innodb_html="<pre>--- BUFFER POOL 信息 ---\n$buffer_pool_info\n\n--- 事务信息 ---\n$trx_info</pre>"
  
  add_html_section "InnoDB 状态" "$innodb_html"
}

# 添加总体状态评估
add_overall_assessment() {
  log_info "生成总体评估..."
  
  local service_status=$(docker ps --filter "name=$PRODUCT-mysql" --format "{{.Names}}" | wc -l)
  local node=$(get_active_node)
  
  local assessment=""
  local status_class=""
  
  if [ -z "$node" ]; then
    assessment="<p>系统状态: <span class='error'>严重</span></p><p>所有MySQL节点均未运行，请立即检查并启动服务。</p>"
    status_class="error"
  elif [ "$service_status" -lt 2 ]; then
    assessment="<p>系统状态: <span class='warning'>警告</span></p><p>部分MySQL节点未运行，高可用性受影响，请尽快恢复所有节点。</p>"
    status_class="warning"
  else
    # 检查复制状态
    local repl_issues=0
    
    # 检查主节点1
    if is_node_running "$MASTER1"; then
      local slave_io=$(docker exec $MASTER1 mysql -uroot -p$(cat "$ROOT_PASSWORD_FILE") -e "SHOW SLAVE STATUS\G" | grep "Slave_IO_Running:" | awk '{print $2}')
      local slave_sql=$(docker exec $MASTER1 mysql -uroot -p$(cat "$ROOT_PASSWORD_FILE") -e "SHOW SLAVE STATUS\G" | grep "Slave_SQL_Running:" | awk '{print $2}')
      
      if [ "$slave_io" != "Yes" ] || [ "$slave_sql" != "Yes" ]; then
        ((repl_issues++))
      fi
    fi
    
    # 检查主节点2
    if is_node_running "$MASTER2"; then
      local slave_io=$(docker exec $MASTER2 mysql -uroot -p$(cat "$ROOT_PASSWORD_FILE") -e "SHOW SLAVE STATUS\G" | grep "Slave_IO_Running:" | awk '{print $2}')
      local slave_sql=$(docker exec $MASTER2 mysql -uroot -p$(cat "$ROOT_PASSWORD_FILE") -e "SHOW SLAVE STATUS\G" | grep "Slave_SQL_Running:" | awk '{print $2}')
      
      if [ "$slave_io" != "Yes" ] || [ "$slave_sql" != "Yes" ]; then
        ((repl_issues++))
      fi
    fi
    
    if [ "$repl_issues" -gt 0 ]; then
      assessment="<p>系统状态: <span class='warning'>警告</span></p><p>MySQL复制出现问题，请检查复制状态并修复错误。</p>"
      status_class="warning"
    else
      # 检查磁盘空间
      local disk_warning=0
      local disk_info=$(docker exec $node df -h /var/lib/mysql | awk 'NR==2 {print $5}')
      
      if [[ $disk_info == *%* ]]; then
        local percent_val=${disk_info%\%}
        if [ $percent_val -gt 90 ]; then
          disk_warning=1
        fi
      fi
      
      if [ "$disk_warning" -eq 1 ]; then
        assessment="<p>系统状态: <span class='warning'>警告</span></p><p>磁盘空间使用率过高，请及时清理数据或扩展存储空间。</p>"
        status_class="warning"
      else
        assessment="<p>系统状态: <span class='success'>正常</span></p><p>MySQL服务运行正常，复制状态正常，系统资源使用在正常范围内。</p>"
        status_class="success"
      fi
    fi
  fi
  
  # 添加建议
  assessment+="<h3>建议:</h3><ul>"
  
  if [ -z "$node" ]; then
    assessment+="<li>使用命令 <code>cd $COMPOSE_DIR && docker-compose -f ${PRODUCT}-mysql.yml up -d</code> 启动服务</li>"
    assessment+="<li>检查日志文件查找可能的错误原因</li>"
  elif [ "$service_status" -lt 2 ]; then
    assessment+="<li>使用命令 <code>cd $COMPOSE_DIR && docker-compose -f ${PRODUCT}-mysql.yml up -d</code> 启动所有服务</li>"
    assessment+="<li>检查未运行节点的日志文件查找可能的错误原因</li>"
  fi
  
  if [ "$repl_issues" -gt 0 ]; then
    assessment+="<li>使用维护工具的'修复复制错误'功能尝试修复复制问题</li>"
    assessment+="<li>如无法自动修复，请联系数据库管理员进行处理</li>"
  fi
  
  if [ "$disk_warning" -eq 1 ]; then
    assessment+="<li>清理不必要的备份文件，特别是较旧的备份</li>"
    assessment+="<li>考虑扩展存储空间或迁移到更大的存储卷</li>"
  fi
  
  assessment+="<li>定期执行备份，确保数据安全</li>"
  assessment+="<li>定期检查日志文件，及时发现潜在问题</li>"
  assessment+="</ul>"
  
  add_html_section "总体评估" "$assessment" "$status_class"
}

# 主函数
main() {
  log_info "开始生成 $PRODUCT MySQL 健康报告..."
  log_info "产品: $PRODUCT (端口: $MYSQL_PORT)"
  
  # 初始化HTML报告
  init_html_report
  
  # 收集各项指标
  check_service_status
  check_replication_status
  check_connections
  check_tables
  check_disk_space
  check_performance
  add_overall_assessment
  
  # 完成HTML报告
  finalize_html_report
  
  # 显示报告位置
  echo ""
  log_success "报告生成完成: $REPORT_FILE"
  echo ""
  echo "您可以使用浏览器打开此HTML文件查看详细报告。"
  echo "在Linux系统中，您可以使用如下命令:"
  echo "  xdg-open $REPORT_FILE  # 对于大多数Linux桌面环境"
  echo "  firefox $REPORT_FILE   # 如果安装了Firefox浏览器"
  echo "  chrome $REPORT_FILE    # 如果安装了Chrome浏览器"
}

# 执行主函数
main "$@" 