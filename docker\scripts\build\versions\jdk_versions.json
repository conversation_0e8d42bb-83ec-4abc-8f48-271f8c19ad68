{"versions": {"8": {"jdk_version": "1.8.0_172", "build_number": "01", "variants": ["jammy"], "supported_architectures": ["amd64", "arm64"]}}, "variants": {"jammy": {"ubuntu_version": "22.04", "os_suffix": "crypto-sc34", "description": "基于Ubuntu 22.04 (Jam<PERSON> Jellyfish)"}}, "build_defaults": {"namespace": "btit", "repository": "infra/jdk"}, "_comments": {"versions.jdk_version": "JDK完整版本号，如1.8.0_172", "versions.build_number": "JDK构建号，用于标签，如8u172-b01", "versions.variants": "支持的变体列表", "versions.supported_architectures": "支持的架构列表", "variants.ubuntu_version": "Ubuntu版本号", "variants.os_suffix": "基础镜像后缀，用于标签命名，如 crypto-sc62", "build_defaults.namespace": "默认命名空间", "build_defaults.repository": "默认仓库路径"}}