#!/bin/bash
#
# Keepalived 日志配置脚本
# 用于独立配置 Keepalived 的日志记录和轮转
#

set -e

# 颜色定义
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[0;33m'
readonly BLUE='\033[0;34m'
readonly NC='\033[0m' # No Color

# 默认配置
LOG_DIR="/var/log/keepalived"
RSYSLOG_CONF="/etc/rsyslog.d/10-keepalived.conf"
LOGROTATE_CONF="/etc/logrotate.d/keepalived"
SYSCONFIG_DIR="/etc/sysconfig"
LOG_FACILITY="0"  # local0
RETENTION_DAYS="14"

# 脚本路径
readonly SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"
readonly BASE_DIR="$(cd "${SCRIPT_DIR}/.." && pwd)"
readonly TEMPLATE_DIR="${BASE_DIR}/config/templates"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO] $1${NC}"
}

log_warn() {
    echo -e "${YELLOW}[WARN] $1${NC}" >&2
}

log_error() {
    echo -e "${RED}[ERROR] $1${NC}" >&2
}

print_separator() {
    echo -e "${BLUE}=========================================${NC}"
}

print_title() {
    print_separator
    log_info "$1"
    print_separator
}

# 显示使用帮助
show_usage() {
    cat << EOF
${GREEN}Keepalived 日志配置脚本${NC}
用法: $0 [选项]

选项:
  -d, --log-dir DIR         指定日志目录路径 (默认: ${LOG_DIR})
  -f, --facility NUM        指定syslog设施编号 0-7 (local0-local7, 默认: ${LOG_FACILITY})
  -r, --retention DAYS      指定日志保留天数 (默认: ${RETENTION_DAYS})
  -h, --help                显示此帮助信息

示例:
  $0 --log-dir /var/log/custom --facility 1 --retention 30
EOF
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -d|--log-dir)
                LOG_DIR="$2"
                shift 2
                ;;
            -f|--facility)
                LOG_FACILITY="$2"
                shift 2
                ;;
            -r|--retention)
                RETENTION_DAYS="$2"
                shift 2
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                log_error "无效的参数: '$1'"
                show_usage
                exit 1
                ;;
        esac
    done
}

# 创建日志目录
create_log_directory() {
    log_info "创建日志目录: ${LOG_DIR}"
    
    mkdir -p "${LOG_DIR}"
    touch "${LOG_DIR}/keepalived.log"
    chmod 644 "${LOG_DIR}/keepalived.log"
    
    # 根据不同系统设置日志文件所有者
    if getent passwd syslog >/dev/null; then
        chown syslog:adm "${LOG_DIR}/keepalived.log"
    elif getent passwd root >/dev/null; then
        chown root:root "${LOG_DIR}/keepalived.log"
    fi
    
    log_info "日志目录创建完成"
}

# 检测rsyslog版本和功能
detect_rsyslog_version() {
    log_info "检测rsyslog版本..."
    
    # 检查rsyslog版本
    local RSYSLOG_VERSION=""
    if command -v rsyslogd >/dev/null 2>&1; then
        RSYSLOG_VERSION=$(rsyslogd -v | head -n 1 | grep -oP 'rsyslogd \K[0-9]+\.[0-9]+\.[0-9]+' || echo "")
    fi
    
    log_info "检测到rsyslog版本: ${RSYSLOG_VERSION:-未知}"
    
    # 根据不同版本返回配置类型
    if [[ -n "$RSYSLOG_VERSION" ]]; then
        local MAJOR_VERSION=$(echo "$RSYSLOG_VERSION" | cut -d. -f1)
        if [[ "$MAJOR_VERSION" -ge 8 ]]; then
            echo "modern"
        elif [[ "$MAJOR_VERSION" -ge 5 ]]; then
            echo "intermediate"
        else
            echo "legacy"
        fi
    else
        # 如果检测不到版本，尝试确定配置类型
        if grep -q "ModLoad" /etc/rsyslog.conf 2>/dev/null; then
            echo "legacy"
        else
            # 默认使用较旧的配置类型，确保兼容性
            echo "legacy"
        fi
    fi
}

# 配置rsyslog
configure_rsyslog() {
    log_info "配置rsyslog..."
    
    # 检测rsyslog版本
    local rsyslog_type=$(detect_rsyslog_version)
    log_info "使用 ${rsyslog_type} 类型的rsyslog配置"
    
    # 备份原始配置
    if [ -f "${RSYSLOG_CONF}" ]; then
        cp "${RSYSLOG_CONF}" "${RSYSLOG_CONF}.bak.$(date +%Y%m%d%H%M%S)"
        log_info "已备份原有rsyslog配置"
    fi
    
    # 根据rsyslog版本类型使用不同的配置语法
    case "$rsyslog_type" in
        "modern")
            # 现代版本(8+)使用RainerScript语法
            cat > "${RSYSLOG_CONF}" << EOF
# Keepalived 日志配置 - 适用于现代版本rsyslog (8+)
# 由 configure-logging.sh 脚本自动生成于 $(date '+%Y-%m-%d %H:%M:%S')

# 捕获Keepalived相关进程的日志
if \$programname startswith "Keepalived" or \$programname == "check_redis" then {
    action(type="omfile" file="${LOG_DIR}/keepalived.log")
    stop
}

# 备用配置 - 基于自定义设施(仅当使用--log-facility参数时生效)
local${LOG_FACILITY}.*    action(type="omfile" file="${LOG_DIR}/keepalived.log")
EOF
            ;;
        "intermediate")
            # 中间版本(5-7)使用混合语法
            cat > "${RSYSLOG_CONF}" << EOF
# Keepalived 日志配置 - 适用于中间版本rsyslog (5-7)
# 由 configure-logging.sh 脚本自动生成于 $(date '+%Y-%m-%d %H:%M:%S')

# 捕获Keepalived相关进程的日志
:programname, startswith, "Keepalived" ${LOG_DIR}/keepalived.log
& stop
:programname, isequal, "check_redis" ${LOG_DIR}/keepalived.log
& stop

# 备用配置 - 基于自定义设施(仅当使用--log-facility参数时生效)
local${LOG_FACILITY}.*    ${LOG_DIR}/keepalived.log
EOF
            ;;
        "legacy"|*)
            # 旧版本使用传统语法
            cat > "${RSYSLOG_CONF}" << EOF
# Keepalived 日志配置 - 适用于传统版本rsyslog
# 由 configure-logging.sh 脚本自动生成于 $(date '+%Y-%m-%d %H:%M:%S')

# 捕获Keepalived相关进程的日志
:programname, startswith, "Keepalived" ${LOG_DIR}/keepalived.log
:programname, startswith, "Keepalived" ~
:programname, isequal, "check_redis" ${LOG_DIR}/keepalived.log
:programname, isequal, "check_redis" ~

# 备用配置 - 基于自定义设施(仅当使用--log-facility参数时生效)
local${LOG_FACILITY}.*    ${LOG_DIR}/keepalived.log
EOF
            ;;
    esac
    
    log_info "rsyslog 配置完成"
}

# 配置Keepalived使用指定的日志设施
configure_keepalived_options() {
    log_info "配置Keepalived日志设施..."
    
    local sysconfig_file="${SYSCONFIG_DIR}/keepalived"
    if [ -f "$sysconfig_file" ]; then
        # 备份原始配置
        cp "$sysconfig_file" "${sysconfig_file}.bak.$(date +%Y%m%d%H%M%S)"
        
        if grep -q "KEEPALIVED_OPTIONS" "$sysconfig_file"; then
            # 检查是否已经配置了log-facility
            if grep -q -- "--log-facility" "$sysconfig_file"; then
                log_info "更新已存在的日志设施配置"
                sed -i "s/--log-facility=[0-7]/--log-facility=${LOG_FACILITY}/g" "$sysconfig_file"
            else
                # 如果已存在选项，添加日志设施选项
                log_info "添加日志设施配置"
                sed -i "s|KEEPALIVED_OPTIONS=\"\(.*\)\"|KEEPALIVED_OPTIONS=\"\1 --log-facility=${LOG_FACILITY}\"|g" "$sysconfig_file"
            fi
        else
            # 如果不存在，创建新的配置行
            log_info "创建新的Keepalived选项配置"
            echo "KEEPALIVED_OPTIONS=\"-D --log-facility=${LOG_FACILITY}\"" >> "$sysconfig_file"
        fi
        log_info "已配置Keepalived使用local${LOG_FACILITY}日志设施"
    else
        log_warn "未找到 $sysconfig_file 文件，无法配置日志设施"
    fi
}

# 配置日志轮转
configure_log_rotation() {
    log_info "配置日志轮转..."
    
    cat > "${LOGROTATE_CONF}" << EOF
${LOG_DIR}/keepalived.log {
    daily
    rotate ${RETENTION_DAYS}
    missingok
    compress
    delaycompress
    notifempty
    create 644 root root
    sharedscripts
    postrotate
        /bin/kill -HUP \$(cat /var/run/rsyslogd.pid 2>/dev/null) 2>/dev/null || true
    endscript
}
EOF
    
    log_info "日志轮转配置完成"
}

# 重启日志服务
restart_logging_service() {
    log_info "重启日志服务..."
    
    # 确保生成的配置文件权限正确
    chmod 644 "${RSYSLOG_CONF}"
    
    # 验证rsyslog配置
    if command -v rsyslogd >/dev/null 2>&1; then
        log_info "验证rsyslog配置..."
        rsyslogd -N1 || log_warn "rsyslog配置验证失败，但将继续尝试重启服务"
    fi
    
    # 重启rsyslog服务
    local restart_success=false
    if systemctl is-active --quiet rsyslog; then
        log_info "尝试重启rsyslog服务..."
        systemctl restart rsyslog && restart_success=true
    elif systemctl is-active --quiet syslog; then
        log_info "尝试重启syslog服务..."
        systemctl restart syslog && restart_success=true
    else
        # 尝试使用传统方式重启
        log_info "尝试使用传统方式重启rsyslog服务..."
        service rsyslog restart 2>/dev/null && restart_success=true || service syslog restart 2>/dev/null && restart_success=true
    fi
    
    if $restart_success; then
        log_info "日志服务重启成功"
    else
        log_warn "无法重启日志服务，可能需要手动重启"
    fi
    
    # 发送测试消息到日志
    log_info "发送测试消息到日志系统..."
    logger -p local${LOG_FACILITY}.info "Keepalived日志配置测试消息 - $(date)"
    logger -t Keepalived "日志配置测试消息 - $(date)"
    
    # 等待日志生效
    sleep 2
    
    # 检查日志文件是否包含测试消息
    if grep -q "日志配置测试消息" "${LOG_DIR}/keepalived.log" 2>/dev/null; then
        log_info "日志配置测试成功，消息已写入 ${LOG_DIR}/keepalived.log"
    else
        log_warn "日志配置测试失败，未在 ${LOG_DIR}/keepalived.log 中找到测试消息"
    fi
}

# 主函数
main() {
    print_title "Keepalived 日志配置"
    
    parse_args "$@"
    create_log_directory
    configure_rsyslog
    configure_keepalived_options
    configure_log_rotation
    restart_logging_service
    
    print_title "Keepalived 日志配置完成"
    log_info "日志文件路径: ${LOG_DIR}/keepalived.log"
    log_info "日志设施: local${LOG_FACILITY}"
    log_info "日志保留天数: ${RETENTION_DAYS}"
}

# 执行主函数
main "$@" 