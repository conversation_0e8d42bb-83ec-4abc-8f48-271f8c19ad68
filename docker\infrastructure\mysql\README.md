# MySQL 容器镜像

## 简介
此目录包含用于构建医疗产品数据库的MySQL容器镜像配置。基于官方MySQL 8.0镜像，针对医疗产品场景进行了优化配置。

## 目录结构
```
mysql/
├── Dockerfile          # 镜像构建文件
```

## 特性
- 基于MySQL 8.0官方镜像
- UTF-8编码（utf8mb4）
- 针对性能优化的InnoDB配置
- 完整的日志记录
- 内置健康检查
- 安全加固配置

## 配置说明
### 主要配置参数
- 最大连接数：1000
- InnoDB缓冲池大小：1GB
- 慢查询日志阈值：2秒
- 字符集：utf8mb4
- 排序规则：utf8mb4_unicode_ci

### 数据持久化
使用Docker命名卷进行数据持久化：
```yaml
volumes:
  - mysql_data:/var/lib/mysql
  - mysql_logs:/var/log/mysql
```

### 环境变量
- `MYSQL_ROOT_PASSWORD`: root用户密码（必需）
- `TZ`: 时区，默认Asia/Shanghai

## 使用方法
1. 构建镜像
```bash
docker build -t medical/mysql:8.0 .
```

2. 运行容器
```bash
docker run -d \
  --name mysql \
  -e MYSQL_ROOT_PASSWORD=your_password \
  -v mysql_data:/var/lib/mysql \
  -v mysql_logs:/var/log/mysql \
  -p 3306:3306 \
  medical/mysql:8.0
```

## 安全注意事项
1. 生产环境必须修改默认密码
2. 建议使用非root用户运行应用
3. 定期备份数据
4. 及时更新安全补丁

## 维护说明
1. 日志位置：/var/log/mysql/
2. 配置文件：/etc/mysql/conf.d/
3. 数据目录：/var/lib/mysql/

## 健康检查
镜像内置健康检查机制，每30秒执行一次，通过mysqladmin ping命令检查服务状态。

## 故障排除
1. 查看错误日志：
```bash
docker exec mysql tail -f /var/log/mysql/error.log
```

2. 查看慢查询日志：
```bash
docker exec mysql tail -f /var/log/mysql/mysql-slow.log
```

## 版本历史
- 1.0.0: 初始版本，基于MySQL 8.0
