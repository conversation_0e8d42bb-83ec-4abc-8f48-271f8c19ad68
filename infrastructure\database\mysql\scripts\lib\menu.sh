#!/bin/bash
# MySQL 菜单功能
# 包含菜单显示和用户交互功能

# 引入依赖
source "$(dirname "${BASH_SOURCE[0]}")/utils.sh"

# 显示主菜单
show_menu() {
  local product="$1"
  
  clear
  echo -e "${GREEN}==== MySQL 维护工具 - $product ====${NC}"
  echo -e "1) 检查服务状态"
  echo -e "2) 检查主主复制状态"
  echo -e "3) 执行手动备份 (自动备份每日凌晨3点执行)"
  echo -e "4) 恢复数据库"
  echo -e "5) 启动服务"
  echo -e "6) 停止服务"
  echo -e "7) 重启服务"
  echo -e "8) 修复复制错误"
  echo -e "9) 检查数据库性能"
  echo -e "10) 显示日志"
  echo -e "11) 数据库连接信息"
  echo -e "0) 退出"
  echo -e "${GREEN}=====================================${NC}"
  echo -n "请选择操作 [0-11]: "
}

# 显示帮助信息
show_help() {
  echo "用法: $1 [产品名称] [选项]"
  echo ""
  echo "参数:"
  echo "  [产品名称]  - 要维护的产品名称 (默认: cloudkey)"
  echo "              可选值: cloudkey, eseal, quicksign"
  echo ""
  echo "选项:"
  echo "  -h, --help  - 显示此帮助信息"
  echo ""
  echo "示例:"
  echo "  $1 cloudkey  # 维护云密钥系统MySQL服务"
  echo "  $1 eseal     # 维护电子签章系统MySQL服务"
  echo "  $1 --help    # 显示帮助信息"
  echo ""
  exit 0
}

# 检查命令行参数，确定产品名称
parse_args() {
  local script_name="$1"
  shift
  
  # 帮助选项已在主脚本中检查，这里不再重复检查
  
  # 配置
  local product="${1:-$DEFAULT_PRODUCT}"
  
  # 确保产品名称不是以"-"开头的选项
  if [[ "$product" == -* ]]; then
    log_error "无效的产品名称: $product"
    show_help "$script_name"
    # 帮助函数内部已有exit，不会继续执行
  fi
  
  echo "$product"
}

# 等待用户按Enter继续
wait_for_enter() {
  echo -n "按Enter键继续..."
  read
} 