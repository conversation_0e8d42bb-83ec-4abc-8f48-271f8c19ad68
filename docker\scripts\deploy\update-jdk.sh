#!/bin/bash

# 设置错误时退出
set -e

# 加载公共函数
source ../lib/common.sh
source ../lib/docker.sh
source ../lib/deploy-utils.sh

# 基础目录
BASE_DIR="../../infrastructure/jdk"
VERSIONS_FILE="$BASE_DIR/versions.json"

# 检查必要文件
check_required_files() {
    if [ ! -f "$VERSIONS_FILE" ]; then
        error "版本配置文件不存在: $VERSIONS_FILE"
        exit 1
    }
}

# 更新加密组件
update_crypto_files() {
    local version=$1
    local arch=$2
    local target_dir="$BASE_DIR/shared/crypto-files/$version/$arch"

    info "更新加密组件 version=$version arch=$arch"

    # 创建目录
    mkdir -p "$target_dir"

    # 从指定位置复制NETCA_CRYPTO文件
    local resource_dir="$BASE_DIR/resources/NETCA_CRYPTO/$version/$arch"
    if [ -d "$resource_dir" ]; then
        cp -r "$resource_dir"/* "$target_dir/"
        success "从本地资源复制加密组件完成"
    else
        warn "本地资源不存在: $resource_dir，请手动更新加密组件"
    fi
}

# 更新证书
update_certs() {
    local certs_dir="$BASE_DIR/shared/certs"
    local resource_dir="$BASE_DIR/resources/certs"
    
    info "更新证书文件"

    # 创建目录
    mkdir -p "$certs_dir"

    # 从指定位置复制证书文件
    if [ -d "$resource_dir" ]; then
        cp -r "$resource_dir"/* "$certs_dir/"
        success "从本地资源复制证书完成"
    else
        warn "本地资源不存在: $resource_dir，请手动更新证书"
    fi
}

# 更新版本配置
update_versions() {
    info "更新版本配置"

    # 备份当前配置
    cp "$VERSIONS_FILE" "${VERSIONS_FILE}.bak"

    # 检查Temurin JDK最新版本
    local jdk8_version=$(curl -s "https://api.adoptium.net/v3/info/release_versions?architecture=x64&heap_size=normal&image_type=jdk&jvm_impl=hotspot&os=linux&page=0&page_size=1&project=jdk&release_type=ga&semver=false&sort_method=DEFAULT&sort_order=DESC&vendor=eclipse&version=%5B8%2C9%29" | jq -r '.versions[0].semver')
    local jdk17_version=$(curl -s "https://api.adoptium.net/v3/info/release_versions?architecture=x64&heap_size=normal&image_type=jdk&jvm_impl=hotspot&os=linux&page=0&page_size=1&project=jdk&release_type=ga&semver=false&sort_method=DEFAULT&sort_order=DESC&vendor=eclipse&version=%5B17%2C18%29" | jq -r '.versions[0].semver')

    # 更新版本信息
    if [ -n "$jdk8_version" ] && [ -n "$jdk17_version" ]; then
        jq --arg jdk8 "$jdk8_version" --arg jdk17 "$jdk17_version" \
           '.versions["8"].base_image = "**************:1443/btit/infra/eclipse-temurin:" + $jdk8 + "-jdk-jammy" | 
            .versions["17"].base_image = "**************:1443/btit/infra/eclipse-temurin:" + $jdk17 + "-jdk-jammy"' \
           "$VERSIONS_FILE" > "${VERSIONS_FILE}.tmp"
        mv "${VERSIONS_FILE}.tmp" "$VERSIONS_FILE"
        success "版本配置更新完成"
    else
        warn "无法获取最新版本信息，请手动更新"
    fi
}

# 主函数
main() {
    # 检查必要的命令
    check_command "jq" "curl"

    # 检查必要文件
    check_required_files

    # 切换到脚本所在目录
    cd "$(dirname "$0")"

    # 更新版本配置
    update_versions

    # 获取加密组件版本
    local crypto_version=$(jq -r '.crypto_files_version' "$VERSIONS_FILE")

    # 获取所有支持的架构
    local architectures=($(jq -r '.versions[] | .supported_architectures[]' "$VERSIONS_FILE" | sort -u))

    # 更新加密组件
    for arch in "${architectures[@]}"; do
        update_crypto_files "$crypto_version" "$arch"
    done

    # 更新证书
    update_certs

    success "JDK资源更新完成"
}

# 执行主函数
main "$@" 