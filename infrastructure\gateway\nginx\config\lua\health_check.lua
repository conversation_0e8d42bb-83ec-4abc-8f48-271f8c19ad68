-- 健康检查辅助模块
local _M = {}
local http = require "resty.http"
local cjson = require "cjson.safe"

-- 获取上游服务器状态
function _M.check_server(ip, port, path, timeout)
    local httpc = http.new()
    httpc:set_timeout(timeout or 2000)
    
    local res, err = httpc:request_uri("http://" .. ip .. ":" .. port .. path, {
        method = "GET",
        headers = {
            ["Host"] = "cloudkeyserver"
        }
    })
    
    if not res then
        return false, err or "连接失败"
    end
    
    return res.status == 200, {
        status = res.status,
        body = res.body,
        headers = res.headers
    }
end

-- 格式化响应结果
function _M.format_response(success, data, target)
    local response = {
        success = success
    }
    
    if target then
        response.target = target
    end
    
    if type(data) == "string" then
        response.message = data
    else
        for k, v in pairs(data or {}) do
            response[k] = v
        end
    end
    
    return cjson.encode(response)
end

return _M 