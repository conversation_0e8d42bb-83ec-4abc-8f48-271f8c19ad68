#!/bin/bash
#
# Redis 备份脚本
# 用于定期备份Redis数据
#

# 设置错误时退出
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # 无颜色

# 脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
BASE_DIR="$(dirname "$SCRIPT_DIR")"

# 默认产品名称和备份类型
PRODUCT_NAME=${1:-"cloudkey"}
BACKUP_TYPE=${2:-"full"}

# 检查参数
if [[ "$PRODUCT_NAME" == "--help" || "$PRODUCT_NAME" == "-h" ]]; then
  echo "用法: $0 [产品名称] [备份类型]"
  echo "产品名称: cloudkey, eseal, quicksign (默认: cloudkey)"
  echo "备份类型:"
  echo "  full - 全量备份 (默认)"
  echo "  snapshot - 快照备份，只备份RDB文件"
  exit 0
fi

# 检查目录和配置文件
BACKUP_DIR="$BASE_DIR/backups/$PRODUCT_NAME"
mkdir -p "$BACKUP_DIR"

# 生成备份文件名
TIMESTAMP=$(date "+%Y%m%d_%H%M%S")
BACKUP_FILE="$BACKUP_DIR/redis_${PRODUCT_NAME}_${BACKUP_TYPE}_${TIMESTAMP}.tar.gz"
LOG_FILE="$BASE_DIR/logs/$PRODUCT_NAME/backup_${TIMESTAMP}.log"

# 创建日志目录
mkdir -p "$BASE_DIR/logs/$PRODUCT_NAME"

# 日志函数
log() {
  echo -e "$1" | tee -a "$LOG_FILE"
}

# 函数: 检查Redis服务状态
check_redis_status() {
  log "${BLUE}[1/4] 检查Redis服务状态...${NC}"
  
  # 检查主节点状态
  if ! docker ps | grep -q "$PRODUCT_NAME-redis-master"; then
    log "${RED}错误: Redis主节点未运行，无法执行备份${NC}"
    exit 1
  fi
  
  log "${GREEN}Redis服务正在运行，可以执行备份${NC}"
}

# 函数: 执行备份
perform_backup() {
  log "${BLUE}[2/4] 执行备份操作...${NC}"
  
  # 创建临时目录
  TEMP_DIR=$(mktemp -d)
  log "${YELLOW}创建临时目录: $TEMP_DIR${NC}"
  
  if [[ "$BACKUP_TYPE" == "full" ]]; then
    # 全量备份: 触发SAVE命令并复制所有数据文件
    log "${YELLOW}执行全量备份...${NC}"
    
    # 触发SAVE命令
    if [ -f "$BASE_DIR/secrets/redis_password.txt" ]; then
      REDIS_PASSWORD=$(cat "$BASE_DIR/secrets/redis_password.txt")
      docker exec -i "$PRODUCT_NAME-redis-master" redis-cli -a "$REDIS_PASSWORD" SAVE
    else
      docker exec -i "$PRODUCT_NAME-redis-master" redis-cli SAVE
    fi
    
    # 复制数据文件
    docker cp "$PRODUCT_NAME-redis-master:/data/" "$TEMP_DIR/master_data"
    
    # 包含配置文件
    mkdir -p "$TEMP_DIR/config"
    cp -r "$BASE_DIR/config/" "$TEMP_DIR/config/"
    
    # 备份元数据
    mkdir -p "$TEMP_DIR/metadata"
    if [ -f "$BASE_DIR/secrets/redis_password.txt" ]; then
      REDIS_PASSWORD=$(cat "$BASE_DIR/secrets/redis_password.txt")
      docker exec -i "$PRODUCT_NAME-redis-master" redis-cli -a "$REDIS_PASSWORD" INFO ALL > "$TEMP_DIR/metadata/master_info.txt"
    else
      docker exec -i "$PRODUCT_NAME-redis-master" redis-cli INFO ALL > "$TEMP_DIR/metadata/master_info.txt"
    fi
    
  else
    # 快照备份: 仅复制RDB文件
    log "${YELLOW}执行快照备份...${NC}"
    
    # 触发SAVE命令
    if [ -f "$BASE_DIR/secrets/redis_password.txt" ]; then
      REDIS_PASSWORD=$(cat "$BASE_DIR/secrets/redis_password.txt")
      docker exec -i "$PRODUCT_NAME-redis-master" redis-cli -a "$REDIS_PASSWORD" SAVE
    else
      docker exec -i "$PRODUCT_NAME-redis-master" redis-cli SAVE
    fi
    
    # 创建快照目录
    mkdir -p "$TEMP_DIR/snapshot"
    
    # 复制RDB文件
    docker cp "$PRODUCT_NAME-redis-master:/data/dump.rdb" "$TEMP_DIR/snapshot/"
  fi
  
  # 创建备份描述文件
  cat > "$TEMP_DIR/backup_info.txt" << EOL
产品名称: $PRODUCT_NAME
备份类型: $BACKUP_TYPE
备份时间: $(date "+%Y-%m-%d %H:%M:%S")
备份源: 主节点 ($PRODUCT_NAME-redis-master)
Redis版本: $(docker exec -i "$PRODUCT_NAME-redis-master" redis-server --version | head -n 1)
EOL
  
  # 创建压缩文件
  log "${YELLOW}创建备份归档...${NC}"
  tar -czf "$BACKUP_FILE" -C "$TEMP_DIR" .
  
  # 清理临时目录
  log "${YELLOW}清理临时文件...${NC}"
  rm -rf "$TEMP_DIR"
  
  log "${GREEN}备份文件已创建: $BACKUP_FILE${NC}"
}

# 函数: 验证备份
verify_backup() {
  log "${BLUE}[3/4] 验证备份文件...${NC}"
  
  # 检查备份文件是否存在
  if [ ! -f "$BACKUP_FILE" ]; then
    log "${RED}错误: 备份文件未创建${NC}"
    exit 1
  fi
  
  # 验证压缩文件完整性
  if ! tar -tzf "$BACKUP_FILE" > /dev/null 2>&1; then
    log "${RED}错误: 备份文件已损坏${NC}"
    exit 1
  fi
  
  # 获取文件大小
  BACKUP_SIZE=$(du -h "$BACKUP_FILE" | cut -f1)
  
  log "${GREEN}备份验证通过，文件大小: $BACKUP_SIZE${NC}"
}

# 函数: 管理备份保留
manage_retention() {
  log "${BLUE}[4/4] 管理备份保留...${NC}"
  
  # 默认保留最近30天的备份
  RETENTION_DAYS=30
  
  # 计算截止日期
  CUTOFF_DATE=$(date -d "$RETENTION_DAYS days ago" +%Y%m%d)
  
  # 找出过期的备份文件
  EXPIRED_FILES=$(find "$BACKUP_DIR" -name "redis_${PRODUCT_NAME}_*_*.tar.gz" -type f | while read -r file; do
    FILE_DATE=$(echo "$file" | grep -oP '\d{8}_\d{6}' | cut -d_ -f1)
    if [[ "$FILE_DATE" < "$CUTOFF_DATE" ]]; then
      echo "$file"
    fi
  done)
  
  # 删除过期备份
  if [ -n "$EXPIRED_FILES" ]; then
    log "${YELLOW}删除过期备份文件:${NC}"
    for file in $EXPIRED_FILES; do
      log "  - $(basename "$file")"
      rm -f "$file"
    done
  else
    log "${GREEN}没有过期的备份文件需要删除${NC}"
  fi
  
  # 统计当前备份数量和总大小
  BACKUP_COUNT=$(find "$BACKUP_DIR" -name "redis_${PRODUCT_NAME}_*_*.tar.gz" -type f | wc -l)
  TOTAL_SIZE=$(du -sh "$BACKUP_DIR" | cut -f1)
  
  log "${GREEN}当前备份文件数量: $BACKUP_COUNT, 总大小: $TOTAL_SIZE${NC}"
}

# 函数: 显示备份摘要
show_backup_summary() {
  log ""
  log "${BLUE}====================================${NC}"
  log "${GREEN}Redis备份完成!${NC}"
  log "${BLUE}====================================${NC}"
  log "${YELLOW}产品名称: ${GREEN}$PRODUCT_NAME${NC}"
  log "${YELLOW}备份类型: ${GREEN}$BACKUP_TYPE${NC}"
  log "${YELLOW}备份文件: ${GREEN}$BACKUP_FILE${NC}"
  log "${YELLOW}文件大小: ${GREEN}$(du -h "$BACKUP_FILE" | cut -f1)${NC}"
  log "${YELLOW}备份时间: ${GREEN}$(date "+%Y-%m-%d %H:%M:%S")${NC}"
  log "${YELLOW}日志文件: ${GREEN}$LOG_FILE${NC}"
  log "${BLUE}====================================${NC}"
}

# 主函数
main() {
  log "${BLUE}====================================${NC}"
  log "${BLUE}       Redis 备份工具              ${NC}"
  log "${BLUE}====================================${NC}"
  log "${YELLOW}开始时间: ${GREEN}$(date "+%Y-%m-%d %H:%M:%S")${NC}"
  log "${YELLOW}产品名称: ${GREEN}$PRODUCT_NAME${NC}"
  log "${YELLOW}备份类型: ${GREEN}$BACKUP_TYPE${NC}"
  log ""
  
  check_redis_status
  perform_backup
  verify_backup
  manage_retention
  show_backup_summary
}

# 执行主函数
main 