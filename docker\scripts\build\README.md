# Tomcat 镜像构建脚本

## 优化说明

对 `build-tomcat.sh` 脚本进行了全面优化，主要包括以下方面：

1. **配置文件驱动**
   - 从 JSON 配置文件加载 Tomcat 和 JDK 版本信息，不再硬编码在脚本中
   - 支持动态获取构建参数，如命名空间、仓库路径、基础镜像等
   - 可以通过配置文件添加新版本，无需修改脚本代码

2. **JDK 基础镜像配置**
   - 动态从配置文件获取 JDK 基础镜像信息
   - 支持不同 JDK 版本和变体
   - 自动检测 JDK 版本兼容性

3. **命令行参数增强**
   - 增加更多命令行选项，提供更灵活的构建控制
   - 支持指定配置文件路径
   - 支持保存镜像到本地目录

4. **错误处理和日志**
   - 添加更详细的错误消息和状态输出
   - 增强对 Harbor 仓库状态的检测和错误报告

5. **版本兼容性检查**
   - 支持从配置文件中获取 Tomcat 和 JDK 的兼容性矩阵
   - 只构建兼容的版本组合，避免不必要的构建失败

## 配置文件格式

### Tomcat 配置文件 (`tomcat_versions.json`)

```json
{
  "default_version": "9.0.83",
  "build_defaults": {
    "namespace": "btit",
    "repository": "infra/tomcat",
    "registry": "192.168.200.39:1443"
  },
  "versions": {
    "9.0.83": {
      "download_url": "https://archive.apache.org/dist/tomcat/tomcat-9/v9.0.83/bin/apache-tomcat-9.0.83.tar.gz",
      "sha512": "ac34f5c45ed5a5ba0a5a57a19a9d6e58bcc90954ece7130c86adff651659d86e88fcb27e4d3fbbb90a6fccaf01dbf3e7ecb0d7ca66e2da83eaad5dd9da0c18e1",
      "variants": ["default", "crypto"]
    },
    "10.1.17": {
      "download_url": "https://archive.apache.org/dist/tomcat/tomcat-10/v10.1.17/bin/apache-tomcat-10.1.17.tar.gz",
      "sha512": "92ca64e2a9118908281bc1db9804ca0dc85ab81cc2a9c31d5a404d23a6e78c11e2e73b95698bbcef3df4fb055ea0d0bb2aaa951f03d4f2dc6ef83025d08e92f3",
      "variants": ["default", "crypto"]
    }
  },
  "jdk_compatibility": {
    "8.5": ["8"],
    "9.0": ["8", "17"],
    "10.1": ["17"]
  }
}
```

配置文件说明：
- `default_version`: 默认的 Tomcat 版本
- `build_defaults`: 构建相关的默认配置
  - `namespace`: Harbor 仓库命名空间
  - `repository`: 镜像仓库路径
  - `registry`: Harbor 仓库地址
- `versions`: Tomcat 版本配置
  - `download_url`: Tomcat 二进制包下载地址
  - `sha512`: 文件校验和
  - `variants`: 支持的构建变体
- `jdk_compatibility`: JDK 兼容性配置
  - 键为 Tomcat 主版本号（如 "8.5", "9.0", "10.1"）
  - 值为该版本支持的 JDK 版本列表
  - 例如：
    - Tomcat 8.5.x 支持 JDK 8
    - Tomcat 9.0.x 支持 JDK 8 和 JDK 17
    - Tomcat 10.1.x 支持 JDK 17

### JDK 配置文件 (`versions.json`)

```