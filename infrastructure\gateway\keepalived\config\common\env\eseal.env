# 电子签章系统Keepalived环境变量配置

# 产品标识
PRODUCT_NAME="eseal"
PRODUCT_DISPLAY_NAME="电子签章系统"

# 注意：Keepalived配置文件中的IP地址、网络接口和路由ID等参数是直接设置在配置文件中的，
# 不是通过环境变量设置。请直接修改对应产品目录下的keepalived_master.conf和keepalived_backup.conf文件。

# MySQL配置
MYSQL_HOST=127.0.0.1
MYSQL_PORT=13316
MYSQL_USER=eseal
MYSQL_PASSWORD=eseal_pass
MYSQL_DATABASE=eseal_db

# Redis配置
REDIS_HOST=127.0.0.1
REDIS_PORT=16389
REDIS_PASSWORD=eseal_redis_pass
REDIS_DB=0

# Nginx配置
NGINX_HOST=127.0.0.1
NGINX_PORT=80
NGINX_STATUS_PATH=/nginx_status
