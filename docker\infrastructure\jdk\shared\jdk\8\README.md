# JDK 8安装包

此目录用于存放JDK 1.8.0_172安装包，用于构建基于Ubuntu Jammy的JDK 8 Docker镜像。

## 使用要求

请将JDK安装包放置在此目录下，文件名必须为：`jdk1.8.0_172.tar.gz`

## 安装包获取

该版本的JDK安装包可从以下渠道获取：

1. Oracle官方归档: https://www.oracle.com/java/technologies/javase/javase8-archive-downloads.html
2. 公司内部软件仓库

## 镜像构建

放置安装包后，使用以下命令构建镜像：

```bash
# 在项目根目录下执行
./docker/scripts/build/build-jdk.sh 8
```

## 注意事项

- 此JDK版本为Oracle JDK 1.8.0_172
- 请遵循Oracle的许可协议
- 确保下载的是Linux x64版本的tar.gz包 