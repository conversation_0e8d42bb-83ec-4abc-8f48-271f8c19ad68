# 使用官方Nginx镜像作为基础镜像
FROM nginx:1.24

# 添加标签信息
LABEL maintainer="Medical Products Team"
LABEL description="Nginx gateway for medical products"
LABEL version="1.0"

# 设置环境变量
ENV TZ=Asia/Shanghai

# 安装必要的工具
RUN apt-get update && apt-get install -y \
    curl \
    openssl \
    && rm -rf /var/lib/apt/lists/*

# 创建必要的目录
RUN mkdir -p /etc/nginx/ssl \
    && mkdir -p /etc/nginx/sites-enabled \
    && mkdir -p /var/log/nginx \
    && mkdir -p /var/cache/nginx

# 复制配置文件
COPY config/nginx.conf /etc/nginx/nginx.conf
COPY config/conf.d/ /etc/nginx/conf.d/
COPY config/ssl/ /etc/nginx/ssl/

# 设置默认时区
RUN ln -sf /usr/share/zoneinfo/$TZ /etc/localtime \
    && echo $TZ > /etc/timezone

# 暴露端口
EXPOSE 80 443

# 设置健康检查
HEALTHCHECK --interval=30s --timeout=3s \
  CMD curl -f http://localhost/ || exit 1

# 启动Nginx
CMD ["nginx", "-g", "daemon off;"] 