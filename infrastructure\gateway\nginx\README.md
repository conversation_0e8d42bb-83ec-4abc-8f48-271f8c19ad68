# Nginx网关服务

本目录包含Nginx网关服务的所有配置文件和相关资源。Nginx作为网关服务，负责接收外部请求并正确路由到内部服务。

## 目录结构

```
infrastructure/gateway/nginx/
├── config/                   # Nginx配置文件目录
│   ├── nginx.conf            # Nginx主配置文件
│   ├── conf.d/               # 网站配置目录
│   │   └── cloudkey.conf     # 云密钥系统配置
│   └── ssl/                  # SSL证书目录
│       ├── nginx.cer         # SSL证书
│       └── nginx_cert.key    # SSL私钥
├── logs/                     # 日志目录（挂载点）
├── backups/                  # 配置备份目录（自动创建）
├── scripts/                  # 运维脚本
│   ├── nginx-deploy.sh       # 部署脚本
│   ├── nginx-control.sh      # 服务控制脚本（启停/重启/状态）
│   └── nginx-backup.sh       # 配置备份脚本
└── docker-compose.yml        # Docker Compose编排文件
```

## 功能特性

- 基于docker.zysproxy.online/nginx:stable-otel镜像
- 支持SSL/TLS加密通信
- 集成OpenTelemetry监控
- 支持跨域资源共享(CORS)
- 加载均衡和高可用配置
- 完善的日志和监控

## 配置说明

### 主要配置文件

- `nginx.conf`: Nginx主配置文件，包含全局设置
- `conf.d/cloudkey.conf`: 云密钥系统的虚拟主机配置，包含路由和代理规则

### SSL证书

- 证书文件存放在`config/ssl/`目录下
- 证书包括：`nginx.cer`（证书文件）和`nginx_cert.key`（私钥文件）

## 使用说明

### 部署服务

```bash
cd scripts
./nginx-deploy.sh
```

### 控制服务

使用`nginx-control.sh`脚本进行日常操作：

```bash
# 启动服务
./nginx-control.sh start

# 停止服务
./nginx-control.sh stop

# 重启服务
./nginx-control.sh restart

# 查看状态
./nginx-control.sh status

# 卸载服务
./nginx-control.sh undeploy
```

### 备份配置

使用`nginx-backup.sh`脚本进行配置备份和恢复：

```bash
# 创建备份
./nginx-backup.sh backup

# 列出所有备份
./nginx-backup.sh list

# 恢复备份
./nginx-backup.sh restore <备份ID>
```

## 性能优化

- 资源限制：CPU使用率上限1核，内存上限512MB
- 健康检查：每30秒检查一次配置有效性
- 日志轮转：自动轮转日志，保留最近3个文件，每个最大50MB

## 注意事项

1. 修改配置文件后，需要重启服务才能生效：`./nginx-control.sh restart`
2. 定期备份配置：`./nginx-backup.sh backup`
3. 配置修改前建议先测试：`docker exec gateway-nginx nginx -t`
4. 保持SSL证书有效并定期更新

## 故障排查

如果服务无法启动或运行异常，请检查：

1. 查看容器状态：`docker ps -a | grep gateway-nginx`
2. 检查日志：`docker logs gateway-nginx`
3. 验证配置：`docker exec gateway-nginx nginx -t`
4. 检查端口占用：`netstat -tuln | grep 6443` 