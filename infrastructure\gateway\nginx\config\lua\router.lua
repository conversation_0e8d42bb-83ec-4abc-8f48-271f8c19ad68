-- 路由选择脚本
local healthcheck = package.loaded.my_checker

-- 如果healthcheck模块未初始化，不做任何操作
if not healthcheck then
    ngx.log(ngx.ERR, "健康检查模块未初始化")
    return
end

-- 获取所有健康的服务器
local hosts = {}
for _, peer in ipairs(healthcheck.get_all_peers()) do
    if peer.healthy then
        table.insert(hosts, {peer.ip, peer.port})
    end
end

-- 如果没有健康的服务器，使用默认的后端
if #hosts == 0 then
    ngx.log(ngx.WARN, "没有健康的服务器可用")
    return
end

-- 从健康服务器中随机选择一个
local target = hosts[math.random(#hosts)]
ngx.ctx.target_host = target[1]
ngx.ctx.target_port = target[2]

ngx.log(ngx.INFO, "选择服务器: ", target[1], ":", target[2]) 